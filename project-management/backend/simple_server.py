#!/usr/bin/env python3
"""
简化版医疗项目管理系统后端服务器
用于演示前端功能，不依赖复杂的第三方库
"""

import json
import http.server
import socketserver
from urllib.parse import urlparse, parse_qs
import threading
import time
import uuid
from datetime import datetime

# 全局数据存储
projects_data = [
    {
        'id': 1,
        'name': '某三甲医院HIS系统升级项目',
        'description': '医院信息系统全面升级改造',
        'status': 'in_progress',
        'progress': 65,
        'startDate': '2024-01-15',
        'endDate': '2024-06-30',
        'manager': '张医生',
        'hospital': '某三甲医院',
        'code': 'HIS-2024-001',
        'createdAt': '2024-01-15T09:00:00Z'
    },
    {
        'id': 2,
        'name': '医疗设备管理系统实施',
        'description': '新一代医疗设备管理系统部署',
        'status': 'planning',
        'progress': 25,
        'startDate': '2024-02-01',
        'endDate': '2024-08-15',
        'manager': '李工程师',
        'hospital': '市人民医院',
        'code': 'EMS-2024-002',
        'createdAt': '2024-02-01T09:00:00Z'
    }
]

tasks_data = []
risks_data = []
meetings_data = []

class MedicalProjectHandler(http.server.BaseHTTPRequestHandler):
    """处理HTTP请求的处理器"""

    def do_OPTIONS(self):
        """处理CORS预检请求"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()

    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path

        # 设置CORS头
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()

        # 路由处理
        if path == '/api/auth/me':
            response = {
                'user': {
                    'id': 1,
                    'username': 'admin',
                    'name': '系统管理员',
                    'email': '<EMAIL>',
                    'role': 'admin'
                }
            }
        elif path == '/api/projects':
            response = {
                'success': True,
                'data': projects_data,
                'message': '获取项目列表成功'
            }
        elif path.startswith('/api/projects/') and '/tasks' in path:
            # 获取项目任务
            project_id = int(path.split('/')[3])
            project_tasks = [task for task in tasks_data if task.get('projectId') == project_id]
            response = {
                'success': True,
                'data': project_tasks,
                'message': '获取项目任务成功'
            }
        elif path.startswith('/api/projects/') and '/risks' in path:
            # 获取项目风险
            project_id = int(path.split('/')[3])
            project_risks = [risk for risk in risks_data if risk.get('projectId') == project_id]
            response = {
                'success': True,
                'data': project_risks,
                'message': '获取项目风险成功'
            }
        elif path.startswith('/api/projects/') and '/meetings' in path:
            # 获取项目会议
            project_id = int(path.split('/')[3])
            project_meetings = [meeting for meeting in meetings_data if meeting.get('projectId') == project_id]
            response = {
                'success': True,
                'data': project_meetings,
                'message': '获取项目会议成功'
            }
        elif path.startswith('/api/projects/') and path.endswith('/chat/messages'):
            # 聊天消息
            response = {
                'messages': [
                    {
                        'id': 1,
                        'user': '张医生',
                        'message': '项目进展如何？',
                        'timestamp': '2024-01-20 10:30:00',
                        'type': 'text'
                    },
                    {
                        'id': 2,
                        'user': '李工程师',
                        'message': '目前已完成65%，预计按时完成',
                        'timestamp': '2024-01-20 10:35:00',
                        'type': 'text'
                    }
                ]
            }
        else:
            response = {'message': '接口正常', 'status': 'ok'}

        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

    def do_POST(self):
        """处理POST请求"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)

        # 设置CORS头
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()

        parsed_path = urlparse(self.path)
        path = parsed_path.path

        if path == '/api/auth/login':
            # 模拟登录
            response = {
                'token': 'demo_token_12345',
                'user': {
                    'id': 1,
                    'username': 'admin',
                    'name': '系统管理员',
                    'email': '<EMAIL>',
                    'role': 'admin'
                }
            }
        elif path == '/api/projects':
            # 创建新项目
            try:
                project_data = json.loads(post_data.decode('utf-8'))

                # 生成新的项目ID
                new_id = max([p['id'] for p in projects_data], default=0) + 1

                # 创建新项目
                new_project = {
                    'id': new_id,
                    'name': project_data.get('name', ''),
                    'description': project_data.get('description', ''),
                    'status': project_data.get('status', 'planning'),
                    'progress': 0,
                    'startDate': project_data.get('startDate', ''),
                    'endDate': project_data.get('endDate', ''),
                    'manager': project_data.get('manager', ''),
                    'hospital': project_data.get('hospital', ''),
                    'code': project_data.get('code', f'PRJ-{new_id:03d}'),
                    'createdAt': datetime.now().isoformat() + 'Z'
                }

                # 添加到项目列表
                projects_data.append(new_project)

                response = {
                    'success': True,
                    'data': new_project,
                    'message': '项目创建成功'
                }

                print(f"新项目已创建: {new_project['name']} (ID: {new_id})")

            except Exception as e:
                response = {
                    'success': False,
                    'message': f'创建项目失败: {str(e)}'
                }
        elif path.startswith('/api/projects/') and path.endswith('/chat/send'):
            # 发送聊天消息
            response = {
                'message': {
                    'id': int(time.time()),
                    'user': '当前用户',
                    'message': '消息已发送',
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'type': 'text'
                }
            }
        else:
            response = {'message': '请求已处理', 'status': 'ok'}

        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

def start_server():
    """启动服务器"""
    PORT = 8090

    with socketserver.TCPServer(("", PORT), MedicalProjectHandler) as httpd:
        print(f"=================================")
        print(f"医疗项目管理系统后端服务启动成功!")
        print(f"服务地址: http://localhost:{PORT}")
        print(f"API文档: http://localhost:{PORT}/api")
        print(f"=================================")
        print(f"按 Ctrl+C 停止服务")

        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n正在停止服务...")
            httpd.shutdown()

if __name__ == "__main__":
    start_server()
