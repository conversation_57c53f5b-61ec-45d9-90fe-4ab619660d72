/**
 * 医疗项目管理系统 - Google风格主题样式
 * Healthcare Project Management System - Google Style Theme
 */

/* 导入Google字体 */
@import url('https://fonts.googleapis.com/css2?family=Google+Sans:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap');

/* CSS变量定义 - 医疗主题色彩系统 */
:root {
  /* 医疗主题色彩 - Medical Theme Colors */
  --medical-primary: #1565C0;        /* 医疗蓝 - Medical Blue */
  --medical-primary-light: #42A5F5;  /* 浅医疗蓝 */
  --medical-primary-dark: #0D47A1;   /* 深医疗蓝 */
  
  --medical-secondary: #2E7D32;      /* 医疗绿 - Medical Green */
  --medical-secondary-light: #66BB6A; /* 浅医疗绿 */
  --medical-secondary-dark: #1B5E20;  /* 深医疗绿 */
  
  --medical-accent: #E53935;         /* 医疗红 - Medical Red */
  --medical-accent-light: #EF5350;   /* 浅医疗红 */
  --medical-accent-dark: #C62828;    /* 深医疗红 */
  
  --medical-warning: #F57C00;        /* 医疗橙 - Medical Orange */
  --medical-warning-light: #FFA726;  /* 浅医疗橙 */
  --medical-warning-dark: #E65100;   /* 深医疗橙 */
  
  --medical-info: #1976D2;           /* 信息蓝 - Info Blue */
  --medical-info-light: #42A5F5;     /* 浅信息蓝 */
  --medical-info-dark: #1565C0;      /* 深信息蓝 */
  
  --medical-success: #388E3C;        /* 成功绿 - Success Green */
  --medical-success-light: #66BB6A;  /* 浅成功绿 */
  --medical-success-dark: #2E7D32;   /* 深成功绿 */
  
  --medical-error: #D32F2F;          /* 错误红 - Error Red */
  --medical-error-light: #EF5350;    /* 浅错误红 */
  --medical-error-dark: #C62828;     /* 深错误红 */
  
  /* Google Material Design 色彩 */
  --google-blue: #4285F4;
  --google-red: #EA4335;
  --google-yellow: #FBBC04;
  --google-green: #34A853;
  
  /* 中性色彩系统 - Neutral Color System */
  --google-grey-50: #FAFAFA;
  --google-grey-100: #F5F5F5;
  --google-grey-200: #EEEEEE;
  --google-grey-300: #E0E0E0;
  --google-grey-400: #BDBDBD;
  --google-grey-500: #9E9E9E;
  --google-grey-600: #757575;
  --google-grey-700: #616161;
  --google-grey-800: #424242;
  --google-grey-900: #212121;
  
  /* 阴影系统 - Shadow System */
  --medical-shadow-1: 0 1px 2px 0 rgba(21, 101, 192, 0.1), 0 1px 3px 1px rgba(21, 101, 192, 0.05);
  --medical-shadow-2: 0 1px 2px 0 rgba(21, 101, 192, 0.15), 0 2px 6px 2px rgba(21, 101, 192, 0.1);
  --medical-shadow-3: 0 4px 8px 3px rgba(21, 101, 192, 0.1), 0 1px 3px rgba(21, 101, 192, 0.15);
  --medical-shadow-4: 0 6px 10px 4px rgba(21, 101, 192, 0.1), 0 2px 3px rgba(21, 101, 192, 0.15);
  
  --google-shadow-1: 0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15);
  --google-shadow-2: 0 1px 2px 0 rgba(60,64,67,.3), 0 2px 6px 2px rgba(60,64,67,.15);
  --google-shadow-3: 0 4px 8px 3px rgba(60,64,67,.15), 0 1px 3px rgba(60,64,67,.3);
  --google-shadow-4: 0 6px 10px 4px rgba(60,64,67,.15), 0 2px 3px rgba(60,64,67,.3);
  
  /* 圆角系统 - Border Radius System */
  --medical-radius-xs: 2px;
  --medical-radius-sm: 4px;
  --medical-radius-md: 8px;
  --medical-radius-lg: 12px;
  --medical-radius-xl: 16px;
  --medical-radius-2xl: 20px;
  --medical-radius-3xl: 24px;
  --medical-radius-full: 9999px;
  
  /* 间距系统 - Spacing System */
  --medical-space-1: 4px;
  --medical-space-2: 8px;
  --medical-space-3: 12px;
  --medical-space-4: 16px;
  --medical-space-5: 20px;
  --medical-space-6: 24px;
  --medical-space-8: 32px;
  --medical-space-10: 40px;
  --medical-space-12: 48px;
  --medical-space-16: 64px;
  --medical-space-20: 80px;
  --medical-space-24: 96px;
  
  /* 字体系统 - Typography System */
  --medical-font-family: 'Google Sans', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --medical-font-mono: 'Roboto Mono', 'Consolas', 'Monaco', monospace;
  
  /* 字体大小 */
  --medical-text-xs: 0.75rem;    /* 12px */
  --medical-text-sm: 0.875rem;   /* 14px */
  --medical-text-base: 1rem;     /* 16px */
  --medical-text-lg: 1.125rem;   /* 18px */
  --medical-text-xl: 1.25rem;    /* 20px */
  --medical-text-2xl: 1.5rem;    /* 24px */
  --medical-text-3xl: 1.875rem;  /* 30px */
  --medical-text-4xl: 2.25rem;   /* 36px */
  --medical-text-5xl: 3rem;      /* 48px */
  
  /* 行高 */
  --medical-leading-tight: 1.25;
  --medical-leading-normal: 1.5;
  --medical-leading-relaxed: 1.75;
  
  /* 字重 */
  --medical-font-light: 300;
  --medical-font-normal: 400;
  --medical-font-medium: 500;
  --medical-font-semibold: 600;
  --medical-font-bold: 700;
  
  /* 过渡动画 - Transitions */
  --medical-transition-fast: 0.15s ease-out;
  --medical-transition-normal: 0.2s ease-out;
  --medical-transition-slow: 0.3s ease-out;
  
  /* Z-index 层级 */
  --medical-z-dropdown: 1000;
  --medical-z-sticky: 1020;
  --medical-z-fixed: 1030;
  --medical-z-modal-backdrop: 1040;
  --medical-z-modal: 1050;
  --medical-z-popover: 1060;
  --medical-z-tooltip: 1070;
}

/* 全局重置和基础样式 */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--medical-font-family);
  font-weight: var(--medical-font-normal);
  color: var(--google-grey-800);
  background-color: var(--google-grey-50);
  margin: 0;
  padding: 0;
}

/* 医疗主题工具类 - Medical Theme Utility Classes */

/* 背景色 */
.bg-medical-primary { background-color: var(--medical-primary) !important; }
.bg-medical-secondary { background-color: var(--medical-secondary) !important; }
.bg-medical-accent { background-color: var(--medical-accent) !important; }
.bg-medical-warning { background-color: var(--medical-warning) !important; }
.bg-medical-info { background-color: var(--medical-info) !important; }
.bg-medical-success { background-color: var(--medical-success) !important; }
.bg-medical-error { background-color: var(--medical-error) !important; }

/* 文字颜色 */
.text-medical-primary { color: var(--medical-primary) !important; }
.text-medical-secondary { color: var(--medical-secondary) !important; }
.text-medical-accent { color: var(--medical-accent) !important; }
.text-medical-warning { color: var(--medical-warning) !important; }
.text-medical-info { color: var(--medical-info) !important; }
.text-medical-success { color: var(--medical-success) !important; }
.text-medical-error { color: var(--medical-error) !important; }

/* 边框颜色 */
.border-medical-primary { border-color: var(--medical-primary) !important; }
.border-medical-secondary { border-color: var(--medical-secondary) !important; }
.border-medical-accent { border-color: var(--medical-accent) !important; }

/* 医疗主题渐变 */
.medical-gradient-primary {
  background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-info) 100%);
}

.medical-gradient-secondary {
  background: linear-gradient(135deg, var(--medical-secondary) 0%, var(--medical-success) 100%);
}

.medical-gradient-accent {
  background: linear-gradient(135deg, var(--medical-accent) 0%, var(--medical-error) 100%);
}

/* 医疗主题文字渐变 */
.medical-gradient-text {
  background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-info) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: var(--medical-font-semibold);
}

/* 医疗主题阴影 */
.medical-shadow-sm { box-shadow: var(--medical-shadow-1); }
.medical-shadow { box-shadow: var(--medical-shadow-2); }
.medical-shadow-md { box-shadow: var(--medical-shadow-3); }
.medical-shadow-lg { box-shadow: var(--medical-shadow-4); }

/* 医疗主题圆角 */
.medical-rounded-sm { border-radius: var(--medical-radius-sm); }
.medical-rounded { border-radius: var(--medical-radius-md); }
.medical-rounded-lg { border-radius: var(--medical-radius-lg); }
.medical-rounded-xl { border-radius: var(--medical-radius-xl); }
.medical-rounded-full { border-radius: var(--medical-radius-full); }

/* 医疗主题间距 */
.medical-p-1 { padding: var(--medical-space-1); }
.medical-p-2 { padding: var(--medical-space-2); }
.medical-p-3 { padding: var(--medical-space-3); }
.medical-p-4 { padding: var(--medical-space-4); }
.medical-p-6 { padding: var(--medical-space-6); }
.medical-p-8 { padding: var(--medical-space-8); }

.medical-m-1 { margin: var(--medical-space-1); }
.medical-m-2 { margin: var(--medical-space-2); }
.medical-m-3 { margin: var(--medical-space-3); }
.medical-m-4 { margin: var(--medical-space-4); }
.medical-m-6 { margin: var(--medical-space-6); }
.medical-m-8 { margin: var(--medical-space-8); }

/* 医疗主题字体 */
.medical-text-xs { font-size: var(--medical-text-xs); }
.medical-text-sm { font-size: var(--medical-text-sm); }
.medical-text-base { font-size: var(--medical-text-base); }
.medical-text-lg { font-size: var(--medical-text-lg); }
.medical-text-xl { font-size: var(--medical-text-xl); }
.medical-text-2xl { font-size: var(--medical-text-2xl); }
.medical-text-3xl { font-size: var(--medical-text-3xl); }

.medical-font-light { font-weight: var(--medical-font-light); }
.medical-font-normal { font-weight: var(--medical-font-normal); }
.medical-font-medium { font-weight: var(--medical-font-medium); }
.medical-font-semibold { font-weight: var(--medical-font-semibold); }
.medical-font-bold { font-weight: var(--medical-font-bold); }

/* 医疗主题过渡动画 */
.medical-transition { transition: all var(--medical-transition-normal); }
.medical-transition-fast { transition: all var(--medical-transition-fast); }
.medical-transition-slow { transition: all var(--medical-transition-slow); }

/* 医疗主题悬停效果 */
.medical-hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--medical-shadow-3);
}

.medical-hover-scale:hover {
  transform: scale(1.02);
}

.medical-hover-glow:hover {
  box-shadow: 0 0 20px rgba(21, 101, 192, 0.3);
}

/* 医疗主题状态指示器 */
.medical-status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: var(--medical-radius-full);
  font-size: var(--medical-text-xs);
  font-weight: var(--medical-font-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.medical-status-indicator.active {
  background-color: var(--medical-success);
  color: white;
}

.medical-status-indicator.pending {
  background-color: var(--medical-warning);
  color: white;
}

.medical-status-indicator.inactive {
  background-color: var(--google-grey-400);
  color: white;
}

.medical-status-indicator.error {
  background-color: var(--medical-error);
  color: white;
}

/* 医疗主题加载动画 */
@keyframes medical-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes medical-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes medical-bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.medical-animate-pulse {
  animation: medical-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.medical-animate-spin {
  animation: medical-spin 1s linear infinite;
}

.medical-animate-bounce {
  animation: medical-bounce 1s infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :root {
    --medical-text-xs: 0.7rem;
    --medical-text-sm: 0.8rem;
    --medical-text-base: 0.9rem;
    --medical-text-lg: 1rem;
    --medical-text-xl: 1.125rem;
    --medical-text-2xl: 1.25rem;
    --medical-text-3xl: 1.5rem;
  }
  
  .medical-p-4 { padding: var(--medical-space-3); }
  .medical-p-6 { padding: var(--medical-space-4); }
  .medical-p-8 { padding: var(--medical-space-6); }
}

/* 打印样式 */
@media print {
  .medical-no-print {
    display: none !important;
  }
  
  .medical-print-only {
    display: block !important;
  }
}
