<template>
  <v-container fluid>
    <v-row>
      <!-- 工作流概览 -->
      <v-col cols="12">
        <v-card>
          <v-card-title class="d-flex align-center">
            <v-icon icon="mdi-chart-timeline-variant" class="me-2"></v-icon>
            工作流自动化控制台
            <v-spacer></v-spacer>
            <v-switch
              v-model="automationEnabled"
              :label="automationEnabled ? '自动化已启用' : '自动化已禁用'"
              color="success"
              @change="toggleAutomation"
            ></v-switch>
          </v-card-title>
        </v-card>
      </v-col>

      <!-- 工作流指标 -->
      <v-col cols="12" md="3">
        <v-card>
          <v-card-text class="text-center">
            <v-icon icon="mdi-speedometer" size="48" color="primary" class="mb-2"></v-icon>
            <div class="text-h4 font-weight-bold">{{ metrics.taskProcessingSpeed.toFixed(1) }}</div>
            <div class="text-caption text-medium-emphasis">任务/天</div>
            <div class="text-body-2 mt-2">处理速度</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="3">
        <v-card>
          <v-card-text class="text-center">
            <v-icon icon="mdi-clock-outline" size="48" color="info" class="mb-2"></v-icon>
            <div class="text-h4 font-weight-bold">{{ metrics.avgResponseTime.toFixed(1) }}</div>
            <div class="text-caption text-medium-emphasis">小时</div>
            <div class="text-body-2 mt-2">平均响应时间</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="3">
        <v-card>
          <v-card-text class="text-center">
            <v-icon icon="mdi-robot" size="48" color="success" class="mb-2"></v-icon>
            <div class="text-h4 font-weight-bold">{{ metrics.automationRate }}%</div>
            <div class="text-caption text-medium-emphasis">自动化率</div>
            <div class="text-body-2 mt-2">流程自动化</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="3">
        <v-card>
          <v-card-text class="text-center">
            <v-icon icon="mdi-chart-line" size="48" color="warning" class="mb-2"></v-icon>
            <div class="text-h4 font-weight-bold">{{ metrics.efficiency }}%</div>
            <div class="text-caption text-medium-emphasis">整体效率</div>
            <div class="text-body-2 mt-2">工作流效率</div>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- 自动化规则管理 -->
      <v-col cols="12" md="6">
        <v-card>
          <v-card-title>
            自动化规则
            <v-spacer></v-spacer>
            <v-btn
              color="primary"
              size="small"
              prepend-icon="mdi-plus"
              @click="showCreateRuleDialog = true"
            >
              新建规则
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-list density="compact">
              <v-list-item
                v-for="rule in activeRules"
                :key="rule.id"
                :title="rule.name"
                :subtitle="rule.description"
              >
                <template v-slot:prepend>
                  <v-icon
                    :icon="getRuleIcon(rule.type)"
                    :color="rule.isActive ? 'success' : 'grey'"
                  ></v-icon>
                </template>
                <template v-slot:append>
                  <v-switch
                    :model-value="rule.isActive"
                    density="compact"
                    @change="toggleRule(rule.id, $event)"
                  ></v-switch>
                </template>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- 优化建议 -->
      <v-col cols="12" md="6">
        <v-card>
          <v-card-title>优化建议</v-card-title>
          <v-card-text>
            <v-list density="compact">
              <v-list-item
                v-for="suggestion in optimizationSuggestions"
                :key="suggestion.id"
                :title="suggestion.title"
                :subtitle="suggestion.description"
                @click="applySuggestion(suggestion)"
              >
                <template v-slot:prepend>
                  <v-icon :icon="suggestion.icon" color="primary"></v-icon>
                </template>
                <template v-slot:append>
                  <v-chip
                    :color="getImpactColor(suggestion.impact)"
                    size="small"
                  >
                    {{ getImpactText(suggestion.impact) }}
                  </v-chip>
                </template>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- 工作流模板 -->
      <v-col cols="12">
        <v-card>
          <v-card-title>工作流模板</v-card-title>
          <v-card-text>
            <v-row>
              <v-col
                v-for="template in workflowTemplates"
                :key="template.id"
                cols="12"
                md="4"
              >
                <v-card
                  variant="outlined"
                  class="template-card"
                  @click="applyTemplate(template)"
                >
                  <v-card-text class="text-center">
                    <v-icon
                      :icon="template.icon"
                      size="48"
                      :color="template.color"
                      class="mb-2"
                    ></v-icon>
                    <div class="text-h6 mb-1">{{ template.name }}</div>
                    <div class="text-caption text-medium-emphasis">{{ template.description }}</div>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- 自动化日志 -->
      <v-col cols="12">
        <v-card>
          <v-card-title>自动化日志</v-card-title>
          <v-card-text>
            <v-data-table
              :headers="logHeaders"
              :items="automationLogs"
              :items-per-page="10"
              density="compact"
            >
              <template v-slot:item.timestamp="{ item }">
                {{ formatDateTime(item.timestamp) }}
              </template>
              <template v-slot:item.type="{ item }">
                <v-chip
                  :color="getLogTypeColor(item.type)"
                  size="small"
                >
                  {{ getLogTypeText(item.type) }}
                </v-chip>
              </template>
            </v-data-table>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 创建规则对话框 -->
    <v-dialog v-model="showCreateRuleDialog" max-width="600">
      <v-card>
        <v-card-title>创建自动化规则</v-card-title>
        <v-card-text>
          <v-form ref="ruleForm" v-model="ruleFormValid">
            <v-text-field
              v-model="newRule.name"
              label="规则名称"
              :rules="[v => !!v || '规则名称不能为空']"
              variant="outlined"
              required
            ></v-text-field>
            
            <v-textarea
              v-model="newRule.description"
              label="规则描述"
              variant="outlined"
              rows="3"
            ></v-textarea>
            
            <v-select
              v-model="newRule.type"
              :items="ruleTypes"
              label="规则类型"
              variant="outlined"
              required
            ></v-select>
            
            <v-select
              v-model="newRule.trigger"
              :items="triggerTypes"
              label="触发条件"
              variant="outlined"
              required
            ></v-select>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="grey-darken-1"
            variant="text"
            @click="showCreateRuleDialog = false"
          >
            取消
          </v-btn>
          <v-btn
            color="primary"
            variant="text"
            :disabled="!ruleFormValid"
            @click="createRule"
          >
            创建
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex'

export default {
  name: 'WorkflowDashboard',
  data() {
    return {
      showCreateRuleDialog: false,
      ruleFormValid: false,
      newRule: {
        name: '',
        description: '',
        type: '',
        trigger: ''
      },
      
      ruleTypes: [
        { title: '任务自动分配', value: 'task_auto_assignment' },
        { title: '状态自动流转', value: 'status_auto_transition' },
        { title: '截止日期提醒', value: 'deadline_reminder' },
        { title: '风险自动创建', value: 'risk_auto_creation' }
      ],
      
      triggerTypes: [
        { title: '任务创建时', value: 'task_created' },
        { title: '任务完成时', value: 'task_completed' },
        { title: '任务延期时', value: 'task_overdue' },
        { title: '定时触发', value: 'time_based' }
      ],
      
      logHeaders: [
        { title: '时间', key: 'timestamp', width: '180px' },
        { title: '类型', key: 'type', width: '120px' },
        { title: '消息', key: 'message' }
      ]
    }
  },
  
  computed: {
    ...mapState('workflow', ['metrics', 'isAutomationEnabled']),
    ...mapGetters('workflow', [
      'getActiveRules',
      'getOptimizationSuggestions',
      'getAvailableTemplates',
      'getAutomationLogs'
    ]),
    
    automationEnabled: {
      get() {
        return this.isAutomationEnabled
      },
      set(value) {
        // 通过action更新状态
      }
    },
    
    activeRules() {
      return this.getActiveRules
    },
    
    optimizationSuggestions() {
      return this.getOptimizationSuggestions
    },
    
    workflowTemplates() {
      return this.getAvailableTemplates
    },
    
    automationLogs() {
      return this.getAutomationLogs
    }
  },
  
  async mounted() {
    await this.initializeWorkflow()
  },
  
  methods: {
    ...mapActions('workflow', [
      'initializeWorkflow',
      'toggleAutomationRule',
      'createAutomationRule',
      'applyOptimizationSuggestion',
      'applyWorkflowTemplate',
      'startAutomationMonitoring',
      'stopAutomationMonitoring'
    ]),
    
    async toggleAutomation(enabled) {
      if (enabled) {
        await this.startAutomationMonitoring()
      } else {
        await this.stopAutomationMonitoring()
      }
    },
    
    async toggleRule(ruleId, enabled) {
      await this.toggleAutomationRule({ ruleId, enabled })
    },
    
    async createRule() {
      if (this.ruleFormValid) {
        await this.createAutomationRule(this.newRule)
        this.showCreateRuleDialog = false
        this.resetRuleForm()
      }
    },
    
    async applySuggestion(suggestion) {
      await this.applyOptimizationSuggestion(suggestion)
    },
    
    async applyTemplate(template) {
      // 这里需要选择项目
      const projectId = 1 // 示例项目ID
      await this.applyWorkflowTemplate({ projectId, templateId: template.id })
    },
    
    resetRuleForm() {
      this.newRule = {
        name: '',
        description: '',
        type: '',
        trigger: ''
      }
    },
    
    getRuleIcon(type) {
      const icons = {
        'task_auto_assignment': 'mdi-account-multiple-plus',
        'status_auto_transition': 'mdi-arrow-right-circle',
        'deadline_reminder': 'mdi-bell-ring',
        'risk_auto_creation': 'mdi-alert-circle'
      }
      return icons[type] || 'mdi-cog'
    },
    
    getImpactColor(impact) {
      const colors = {
        'high': 'success',
        'medium': 'warning',
        'low': 'info'
      }
      return colors[impact] || 'grey'
    },
    
    getImpactText(impact) {
      const texts = {
        'high': '高效果',
        'medium': '中效果',
        'low': '低效果'
      }
      return texts[impact] || '未知'
    },
    
    getLogTypeColor(type) {
      const colors = {
        'rule_created': 'success',
        'rule_toggled': 'info',
        'template_applied': 'primary',
        'workflow_analyzed': 'warning',
        'suggestion_applied': 'success'
      }
      return colors[type] || 'grey'
    },
    
    getLogTypeText(type) {
      const texts = {
        'rule_created': '规则创建',
        'rule_toggled': '规则切换',
        'template_applied': '模板应用',
        'workflow_analyzed': '工作流分析',
        'suggestion_applied': '建议应用'
      }
      return texts[type] || '未知操作'
    },
    
    formatDateTime(timestamp) {
      return new Date(timestamp).toLocaleString()
    }
  }
}
</script>

<style scoped>
.template-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style>
