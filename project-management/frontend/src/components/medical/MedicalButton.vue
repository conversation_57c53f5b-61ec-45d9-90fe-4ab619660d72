<template>
  <v-btn
    :class="[
      'medical-btn',
      `medical-btn--${variant}`,
      `medical-btn--${size}`,
      { 'medical-btn--loading': loading },
      { 'medical-btn--icon-only': iconOnly }
    ]"
    :color="buttonColor"
    :variant="buttonVariant"
    :size="buttonSize"
    :disabled="disabled || loading"
    :prepend-icon="!loading && prependIcon ? prependIcon : undefined"
    :append-icon="!loading && appendIcon ? appendIcon : undefined"
    v-bind="$attrs"
    @click="handleClick"
  >
    <!-- 加载状态 -->
    <template v-if="loading" #prepend>
      <v-progress-circular
        :size="loadingSize"
        :width="2"
        color="currentColor"
        indeterminate
        class="medical-btn-loading"
      ></v-progress-circular>
    </template>
    
    <!-- 按钮内容 -->
    <span v-if="!iconOnly" class="medical-btn-content">
      <slot>{{ text }}</slot>
    </span>
    
    <!-- 仅图标模式 -->
    <v-icon v-if="iconOnly && icon" :icon="icon"></v-icon>
  </v-btn>
</template>

<script>
export default {
  name: 'MedicalButton',
  
  inheritAttrs: false,
  
  props: {
    // 按钮变体
    variant: {
      type: String,
      default: 'primary',
      validator: value => [
        'primary', 'secondary', 'accent', 'success', 'warning', 'error', 'info',
        'outlined', 'text', 'plain'
      ].includes(value)
    },
    
    // 按钮大小
    size: {
      type: String,
      default: 'default',
      validator: value => ['small', 'default', 'large'].includes(value)
    },
    
    // 按钮文本
    text: {
      type: String,
      default: ''
    },
    
    // 图标
    icon: {
      type: String,
      default: ''
    },
    
    prependIcon: {
      type: String,
      default: ''
    },
    
    appendIcon: {
      type: String,
      default: ''
    },
    
    // 仅图标模式
    iconOnly: {
      type: Boolean,
      default: false
    },
    
    // 状态
    loading: {
      type: Boolean,
      default: false
    },
    
    disabled: {
      type: Boolean,
      default: false
    },
    
    // 医疗主题特定属性
    medicalType: {
      type: String,
      default: '',
      validator: value => ['', 'emergency', 'critical', 'routine', 'diagnostic'].includes(value)
    }
  },
  
  computed: {
    buttonColor() {
      // 医疗主题特定颜色
      if (this.medicalType) {
        const medicalColors = {
          'emergency': 'medical-error',
          'critical': 'medical-accent',
          'routine': 'medical-primary',
          'diagnostic': 'medical-info'
        }
        return medicalColors[this.medicalType]
      }
      
      // 标准颜色映射
      const colorMap = {
        'primary': 'medical-primary',
        'secondary': 'medical-secondary',
        'accent': 'medical-accent',
        'success': 'medical-success',
        'warning': 'medical-warning',
        'error': 'medical-error',
        'info': 'medical-info',
        'outlined': 'medical-primary',
        'text': 'medical-primary',
        'plain': 'grey'
      }
      
      return colorMap[this.variant] || 'medical-primary'
    },
    
    buttonVariant() {
      const variantMap = {
        'primary': 'flat',
        'secondary': 'flat',
        'accent': 'flat',
        'success': 'flat',
        'warning': 'flat',
        'error': 'flat',
        'info': 'flat',
        'outlined': 'outlined',
        'text': 'text',
        'plain': 'plain'
      }
      
      return variantMap[this.variant] || 'flat'
    },
    
    buttonSize() {
      const sizeMap = {
        'small': 'small',
        'default': 'default',
        'large': 'large'
      }
      
      return sizeMap[this.size] || 'default'
    },
    
    loadingSize() {
      const sizeMap = {
        'small': 16,
        'default': 20,
        'large': 24
      }
      
      return sizeMap[this.size] || 20
    }
  },
  
  methods: {
    handleClick(event) {
      if (!this.disabled && !this.loading) {
        this.$emit('click', event)
      }
    }
  }
}
</script>

<style scoped>
/* 医疗按钮基础样式 */
.medical-btn {
  font-family: var(--medical-font-family) !important;
  font-weight: var(--medical-font-medium) !important;
  text-transform: none !important;
  letter-spacing: 0.25px !important;
  border-radius: var(--medical-radius-md) !important;
  transition: all var(--medical-transition-normal) !important;
  box-shadow: var(--medical-shadow-1) !important;
}

.medical-btn:hover {
  box-shadow: var(--medical-shadow-2) !important;
  transform: translateY(-1px);
}

.medical-btn:active {
  transform: translateY(0);
  box-shadow: var(--medical-shadow-1) !important;
}

/* 按钮变体样式 */
.medical-btn--primary {
  background: var(--medical-gradient-primary) !important;
  color: white !important;
}

.medical-btn--secondary {
  background: var(--medical-gradient-secondary) !important;
  color: white !important;
}

.medical-btn--accent {
  background: var(--medical-gradient-accent) !important;
  color: white !important;
}

.medical-btn--success {
  background-color: var(--medical-success) !important;
  color: white !important;
}

.medical-btn--warning {
  background-color: var(--medical-warning) !important;
  color: white !important;
}

.medical-btn--error {
  background-color: var(--medical-error) !important;
  color: white !important;
}

.medical-btn--info {
  background-color: var(--medical-info) !important;
  color: white !important;
}

.medical-btn--outlined {
  border: 2px solid var(--medical-primary) !important;
  background-color: transparent !important;
  color: var(--medical-primary) !important;
  box-shadow: none !important;
}

.medical-btn--outlined:hover {
  background-color: rgba(21, 101, 192, 0.08) !important;
  box-shadow: var(--medical-shadow-1) !important;
}

.medical-btn--text {
  background-color: transparent !important;
  color: var(--medical-primary) !important;
  box-shadow: none !important;
}

.medical-btn--text:hover {
  background-color: rgba(21, 101, 192, 0.08) !important;
}

.medical-btn--plain {
  background-color: var(--google-grey-100) !important;
  color: var(--google-grey-700) !important;
  box-shadow: none !important;
}

.medical-btn--plain:hover {
  background-color: var(--google-grey-200) !important;
}

/* 按钮大小 */
.medical-btn--small {
  min-height: 32px !important;
  padding: 0 12px !important;
  font-size: var(--medical-text-sm) !important;
}

.medical-btn--default {
  min-height: 40px !important;
  padding: 0 16px !important;
  font-size: var(--medical-text-base) !important;
}

.medical-btn--large {
  min-height: 48px !important;
  padding: 0 24px !important;
  font-size: var(--medical-text-lg) !important;
}

/* 仅图标按钮 */
.medical-btn--icon-only {
  min-width: auto !important;
  width: auto !important;
  aspect-ratio: 1;
}

.medical-btn--icon-only.medical-btn--small {
  width: 32px !important;
  padding: 0 !important;
}

.medical-btn--icon-only.medical-btn--default {
  width: 40px !important;
  padding: 0 !important;
}

.medical-btn--icon-only.medical-btn--large {
  width: 48px !important;
  padding: 0 !important;
}

/* 加载状态 */
.medical-btn--loading {
  pointer-events: none;
}

.medical-btn-loading {
  margin-right: 8px !important;
}

.medical-btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

/* 医疗主题特定样式 */
.medical-btn[data-medical-type="emergency"] {
  background: linear-gradient(135deg, var(--medical-error) 0%, var(--medical-accent) 100%) !important;
  color: white !important;
  animation: medical-pulse 2s infinite;
}

.medical-btn[data-medical-type="critical"] {
  background: var(--medical-accent) !important;
  color: white !important;
  border: 2px solid var(--medical-error) !important;
}

.medical-btn[data-medical-type="routine"] {
  background: var(--medical-primary) !important;
  color: white !important;
}

.medical-btn[data-medical-type="diagnostic"] {
  background: var(--medical-info) !important;
  color: white !important;
  border-left: 4px solid var(--medical-secondary) !important;
}

/* 禁用状态 */
.medical-btn:disabled {
  background-color: var(--google-grey-300) !important;
  color: var(--google-grey-500) !important;
  box-shadow: none !important;
  transform: none !important;
  cursor: not-allowed;
}

.medical-btn--outlined:disabled {
  border-color: var(--google-grey-300) !important;
  background-color: transparent !important;
}

.medical-btn--text:disabled {
  background-color: transparent !important;
}

/* 焦点状态 */
.medical-btn:focus-visible {
  outline: 2px solid var(--medical-primary);
  outline-offset: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .medical-btn--large {
    min-height: 44px !important;
    padding: 0 20px !important;
    font-size: var(--medical-text-base) !important;
  }
  
  .medical-btn--default {
    min-height: 36px !important;
    padding: 0 14px !important;
    font-size: var(--medical-text-sm) !important;
  }
  
  .medical-btn--small {
    min-height: 28px !important;
    padding: 0 10px !important;
    font-size: var(--medical-text-xs) !important;
  }
}

/* 动画效果 */
@keyframes medical-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(211, 47, 47, 0.7);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(211, 47, 47, 0);
  }
}
</style>
