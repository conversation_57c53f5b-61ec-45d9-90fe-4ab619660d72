<template>
  <v-card 
    :class="[
      'medical-card',
      `medical-card--${variant}`,
      { 'medical-card--elevated': elevated },
      { 'medical-card--interactive': interactive }
    ]"
    :elevation="elevated ? 2 : 1"
    @click="handleClick"
  >
    <!-- 卡片头部 -->
    <div 
      v-if="hasHeader"
      :class="[
        'medical-card-header',
        headerClass
      ]"
    >
      <div class="d-flex align-center">
        <v-icon 
          v-if="icon"
          :icon="icon"
          :color="iconColor"
          :size="iconSize"
          class="me-3"
        ></v-icon>
        
        <div class="flex-grow-1">
          <div v-if="title" class="medical-card-title">
            {{ title }}
          </div>
          <div v-if="subtitle" class="medical-card-subtitle">
            {{ subtitle }}
          </div>
        </div>
        
        <div v-if="hasActions" class="medical-card-actions">
          <slot name="actions"></slot>
        </div>
      </div>
    </div>
    
    <!-- 卡片内容 -->
    <v-card-text 
      v-if="hasContent"
      :class="contentClass"
    >
      <slot></slot>
    </v-card-text>
    
    <!-- 卡片底部 -->
    <v-card-actions 
      v-if="hasFooter"
      :class="footerClass"
    >
      <slot name="footer"></slot>
    </v-card-actions>
    
    <!-- 状态指示器 -->
    <div 
      v-if="status"
      :class="[
        'medical-status-indicator',
        `medical-status-indicator--${status}`
      ]"
    >
      <v-icon 
        :icon="getStatusIcon(status)"
        size="16"
        class="me-1"
      ></v-icon>
      {{ getStatusText(status) }}
    </div>
    
    <!-- 加载状态 -->
    <v-overlay
      v-if="loading"
      :model-value="loading"
      class="medical-card-loading"
      contained
    >
      <v-progress-circular
        color="medical-primary"
        indeterminate
        size="48"
      ></v-progress-circular>
    </v-overlay>
  </v-card>
</template>

<script>
export default {
  name: 'MedicalCard',
  
  props: {
    // 卡片变体
    variant: {
      type: String,
      default: 'default',
      validator: value => ['default', 'primary', 'secondary', 'accent', 'outlined'].includes(value)
    },
    
    // 是否提升
    elevated: {
      type: Boolean,
      default: false
    },
    
    // 是否可交互
    interactive: {
      type: Boolean,
      default: false
    },
    
    // 头部信息
    title: {
      type: String,
      default: ''
    },
    
    subtitle: {
      type: String,
      default: ''
    },
    
    icon: {
      type: String,
      default: ''
    },
    
    iconColor: {
      type: String,
      default: 'medical-primary'
    },
    
    iconSize: {
      type: [String, Number],
      default: 24
    },
    
    // 状态
    status: {
      type: String,
      default: '',
      validator: value => ['', 'active', 'pending', 'completed', 'error', 'warning'].includes(value)
    },
    
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    
    // 样式类
    headerClass: {
      type: String,
      default: ''
    },
    
    contentClass: {
      type: String,
      default: ''
    },
    
    footerClass: {
      type: String,
      default: ''
    }
  },
  
  computed: {
    hasHeader() {
      return this.title || this.subtitle || this.icon || this.$slots.actions
    },
    
    hasContent() {
      return this.$slots.default
    },
    
    hasFooter() {
      return this.$slots.footer
    },
    
    hasActions() {
      return this.$slots.actions
    }
  },
  
  methods: {
    handleClick() {
      if (this.interactive) {
        this.$emit('click')
      }
    },
    
    getStatusIcon(status) {
      const icons = {
        'active': 'mdi-check-circle',
        'pending': 'mdi-clock-outline',
        'completed': 'mdi-check-circle',
        'error': 'mdi-alert-circle',
        'warning': 'mdi-alert'
      }
      return icons[status] || 'mdi-information'
    },
    
    getStatusText(status) {
      const texts = {
        'active': '进行中',
        'pending': '待处理',
        'completed': '已完成',
        'error': '错误',
        'warning': '警告'
      }
      return texts[status] || status
    }
  }
}
</script>

<style scoped>
/* 医疗卡片基础样式 */
.medical-card {
  border-radius: var(--medical-radius-lg) !important;
  border: 1px solid var(--google-grey-200);
  transition: all var(--medical-transition-normal);
  position: relative;
  overflow: hidden;
}

.medical-card:hover {
  border-color: var(--medical-primary);
}

/* 卡片变体 */
.medical-card--primary {
  border-color: var(--medical-primary);
}

.medical-card--primary .medical-card-header {
  background: var(--medical-gradient-primary);
  color: white;
}

.medical-card--secondary {
  border-color: var(--medical-secondary);
}

.medical-card--secondary .medical-card-header {
  background: var(--medical-gradient-secondary);
  color: white;
}

.medical-card--accent {
  border-color: var(--medical-accent);
}

.medical-card--accent .medical-card-header {
  background: var(--medical-gradient-accent);
  color: white;
}

.medical-card--outlined {
  border: 2px solid var(--medical-primary);
  background-color: rgba(21, 101, 192, 0.02);
}

/* 提升效果 */
.medical-card--elevated {
  box-shadow: var(--medical-shadow-3) !important;
}

.medical-card--elevated:hover {
  box-shadow: var(--medical-shadow-4) !important;
  transform: translateY(-2px);
}

/* 交互效果 */
.medical-card--interactive {
  cursor: pointer;
}

.medical-card--interactive:hover {
  transform: translateY(-2px);
  box-shadow: var(--medical-shadow-3) !important;
}

/* 卡片头部 */
.medical-card-header {
  padding: var(--medical-space-4);
  background-color: var(--google-grey-50);
  border-bottom: 1px solid var(--google-grey-200);
}

.medical-card-title {
  font-size: var(--medical-text-lg);
  font-weight: var(--medical-font-semibold);
  line-height: var(--medical-leading-tight);
  margin-bottom: 2px;
}

.medical-card-subtitle {
  font-size: var(--medical-text-sm);
  opacity: 0.8;
  line-height: var(--medical-leading-normal);
}

/* 卡片动作 */
.medical-card-actions {
  display: flex;
  align-items: center;
  gap: var(--medical-space-2);
}

/* 状态指示器 */
.medical-status-indicator {
  position: absolute;
  top: var(--medical-space-3);
  right: var(--medical-space-3);
  display: flex;
  align-items: center;
  padding: var(--medical-space-1) var(--medical-space-2);
  border-radius: var(--medical-radius-full);
  font-size: var(--medical-text-xs);
  font-weight: var(--medical-font-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  z-index: 1;
}

.medical-status-indicator--active {
  background-color: var(--medical-success);
  color: white;
}

.medical-status-indicator--pending {
  background-color: var(--medical-warning);
  color: white;
}

.medical-status-indicator--completed {
  background-color: var(--medical-success);
  color: white;
}

.medical-status-indicator--error {
  background-color: var(--medical-error);
  color: white;
}

.medical-status-indicator--warning {
  background-color: var(--medical-warning);
  color: white;
}

/* 加载状态 */
.medical-card-loading {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .medical-card-header {
    padding: var(--medical-space-3);
  }
  
  .medical-card-title {
    font-size: var(--medical-text-base);
  }
  
  .medical-card-subtitle {
    font-size: var(--medical-text-xs);
  }
  
  .medical-status-indicator {
    position: static;
    margin-top: var(--medical-space-2);
    align-self: flex-start;
  }
}
</style>
