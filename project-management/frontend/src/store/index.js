import { createStore } from 'vuex'
import createPersistedState from 'vuex-persistedstate'
import auth from './modules/auth'
import projects from './modules/projects'
import phases from './modules/phases'
import tasks from './modules/tasks'
import risks from './modules/risks'
import meetings from './modules/meetings'
import users from './modules/users'
import relationships from './modules/relationships'
import dashboard from './modules/dashboard'
import workflow from './modules/workflow'

export default createStore({
  modules: {
    auth,
    projects,
    phases,
    tasks,
    risks,
    meetings,
    users,
    relationships,
    dashboard,
    workflow
  },
  plugins: [
    createPersistedState({
      key: 'project-management-system',
      paths: ['auth.token', 'auth.user']
    })
  ],
  // 全局状态
  state: {
    loading: false,
    error: null,
    success: null,
    notifications: []
  },
  // 用于更改状态的同步函数
  mutations: {
    SET_LOADING(state, isLoading) {
      state.loading = isLoading
    },
    SET_ERROR(state, error) {
      state.error = error
    },
    CLEAR_ERROR(state) {
      state.error = null
    },
    SET_SUCCESS(state, message) {
      state.success = message
    },
    CLEAR_SUCCESS(state) {
      state.success = null
    },
    ADD_NOTIFICATION(state, notification) {
      state.notifications.push(notification)
    },
    REMOVE_NOTIFICATION(state, id) {
      state.notifications = state.notifications.filter(n => n.id !== id)
    },
    CLEAR_NOTIFICATIONS(state) {
      state.notifications = []
    }
  },
  // 用于业务逻辑处理的异步函数
  actions: {
    setLoading({ commit }, isLoading) {
      commit('SET_LOADING', isLoading)
    },
    setError({ commit }, error) {
      commit('SET_ERROR', error)
      // 自动清除错误
      setTimeout(() => {
        commit('CLEAR_ERROR')
      }, 5000)
    },
    clearError({ commit }) {
      commit('CLEAR_ERROR')
    },
    setSuccess({ commit }, message) {
      commit('SET_SUCCESS', message)
      // 自动清除成功消息
      setTimeout(() => {
        commit('CLEAR_SUCCESS')
      }, 5000)
    },
    clearSuccess({ commit }) {
      commit('CLEAR_SUCCESS')
    },
    addNotification({ commit }, notification) {
      const id = Date.now()
      commit('ADD_NOTIFICATION', { ...notification, id })
      // 自动移除通知
      setTimeout(() => {
        commit('REMOVE_NOTIFICATION', id)
      }, notification.timeout || 5000)
      return id
    },
    removeNotification({ commit }, id) {
      commit('REMOVE_NOTIFICATION', id)
    },
    clearNotifications({ commit }) {
      commit('CLEAR_NOTIFICATIONS')
    }
  },
  // 用于从状态派生数据的计算属性
  getters: {
    isLoading: state => state.loading,
    error: state => state.error,
    success: state => state.success,
    notifications: state => state.notifications
  }
})
