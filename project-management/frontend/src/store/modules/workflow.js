/**
 * 工作流自动化状态管理模块
 */

import workflowService from '@/services/workflowService'
import api from '@/services/api'

const state = {
  // 自动化规则
  automationRules: [],
  activeRules: [],
  
  // 工作流模板
  workflowTemplates: [],
  currentTemplate: null,
  
  // 工作流指标
  metrics: {
    taskProcessingSpeed: 0,
    avgResponseTime: 0,
    automationRate: 0,
    errorRate: 0,
    efficiency: 0
  },
  
  // 优化建议
  optimizationSuggestions: [],
  appliedSuggestions: [],
  
  // 工作流状态
  isAutomationEnabled: false,
  isAnalyzing: false,
  lastAnalysisTime: null,
  
  // 通知和提醒
  pendingNotifications: [],
  automationLogs: []
}

const getters = {
  // 获取活跃的自动化规则
  getActiveRules: state => state.activeRules,
  
  // 获取工作流效率
  getWorkflowEfficiency: state => state.metrics.efficiency,
  
  // 获取优化建议
  getOptimizationSuggestions: state => state.optimizationSuggestions,
  
  // 获取可用的工作流模板
  getAvailableTemplates: state => state.workflowTemplates,
  
  // 获取自动化状态
  isAutomationActive: state => state.isAutomationEnabled,
  
  // 获取待处理通知
  getPendingNotifications: state => state.pendingNotifications,
  
  // 获取自动化日志
  getAutomationLogs: state => state.automationLogs.slice(-50) // 最近50条
}

const mutations = {
  // 设置自动化规则
  SET_AUTOMATION_RULES(state, rules) {
    state.automationRules = rules
    state.activeRules = rules.filter(rule => rule.isActive)
  },
  
  // 添加自动化规则
  ADD_AUTOMATION_RULE(state, rule) {
    state.automationRules.push(rule)
    if (rule.isActive) {
      state.activeRules.push(rule)
    }
  },
  
  // 更新自动化规则
  UPDATE_AUTOMATION_RULE(state, { ruleId, updates }) {
    const ruleIndex = state.automationRules.findIndex(rule => rule.id === ruleId)
    if (ruleIndex !== -1) {
      state.automationRules[ruleIndex] = { ...state.automationRules[ruleIndex], ...updates }
      
      // 更新活跃规则列表
      state.activeRules = state.automationRules.filter(rule => rule.isActive)
    }
  },
  
  // 设置工作流模板
  SET_WORKFLOW_TEMPLATES(state, templates) {
    state.workflowTemplates = templates
  },
  
  // 设置当前模板
  SET_CURRENT_TEMPLATE(state, template) {
    state.currentTemplate = template
  },
  
  // 更新工作流指标
  UPDATE_METRICS(state, metrics) {
    state.metrics = { ...state.metrics, ...metrics }
  },
  
  // 设置优化建议
  SET_OPTIMIZATION_SUGGESTIONS(state, suggestions) {
    state.optimizationSuggestions = suggestions
  },
  
  // 添加已应用的建议
  ADD_APPLIED_SUGGESTION(state, suggestion) {
    state.appliedSuggestions.push({
      ...suggestion,
      appliedAt: new Date().toISOString()
    })
  },
  
  // 设置自动化状态
  SET_AUTOMATION_ENABLED(state, enabled) {
    state.isAutomationEnabled = enabled
  },
  
  // 设置分析状态
  SET_ANALYZING(state, analyzing) {
    state.isAnalyzing = analyzing
  },
  
  // 设置最后分析时间
  SET_LAST_ANALYSIS_TIME(state, time) {
    state.lastAnalysisTime = time
  },
  
  // 添加通知
  ADD_NOTIFICATION(state, notification) {
    state.pendingNotifications.push({
      ...notification,
      id: Date.now(),
      createdAt: new Date().toISOString()
    })
  },
  
  // 移除通知
  REMOVE_NOTIFICATION(state, notificationId) {
    state.pendingNotifications = state.pendingNotifications.filter(
      notification => notification.id !== notificationId
    )
  },
  
  // 添加自动化日志
  ADD_AUTOMATION_LOG(state, log) {
    state.automationLogs.push({
      ...log,
      id: Date.now(),
      timestamp: new Date().toISOString()
    })
  }
}

const actions = {
  // 初始化工作流自动化
  async initializeWorkflow({ commit, dispatch }) {
    try {
      // 加载自动化规则
      await dispatch('loadAutomationRules')
      
      // 加载工作流模板
      await dispatch('loadWorkflowTemplates')
      
      // 启动自动化监控
      dispatch('startAutomationMonitoring')
      
    } catch (error) {
      console.error('初始化工作流失败:', error)
    }
  },
  
  // 加载自动化规则
  async loadAutomationRules({ commit }) {
    try {
      const response = await api.get('/workflow/automation-rules')
      commit('SET_AUTOMATION_RULES', response.data)
    } catch (error) {
      console.error('加载自动化规则失败:', error)
    }
  },
  
  // 创建自动化规则
  async createAutomationRule({ commit }, ruleData) {
    try {
      const response = await api.post('/workflow/automation-rules', ruleData)
      commit('ADD_AUTOMATION_RULE', response.data)
      
      // 记录日志
      commit('ADD_AUTOMATION_LOG', {
        type: 'rule_created',
        message: `创建自动化规则: ${ruleData.name}`,
        data: ruleData
      })
      
      return response.data
    } catch (error) {
      console.error('创建自动化规则失败:', error)
      throw error
    }
  },
  
  // 启用/禁用自动化规则
  async toggleAutomationRule({ commit }, { ruleId, enabled }) {
    try {
      const response = await api.put(`/workflow/automation-rules/${ruleId}`, { isActive: enabled })
      commit('UPDATE_AUTOMATION_RULE', { ruleId, updates: { isActive: enabled } })
      
      commit('ADD_AUTOMATION_LOG', {
        type: 'rule_toggled',
        message: `${enabled ? '启用' : '禁用'}自动化规则: ${ruleId}`,
        data: { ruleId, enabled }
      })
      
      return response.data
    } catch (error) {
      console.error('切换自动化规则状态失败:', error)
      throw error
    }
  },
  
  // 加载工作流模板
  async loadWorkflowTemplates({ commit }) {
    try {
      const response = await api.get('/workflow/templates')
      commit('SET_WORKFLOW_TEMPLATES', response.data)
    } catch (error) {
      console.error('加载工作流模板失败:', error)
    }
  },
  
  // 应用工作流模板
  async applyWorkflowTemplate({ commit, dispatch }, { projectId, templateId }) {
    try {
      const result = await workflowService.applyWorkflowTemplate(projectId, templateId)
      
      if (result.success) {
        commit('ADD_AUTOMATION_LOG', {
          type: 'template_applied',
          message: `应用工作流模板到项目 ${projectId}`,
          data: { projectId, templateId }
        })
        
        // 重新分析工作流
        await dispatch('analyzeWorkflow', projectId)
      }
      
      return result
    } catch (error) {
      console.error('应用工作流模板失败:', error)
      throw error
    }
  },
  
  // 分析工作流效率
  async analyzeWorkflow({ commit }, projectId) {
    commit('SET_ANALYZING', true)
    
    try {
      const analysis = await workflowService.analyzeWorkflowEfficiency(projectId)
      
      // 更新指标
      commit('UPDATE_METRICS', {
        taskProcessingSpeed: analysis.taskMetrics.avgCompletionTime,
        efficiency: analysis.overallEfficiency,
        ...analysis.taskMetrics
      })
      
      // 设置优化建议
      commit('SET_OPTIMIZATION_SUGGESTIONS', analysis.suggestions)
      
      // 记录分析时间
      commit('SET_LAST_ANALYSIS_TIME', new Date().toISOString())
      
      commit('ADD_AUTOMATION_LOG', {
        type: 'workflow_analyzed',
        message: `完成项目 ${projectId} 工作流分析`,
        data: analysis
      })
      
      return analysis
    } catch (error) {
      console.error('工作流分析失败:', error)
      throw error
    } finally {
      commit('SET_ANALYZING', false)
    }
  },
  
  // 应用优化建议
  async applyOptimizationSuggestion({ commit, dispatch }, suggestion) {
    try {
      // 根据建议类型执行相应操作
      switch (suggestion.action) {
        case 'enable_auto_assignment':
          await dispatch('enableAutoAssignment')
          break
        case 'enable_auto_transition':
          await dispatch('enableAutoTransition')
          break
        case 'enable_smart_reminders':
          await dispatch('enableSmartReminders')
          break
        case 'optimize_notifications':
          await dispatch('optimizeNotifications')
          break
      }
      
      commit('ADD_APPLIED_SUGGESTION', suggestion)
      commit('ADD_AUTOMATION_LOG', {
        type: 'suggestion_applied',
        message: `应用优化建议: ${suggestion.title}`,
        data: suggestion
      })
      
    } catch (error) {
      console.error('应用优化建议失败:', error)
      throw error
    }
  },
  
  // 启用任务自动分配
  async enableAutoAssignment({ commit }) {
    const rule = {
      id: `auto_assignment_${Date.now()}`,
      name: '任务自动分配',
      type: 'task_auto_assignment',
      description: '根据团队成员工作负载自动分配新任务',
      isActive: true,
      conditions: {
        trigger: 'task_created',
        criteria: 'unassigned_task'
      },
      actions: {
        type: 'assign_to_least_loaded_member'
      }
    }
    
    workflowService.addAutomationRule(rule)
    commit('ADD_AUTOMATION_RULE', rule)
  },
  
  // 启用状态自动流转
  async enableAutoTransition({ commit }) {
    const rule = {
      id: `auto_transition_${Date.now()}`,
      name: '状态自动流转',
      type: 'status_auto_transition',
      description: '满足条件时自动流转任务状态',
      isActive: true,
      conditions: {
        trigger: 'task_completed',
        criteria: 'all_dependencies_met'
      },
      actions: {
        type: 'transition_to_next_status'
      }
    }
    
    workflowService.addAutomationRule(rule)
    commit('ADD_AUTOMATION_RULE', rule)
  },
  
  // 启用智能提醒
  async enableSmartReminders({ commit }) {
    const rule = {
      id: `smart_reminders_${Date.now()}`,
      name: '智能提醒',
      type: 'deadline_reminder',
      description: '基于历史数据智能发送截止日期提醒',
      isActive: true,
      conditions: {
        trigger: 'time_based',
        criteria: 'approaching_deadline'
      },
      actions: {
        type: 'send_smart_reminder'
      }
    }
    
    workflowService.addAutomationRule(rule)
    commit('ADD_AUTOMATION_RULE', rule)
  },
  
  // 启动自动化监控
  startAutomationMonitoring({ commit, state }) {
    if (state.isAutomationEnabled) return
    
    commit('SET_AUTOMATION_ENABLED', true)
    
    // 启动定时检查
    setInterval(() => {
      // 检查待处理的自动化任务
      // 这里可以添加具体的监控逻辑
    }, 60000) // 每分钟检查一次
    
    commit('ADD_AUTOMATION_LOG', {
      type: 'monitoring_started',
      message: '工作流自动化监控已启动'
    })
  },
  
  // 停止自动化监控
  stopAutomationMonitoring({ commit }) {
    commit('SET_AUTOMATION_ENABLED', false)
    commit('ADD_AUTOMATION_LOG', {
      type: 'monitoring_stopped',
      message: '工作流自动化监控已停止'
    })
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
