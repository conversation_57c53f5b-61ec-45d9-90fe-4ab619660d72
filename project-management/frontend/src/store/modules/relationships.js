/**
 * 模块间关联管理
 * 负责处理项目、任务、风险、会议等模块间的数据关联和业务逻辑
 */

const state = {
  // 关联关系缓存
  projectRelations: {}, // { projectId: { tasks: [], risks: [], meetings: [] } }
  taskDependencies: {}, // { taskId: { dependencies: [], dependents: [] } }

  // 统计数据缓存
  projectStats: {}, // { projectId: { progress, taskStats, riskStats, etc. } }

  // 同步状态
  lastSyncTime: null,
  syncInProgress: false
}

const mutations = {
  SET_PROJECT_RELATIONS(state, { projectId, relations }) {
    state.projectRelations = {
      ...state.projectRelations,
      [projectId]: relations
    }
  },

  SET_PROJECT_STATS(state, { projectId, stats }) {
    state.projectStats = {
      ...state.projectStats,
      [projectId]: stats
    }
  },

  SET_TASK_DEPENDENCIES(state, { taskId, dependencies }) {
    state.taskDependencies = {
      ...state.taskDependencies,
      [taskId]: dependencies
    }
  },

  SET_SYNC_STATUS(state, status) {
    state.syncInProgress = status
  },

  SET_LAST_SYNC_TIME(state, time) {
    state.lastSyncTime = time
  },

  CLEAR_PROJECT_RELATIONS(state, projectId) {
    delete state.projectRelations[projectId]
    delete state.projectStats[projectId]
  }
}

const actions = {
  /**
   * 同步项目关联数据
   */
  async syncProjectRelations({ commit, dispatch, rootGetters }, projectId) {
    try {
      commit('SET_SYNC_STATUS', true)

      // 获取项目相关的所有数据
      const [tasks, risks, meetings] = await Promise.all([
        dispatch('tasks/fetchTasksByProject', projectId, { root: true }),
        dispatch('risks/fetchRisksByProject', projectId, { root: true }),
        dispatch('meetings/fetchMeetingsByProject', projectId, { root: true })
      ])

      // 构建关联关系
      const relations = {
        tasks: tasks || [],
        risks: risks || [],
        meetings: meetings || []
      }

      commit('SET_PROJECT_RELATIONS', { projectId, relations })

      // 计算项目统计数据
      await dispatch('calculateProjectStats', projectId)

      commit('SET_LAST_SYNC_TIME', new Date().toISOString())

      return relations
    } catch (error) {
      dispatch('setError', `同步项目关联数据失败: ${error.message}`, { root: true })
      throw error
    } finally {
      commit('SET_SYNC_STATUS', false)
    }
  },

  /**
   * 计算项目统计数据
   */
  async calculateProjectStats({ commit, state, rootGetters }, projectId) {
    try {
      const relations = state.projectRelations[projectId]
      if (!relations) return null

      const { tasks, risks, meetings } = relations

      // 任务统计
      const taskStats = {
        total: tasks.length,
        completed: tasks.filter(t => t.status === 'completed').length,
        inProgress: tasks.filter(t => t.status === 'in-progress').length,
        pending: tasks.filter(t => t.status === 'pending').length,
        overdue: tasks.filter(t => {
          const dueDate = new Date(t.dueDate || t.endDate)
          return t.status !== 'completed' && dueDate < new Date()
        }).length
      }

      // 风险统计
      const riskStats = {
        total: risks.length,
        open: risks.filter(r => r.status === 'open').length,
        monitoring: risks.filter(r => r.status === 'monitoring').length,
        mitigated: risks.filter(r => r.status === 'mitigated').length,
        closed: risks.filter(r => r.status === 'closed').length,
        high: risks.filter(r => r.impactLevel === 'high' || r.level === 'high').length,
        critical: risks.filter(r => r.impactLevel === 'critical' || r.level === 'critical').length
      }

      // 会议统计
      const meetingStats = {
        total: meetings.length,
        upcoming: meetings.filter(m => new Date(m.startTime) > new Date()).length,
        completed: meetings.filter(m => m.status === 'completed').length
      }

      // 计算项目整体进度
      const progress = taskStats.total > 0
        ? Math.round((taskStats.completed / taskStats.total) * 100)
        : 0

      // 计算项目健康度
      const healthScore = this.calculateProjectHealthScore(taskStats, riskStats)

      const stats = {
        progress,
        healthScore,
        taskStats,
        riskStats,
        meetingStats,
        lastUpdated: new Date().toISOString()
      }

      commit('SET_PROJECT_STATS', { projectId, stats })

      // 同步更新项目进度
      await dispatch('projects/updateProjectProgress', {
        projectId,
        progress
      }, { root: true })

      return stats
    } catch (error) {
      console.error('计算项目统计数据失败:', error)
      throw error
    }
  },

  /**
   * 计算项目健康度评分
   */
  calculateProjectHealthScore(context, taskStats, riskStats) {
    let score = 100

    // 任务延期扣分
    if (taskStats.overdue > 0) {
      score -= Math.min(taskStats.overdue * 10, 30)
    }

    // 高风险扣分
    if (riskStats.high > 0) {
      score -= Math.min(riskStats.high * 15, 40)
    }

    // 关键风险扣分
    if (riskStats.critical > 0) {
      score -= Math.min(riskStats.critical * 25, 50)
    }

    // 任务完成率加分
    if (taskStats.total > 0) {
      const completionRate = taskStats.completed / taskStats.total
      score += completionRate * 20
    }

    return Math.max(0, Math.min(100, Math.round(score)))
  },

  /**
   * 处理任务状态变更的级联影响
   */
  async handleTaskStatusChange({ dispatch, commit }, { taskId, oldStatus, newStatus, projectId }) {
    try {
      // 重新计算项目统计
      await dispatch('calculateProjectStats', projectId)

      // 如果任务完成，检查是否有依赖任务可以开始
      if (newStatus === 'completed') {
        await dispatch('checkDependentTasks', taskId)
      }

      // 如果任务延期，创建风险记录
      if (newStatus === 'overdue') {
        await dispatch('createTaskDelayRisk', { taskId, projectId })
      }

      // 通知相关模块更新
      await dispatch('notifyModulesUpdate', {
        type: 'task_status_change',
        taskId,
        projectId,
        oldStatus,
        newStatus
      })

    } catch (error) {
      console.error('处理任务状态变更失败:', error)
    }
  },

  /**
   * 处理项目状态变更的级联影响
   */
  async handleProjectStatusChange({ dispatch, state }, { projectId, oldStatus, newStatus }) {
    try {
      const relations = state.projectRelations[projectId]
      if (!relations) return

      // 如果项目完成，自动完成所有未完成任务
      if (newStatus === 'completed') {
        const incompleteTasks = relations.tasks.filter(t => t.status !== 'completed')
        for (const task of incompleteTasks) {
          await dispatch('tasks/updateTaskStatus', {
            taskId: task.id,
            status: 'completed'
          }, { root: true })
        }

        // 关闭所有开放的风险
        const openRisks = relations.risks.filter(r => r.status === 'open')
        for (const risk of openRisks) {
          await dispatch('risks/updateRiskStatus', {
            riskId: risk.id,
            status: 'closed'
          }, { root: true })
        }
      }

      // 如果项目暂停，暂停所有进行中的任务
      if (newStatus === 'paused') {
        const activeTasks = relations.tasks.filter(t => t.status === 'in-progress')
        for (const task of activeTasks) {
          await dispatch('tasks/updateTaskStatus', {
            taskId: task.id,
            status: 'paused'
          }, { root: true })
        }
      }

      // 重新同步项目关联数据
      await dispatch('syncProjectRelations', projectId)

    } catch (error) {
      console.error('处理项目状态变更失败:', error)
    }
  },

  /**
   * 创建任务延期风险
   */
  async createTaskDelayRisk({ dispatch }, { taskId, projectId }) {
    try {
      const riskData = {
        title: `任务延期风险 - 任务ID: ${taskId}`,
        description: '任务超过预期完成时间，可能影响项目进度',
        projectId: projectId,
        category: 'schedule',
        impactLevel: 'medium',
        status: 'open',
        relatedTaskId: taskId
      }

      await dispatch('risks/createRisk', riskData, { root: true })
    } catch (error) {
      console.error('创建任务延期风险失败:', error)
    }
  },

  /**
   * 检查依赖任务
   */
  async checkDependentTasks({ dispatch, state }, taskId) {
    try {
      const dependencies = state.taskDependencies[taskId]
      if (!dependencies || !dependencies.dependents) return

      // 检查依赖此任务的其他任务是否可以开始
      for (const dependentTaskId of dependencies.dependents) {
        await dispatch('tasks/checkTaskCanStart', dependentTaskId, { root: true })
      }
    } catch (error) {
      console.error('检查依赖任务失败:', error)
    }
  },

  /**
   * 通知模块更新
   */
  async notifyModulesUpdate({ dispatch }, { type, ...data }) {
    try {
      // 根据事件类型通知相关模块
      switch (type) {
        case 'task_status_change':
          // 通知甘特图更新
          dispatch('gantt/refreshData', data.projectId, { root: true })
          // 通知看板更新
          dispatch('kanban/refreshData', data.projectId, { root: true })
          break

        case 'project_status_change':
          // 通知所有相关视图更新
          dispatch('dashboard/refreshProjectData', data.projectId, { root: true })
          break

        case 'risk_status_change':
          // 通知风险仪表盘更新
          dispatch('dashboard/refreshRiskData', data.projectId, { root: true })
          break
      }
    } catch (error) {
      console.error('通知模块更新失败:', error)
    }
  },

  /**
   * 删除项目及其关联数据
   */
  async deleteProjectWithRelations({ dispatch, commit }, projectId) {
    try {
      // 删除关联的任务
      await dispatch('tasks/deleteTasksByProject', projectId, { root: true })

      // 删除关联的风险
      await dispatch('risks/deleteRisksByProject', projectId, { root: true })

      // 删除关联的会议
      await dispatch('meetings/deleteMeetingsByProject', projectId, { root: true })

      // 删除项目
      await dispatch('projects/deleteProject', projectId, { root: true })

      // 清理关联数据缓存
      commit('CLEAR_PROJECT_RELATIONS', projectId)

      dispatch('setSuccess', '项目及其关联数据删除成功', { root: true })
    } catch (error) {
      dispatch('setError', `删除项目失败: ${error.message}`, { root: true })
      throw error
    }
  }
}

const getters = {
  // 获取项目关联数据
  getProjectRelations: state => projectId => state.projectRelations[projectId],

  // 获取项目统计数据
  getProjectStats: state => projectId => state.projectStats[projectId],

  // 获取任务依赖关系
  getTaskDependencies: state => taskId => state.taskDependencies[taskId],

  // 检查同步状态
  isSyncInProgress: state => state.syncInProgress,

  // 获取最后同步时间
  getLastSyncTime: state => state.lastSyncTime,

  // 计算项目健康度
  calculateProjectHealth: () => (taskStats, riskStats) => {
    let score = 100

    // 任务延期扣分
    if (taskStats.overdue > 0) {
      score -= Math.min(taskStats.overdue * 10, 30)
    }

    // 高风险扣分
    if (riskStats.high > 0) {
      score -= Math.min(riskStats.high * 15, 40)
    }

    // 关键风险扣分
    if (riskStats.critical > 0) {
      score -= Math.min(riskStats.critical * 25, 50)
    }

    // 任务完成率加分
    if (taskStats.total > 0) {
      const completionRate = taskStats.completed / taskStats.total
      score += completionRate * 20
    }

    return Math.max(0, Math.min(100, Math.round(score)))
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
