<template>
  <div class="google-content">
    <!-- 页面头部 - Google风格 -->
    <div class="medical-page-header mb-6">
      <div class="d-flex align-center justify-space-between">
        <div class="d-flex align-center">
          <v-icon
            icon="mdi-account-circle"
            size="48"
            color="medical-primary"
            class="me-4"
          ></v-icon>
          <div>
            <h1 class="medical-gradient-text text-h4 mb-1">个人资料</h1>
            <p class="text-subtitle-1 text-medium-emphasis">
              <v-icon icon="mdi-hospital-box" size="16" class="me-1"></v-icon>
              医疗项目管理系统 - 个人信息管理
            </p>
          </div>
        </div>
        <div class="d-flex align-center ga-3">
          <v-btn
            :color="editMode ? 'medical-warning' : 'medical-primary'"
            :prepend-icon="editMode ? 'mdi-close' : 'mdi-pencil'"
            class="medical-btn-primary"
            @click="editMode = !editMode"
          >
            {{ editMode ? '取消编辑' : '编辑资料' }}
          </v-btn>
          <v-btn
            color="medical-info"
            prepend-icon="mdi-chart-line"
            variant="outlined"
            @click="showWorkflowOptimization = true"
          >
            工作流优化
          </v-btn>
        </div>
      </div>
    </div>

    <v-row>
      <v-col cols="12" md="4">
        <!-- 用户信息卡片 - 医疗主题 -->
        <v-card class="medical-card mb-4">
          <div class="medical-card-header pa-4">
            <div class="text-center">
              <v-avatar size="120" class="mb-3" color="white">
                <v-img :src="user.avatar" alt="用户头像">
                  <template v-slot:placeholder>
                    <v-icon icon="mdi-account" size="60" color="medical-primary"></v-icon>
                  </template>
                </v-img>
              </v-avatar>
              <h2 class="text-h5 mb-1 text-white">{{ user.name }}</h2>
              <p class="text-body-1 text-white opacity-90">{{ user.position }}</p>
              <v-chip
                color="white"
                text-color="medical-primary"
                size="small"
                class="mt-2"
              >
                <v-icon start icon="mdi-hospital-box"></v-icon>
                医疗项目专家
              </v-chip>
            </div>
          </div>

          <v-card-text class="pa-4">
            <v-btn
              v-if="editMode"
              color="medical-primary"
              variant="outlined"
              block
              prepend-icon="mdi-camera"
              class="mb-3"
              @click="showUploadDialog = true"
            >
              更换头像
            </v-btn>

            <!-- 快速统计 -->
            <div class="text-center">
              <v-row dense>
                <v-col cols="6">
                  <div class="text-h6 medical-gradient-text">{{ stats.projects }}</div>
                  <div class="text-caption text-medium-emphasis">参与项目</div>
                </v-col>
                <v-col cols="6">
                  <div class="text-h6 medical-gradient-text">{{ workflowEfficiency }}%</div>
                  <div class="text-caption text-medium-emphasis">工作效率</div>
                </v-col>
              </v-row>
            </div>
          </v-card-text>
        </v-card>

        <!-- 联系方式卡片 - 医疗主题 -->
        <v-card class="medical-card">
          <v-card-title class="d-flex align-center">
            <v-icon icon="mdi-card-account-details" color="medical-primary" class="me-2"></v-icon>
            联系方式
          </v-card-title>
          <v-card-text>
            <v-list class="pa-0">
              <v-list-item class="px-0">
                <template v-slot:prepend>
                  <v-avatar size="40" color="medical-info" class="me-3">
                    <v-icon icon="mdi-email" color="white"></v-icon>
                  </v-avatar>
                </template>
                <v-list-item-title class="text-caption text-medium-emphasis">邮箱地址</v-list-item-title>
                <v-list-item-subtitle class="text-body-1 font-weight-medium">{{ user.email }}</v-list-item-subtitle>
              </v-list-item>

              <v-list-item class="px-0">
                <template v-slot:prepend>
                  <v-avatar size="40" color="medical-success" class="me-3">
                    <v-icon icon="mdi-phone" color="white"></v-icon>
                  </v-avatar>
                </template>
                <v-list-item-title class="text-caption text-medium-emphasis">手机号码</v-list-item-title>
                <v-list-item-subtitle class="text-body-1 font-weight-medium">{{ user.phone }}</v-list-item-subtitle>
              </v-list-item>

              <v-list-item class="px-0">
                <template v-slot:prepend>
                  <v-avatar size="40" color="medical-warning" class="me-3">
                    <v-icon icon="mdi-hospital-building" color="white"></v-icon>
                  </v-avatar>
                </template>
                <v-list-item-title class="text-caption text-medium-emphasis">所属部门</v-list-item-title>
                <v-list-item-subtitle class="text-body-1 font-weight-medium">{{ user.department }}</v-list-item-subtitle>
              </v-list-item>

              <v-list-item class="px-0">
                <template v-slot:prepend>
                  <v-avatar size="40" color="medical-accent" class="me-3">
                    <v-icon icon="mdi-map-marker" color="white"></v-icon>
                  </v-avatar>
                </template>
                <v-list-item-title class="text-caption text-medium-emphasis">工作地址</v-list-item-title>
                <v-list-item-subtitle class="text-body-1 font-weight-medium">{{ user.address }}</v-list-item-subtitle>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="8">
        <!-- 个人资料表单 - 医疗主题 -->
        <v-card class="medical-card mb-4">
          <v-card-title class="d-flex align-center">
            <v-icon icon="mdi-account-edit" color="medical-primary" class="me-2"></v-icon>
            个人资料
            <v-spacer></v-spacer>
            <v-chip
              :color="editMode ? 'medical-warning' : 'medical-success'"
              size="small"
              variant="flat"
            >
              <v-icon
                :icon="editMode ? 'mdi-pencil' : 'mdi-lock'"
                start
                size="16"
              ></v-icon>
              {{ editMode ? '编辑模式' : '查看模式' }}
            </v-chip>
          </v-card-title>
          <v-card-text>
            <v-form ref="form" v-model="valid">
              <v-row>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="user.name"
                    label="姓名"
                    :rules="nameRules"
                    variant="outlined"
                    :readonly="!editMode"
                    :color="editMode ? 'medical-primary' : 'grey'"
                    prepend-inner-icon="mdi-account"
                    required
                  ></v-text-field>
                </v-col>

                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="user.email"
                    label="邮箱地址"
                    :rules="emailRules"
                    variant="outlined"
                    readonly
                    disabled
                    prepend-inner-icon="mdi-email"
                  ></v-text-field>
                </v-col>

                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="user.phone"
                    label="手机号码"
                    :rules="phoneRules"
                    variant="outlined"
                    :readonly="!editMode"
                    :color="editMode ? 'medical-primary' : 'grey'"
                    prepend-inner-icon="mdi-phone"
                  ></v-text-field>
                </v-col>

                <v-col cols="12" md="6">
                  <v-select
                    v-model="user.department"
                    :items="medicalDepartments"
                    label="医疗部门"
                    variant="outlined"
                    :readonly="!editMode"
                    :color="editMode ? 'medical-primary' : 'grey'"
                    prepend-inner-icon="mdi-hospital-building"
                  ></v-select>
                </v-col>

                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="user.position"
                    label="职位"
                    variant="outlined"
                    :readonly="!editMode"
                    :color="editMode ? 'medical-primary' : 'grey'"
                    prepend-inner-icon="mdi-badge-account"
                  ></v-text-field>
                </v-col>

                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="user.joinDate"
                    label="入职日期"
                    variant="outlined"
                    readonly
                    disabled
                    prepend-inner-icon="mdi-calendar"
                  ></v-text-field>
                </v-col>

                <v-col cols="12">
                  <v-text-field
                    v-model="user.address"
                    label="工作地址"
                    variant="outlined"
                    :readonly="!editMode"
                    :color="editMode ? 'medical-primary' : 'grey'"
                    prepend-inner-icon="mdi-map-marker"
                  ></v-text-field>
                </v-col>

                <v-col cols="12">
                  <v-textarea
                    v-model="user.bio"
                    label="个人简介"
                    variant="outlined"
                    :readonly="!editMode"
                    :color="editMode ? 'medical-primary' : 'grey'"
                    prepend-inner-icon="mdi-text-account"
                    auto-grow
                    rows="3"
                  ></v-textarea>
                </v-col>
              </v-row>

              <v-row v-if="editMode">
                <v-col cols="12" class="d-flex justify-end ga-3">
                  <v-btn
                    color="grey"
                    variant="outlined"
                    @click="editMode = false"
                  >
                    取消
                  </v-btn>
                  <v-btn
                    color="medical-primary"
                    class="medical-btn-primary"
                    :disabled="!valid"
                    @click="saveProfile"
                  >
                    <v-icon start icon="mdi-content-save"></v-icon>
                    保存资料
                  </v-btn>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>

        <v-card>
          <v-card-title>
            <div class="d-flex align-center justify-space-between">
              <span>工作统计</span>
              <v-btn
                color="primary"
                variant="text"
                size="small"
                prepend-icon="mdi-chart-line"
                @click="showWorkflowOptimization = true"
              >
                工作流优化
              </v-btn>
            </div>
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="6" md="3">
                <div class="text-center">
                  <div class="text-h4 font-weight-bold">{{ stats.projects }}</div>
                  <div class="text-caption text-medium-emphasis">参与项目</div>
                </div>
              </v-col>

              <v-col cols="6" md="3">
                <div class="text-center">
                  <div class="text-h4 font-weight-bold">{{ stats.tasks }}</div>
                  <div class="text-caption text-medium-emphasis">完成任务</div>
                </div>
              </v-col>

              <v-col cols="6" md="3">
                <div class="text-center">
                  <div class="text-h4 font-weight-bold">{{ stats.meetings }}</div>
                  <div class="text-caption text-medium-emphasis">参加会议</div>
                </div>
              </v-col>

              <v-col cols="6" md="3">
                <div class="text-center">
                  <div class="text-h4 font-weight-bold">{{ stats.documents }}</div>
                  <div class="text-caption text-medium-emphasis">提交文档</div>
                </div>
              </v-col>
            </v-row>

            <v-divider class="my-4"></v-divider>

            <div class="mb-4">
              <div class="d-flex align-center justify-space-between mb-2">
                <div class="text-body-1">任务完成率</div>
                <div class="text-body-1 font-weight-bold">{{ stats.taskCompletionRate }}%</div>
              </div>
              <v-progress-linear
                :model-value="stats.taskCompletionRate"
                color="success"
                height="8"
                rounded
              ></v-progress-linear>
            </div>

            <div class="mb-4">
              <div class="d-flex align-center justify-space-between mb-2">
                <div class="text-body-1">按时完成率</div>
                <div class="text-body-1 font-weight-bold">{{ stats.onTimeRate }}%</div>
              </div>
              <v-progress-linear
                :model-value="stats.onTimeRate"
                color="info"
                height="8"
                rounded
              ></v-progress-linear>
            </div>

            <!-- 工作流效率指标 -->
            <div class="mb-4">
              <div class="d-flex align-center justify-space-between mb-2">
                <div class="text-body-1">工作流效率</div>
                <div class="text-body-1 font-weight-bold">{{ workflowEfficiency }}%</div>
              </div>
              <v-progress-linear
                :model-value="workflowEfficiency"
                :color="getEfficiencyColor(workflowEfficiency)"
                height="8"
                rounded
              ></v-progress-linear>
            </div>

            <!-- 待处理事项 -->
            <div v-if="pendingItems.length > 0">
              <v-divider class="my-4"></v-divider>
              <div class="text-subtitle-2 mb-2">待处理事项</div>
              <v-chip
                v-for="item in pendingItems"
                :key="item.id"
                :color="item.priority === 'high' ? 'error' : item.priority === 'medium' ? 'warning' : 'info'"
                size="small"
                class="me-2 mb-2"
                @click="handlePendingItem(item)"
              >
                {{ item.title }}
              </v-chip>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 上传头像对话框 -->
    <v-dialog v-model="showUploadDialog" max-width="500">
      <v-card>
        <v-card-title>上传头像</v-card-title>
        <v-card-text>
          <v-file-input
            v-model="avatarFile"
            label="选择图片"
            accept="image/*"
            show-size
            truncate-length="15"
            variant="outlined"
          ></v-file-input>

          <div v-if="avatarPreview" class="text-center mt-4">
            <v-avatar size="150">
              <v-img :src="avatarPreview" alt="Avatar Preview"></v-img>
            </v-avatar>
          </div>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="grey-darken-1"
            variant="text"
            @click="showUploadDialog = false"
          >
            取消
          </v-btn>
          <v-btn
            color="primary"
            variant="text"
            :disabled="!avatarFile"
            @click="uploadAvatar"
          >
            上传
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 工作流优化对话框 -->
    <v-dialog v-model="showWorkflowOptimization" max-width="1200">
      <v-card>
        <v-card-title class="d-flex align-center">
          <v-icon icon="mdi-chart-line" class="me-2"></v-icon>
          工作流优化分析
        </v-card-title>

        <v-card-text>
          <v-row>
            <!-- 工作流效率分析 -->
            <v-col cols="12" md="6">
              <v-card variant="outlined">
                <v-card-title class="text-h6">效率分析</v-card-title>
                <v-card-text>
                  <div class="mb-4">
                    <div class="d-flex justify-space-between mb-2">
                      <span>任务处理速度</span>
                      <span class="font-weight-bold">{{ workflowMetrics.taskProcessingSpeed }}个/天</span>
                    </div>
                    <v-progress-linear
                      :model-value="workflowMetrics.taskProcessingSpeed * 10"
                      color="primary"
                      height="6"
                    ></v-progress-linear>
                  </div>

                  <div class="mb-4">
                    <div class="d-flex justify-space-between mb-2">
                      <span>平均响应时间</span>
                      <span class="font-weight-bold">{{ workflowMetrics.avgResponseTime }}小时</span>
                    </div>
                    <v-progress-linear
                      :model-value="Math.max(0, 100 - workflowMetrics.avgResponseTime * 5)"
                      color="info"
                      height="6"
                    ></v-progress-linear>
                  </div>

                  <div class="mb-4">
                    <div class="d-flex justify-space-between mb-2">
                      <span>工作流自动化率</span>
                      <span class="font-weight-bold">{{ workflowMetrics.automationRate }}%</span>
                    </div>
                    <v-progress-linear
                      :model-value="workflowMetrics.automationRate"
                      color="success"
                      height="6"
                    ></v-progress-linear>
                  </div>
                </v-card-text>
              </v-card>
            </v-col>

            <!-- 优化建议 -->
            <v-col cols="12" md="6">
              <v-card variant="outlined">
                <v-card-title class="text-h6">优化建议</v-card-title>
                <v-card-text>
                  <v-list density="compact">
                    <v-list-item
                      v-for="suggestion in optimizationSuggestions"
                      :key="suggestion.id"
                      :prepend-icon="suggestion.icon"
                      :title="suggestion.title"
                      :subtitle="suggestion.description"
                      @click="applySuggestion(suggestion)"
                    >
                      <template v-slot:append>
                        <v-chip
                          :color="suggestion.impact === 'high' ? 'success' : suggestion.impact === 'medium' ? 'warning' : 'info'"
                          size="small"
                        >
                          {{ suggestion.impact === 'high' ? '高效果' : suggestion.impact === 'medium' ? '中效果' : '低效果' }}
                        </v-chip>
                      </template>
                    </v-list-item>
                  </v-list>
                </v-card-text>
              </v-card>
            </v-col>

            <!-- 工作流模板 -->
            <v-col cols="12">
              <v-card variant="outlined">
                <v-card-title class="text-h6">工作流模板</v-card-title>
                <v-card-text>
                  <v-row>
                    <v-col
                      v-for="template in workflowTemplates"
                      :key="template.id"
                      cols="12"
                      md="4"
                    >
                      <v-card
                        variant="outlined"
                        class="workflow-template-card"
                        @click="applyWorkflowTemplate(template)"
                      >
                        <v-card-text class="text-center">
                          <v-icon
                            :icon="template.icon"
                            size="48"
                            :color="template.color"
                            class="mb-2"
                          ></v-icon>
                          <div class="text-h6 mb-1">{{ template.name }}</div>
                          <div class="text-caption text-medium-emphasis">{{ template.description }}</div>
                        </v-card-text>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="grey-darken-1"
            variant="text"
            @click="showWorkflowOptimization = false"
          >
            关闭
          </v-btn>
          <v-btn
            color="primary"
            variant="text"
            @click="exportWorkflowReport"
          >
            导出报告
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 操作成功提示 -->
    <v-snackbar
      v-model="showSnackbar"
      color="success"
    >
      {{ snackbarText }}

      <template v-slot:actions>
        <v-btn
          variant="text"
          @click="showSnackbar = false"
        >
          关闭
        </v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script>
export default {
  name: 'ProfileView',
  data() {
    return {
      editMode: false,
      valid: true,
      showUploadDialog: false,
      avatarFile: null,
      avatarPreview: null,
      showSnackbar: false,
      snackbarText: '',
      showWorkflowOptimization: false,

      nameRules: [
        v => !!v || '姓名不能为空',
        v => v.length <= 20 || '姓名不能超过20个字符'
      ],
      emailRules: [
        v => !!v || '邮箱不能为空',
        v => /.+@.+\..+/.test(v) || '邮箱格式不正确'
      ],
      phoneRules: [
        v => !!v || '手机号码不能为空',
        v => /^1[3-9]\d{9}$/.test(v) || '手机号码格式不正确'
      ],

      // 医疗部门选项
      medicalDepartments: [
        { title: '信息技术部', value: '信息技术部' },
        { title: '医疗信息部', value: '医疗信息部' },
        { title: '项目管理部', value: '项目管理部' },
        { title: '系统集成部', value: '系统集成部' },
        { title: '数据分析部', value: '数据分析部' },
        { title: '质量管理部', value: '质量管理部' },
        { title: '运维支持部', value: '运维支持部' },
        { title: '培训服务部', value: '培训服务部' }
      ],

      user: {
        name: '李医生',
        email: '<EMAIL>',
        phone: '13800138000',
        department: '医疗信息部',
        position: '医疗项目经理',
        joinDate: '2022-03-15',
        address: '北京市朝阳区医疗科技园区A座',
        bio: '拥有10年医疗信息化项目管理经验，专注于医院信息系统集成与数据迁移。擅长医疗流程优化，曾成功带领团队完成多个三甲医院的信息化建设项目。持有PMP项目管理认证和医疗信息化专业认证。',
        avatar: 'https://ui-avatars.com/api/?name=李医生&background=1565C0&color=fff'
      },

      stats: {
        projects: 15,        // 参与的医疗项目数量
        tasks: 189,          // 完成的任务数量
        meetings: 67,        // 参加的项目会议
        documents: 45,       // 提交的项目文档
        taskCompletionRate: 94,  // 任务完成率
        onTimeRate: 91       // 按时完成率
      },

      // 工作流优化相关数据
      workflowMetrics: {
        taskProcessingSpeed: 8.5,
        avgResponseTime: 2.3,
        automationRate: 75
      },

      pendingItems: [
        { id: 1, title: '北京协和医院HIS系统审批', priority: 'high', type: 'approval' },
        { id: 2, title: '上海瑞金医院数据迁移验收', priority: 'medium', type: 'review' },
        { id: 3, title: '医疗设备接口测试确认', priority: 'high', type: 'confirmation' },
        { id: 4, title: '医院培训计划审核', priority: 'low', type: 'approval' }
      ],

      optimizationSuggestions: [
        {
          id: 1,
          title: '启用医疗任务智能分配',
          description: '根据医疗专业技能和工作负载自动分配医院项目任务',
          icon: 'mdi-hospital-box',
          impact: 'high',
          action: 'enable_auto_assignment'
        },
        {
          id: 2,
          title: '医疗项目阶段自动流转',
          description: '医院确认完成后自动推进到下一实施阶段',
          icon: 'mdi-arrow-right-circle',
          impact: 'high',
          action: 'enable_auto_transition'
        },
        {
          id: 3,
          title: '优化医院沟通频率',
          description: '减少非关键通知，专注医疗核心业务沟通',
          icon: 'mdi-bell-outline',
          impact: 'medium',
          action: 'optimize_notifications'
        },
        {
          id: 4,
          title: '启用医疗项目智能预警',
          description: '基于医院项目历史数据预测实施风险和延期',
          icon: 'mdi-brain',
          impact: 'high',
          action: 'enable_smart_reminders'
        },
        {
          id: 5,
          title: '医院培训自动安排',
          description: '根据项目进度自动安排医院用户培训计划',
          icon: 'mdi-school',
          impact: 'medium',
          action: 'enable_training_schedule'
        }
      ],

      workflowTemplates: [
        {
          id: 1,
          name: 'HIS系统实施流程',
          description: '医院信息系统(HIS)标准实施工作流程',
          icon: 'mdi-hospital-box',
          color: 'medical-primary'
        },
        {
          id: 2,
          name: 'EMR系统集成流程',
          description: '电子病历系统集成与数据迁移流程',
          icon: 'mdi-file-document-multiple',
          color: 'medical-success'
        },
        {
          id: 3,
          name: 'PACS影像系统流程',
          description: '医学影像存储与传输系统实施流程',
          icon: 'mdi-image-multiple',
          color: 'medical-info'
        },
        {
          id: 4,
          name: '医疗设备接口流程',
          description: '医疗设备与信息系统接口集成流程',
          icon: 'mdi-connection',
          color: 'medical-warning'
        },
        {
          id: 5,
          name: '医院培训支持流程',
          description: '医院用户培训与技术支持标准流程',
          icon: 'mdi-school',
          color: 'medical-secondary'
        }
      ]
    }
  },
  computed: {
    workflowEfficiency() {
      // 基于任务完成率、按时完成率和自动化率计算工作流效率
      const taskWeight = 0.4
      const timeWeight = 0.3
      const autoWeight = 0.3

      return Math.round(
        this.stats.taskCompletionRate * taskWeight +
        this.stats.onTimeRate * timeWeight +
        this.workflowMetrics.automationRate * autoWeight
      )
    }
  },
  watch: {
    avatarFile(file) {
      if (file) {
        this.createImagePreview(file)
      } else {
        this.avatarPreview = null
      }
    }
  },
  methods: {
    saveProfile() {
      // 在实际应用中，这里会发送请求到后端保存个人资料
      this.editMode = false
      this.showSuccessMessage('个人资料保存成功')
    },
    createImagePreview(file) {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = e => {
        this.avatarPreview = e.target.result
      }
    },
    uploadAvatar() {
      // 在实际应用中，这里会发送请求到后端上传头像
      if (this.avatarPreview) {
        this.user.avatar = this.avatarPreview
      }
      this.showUploadDialog = false
      this.avatarFile = null
      this.showSuccessMessage('头像上传成功')
    },
    showSuccessMessage(text) {
      this.snackbarText = text
      this.showSnackbar = true
    },

    // 工作流优化相关方法
    getEfficiencyColor(efficiency) {
      if (efficiency >= 85) return 'success'
      if (efficiency >= 70) return 'warning'
      return 'error'
    },

    handlePendingItem(item) {
      // 处理待办事项
      switch (item.type) {
        case 'approval':
          this.$router.push('/projects')
          break
        case 'review':
          this.$router.push('/kanban')
          break
        case 'confirmation':
          this.$router.push('/meetings')
          break
      }
      this.showSuccessMessage(`正在处理：${item.title}`)
    },

    applySuggestion(suggestion) {
      // 应用优化建议
      switch (suggestion.action) {
        case 'enable_auto_assignment':
          this.enableAutoAssignment()
          break
        case 'enable_auto_transition':
          this.enableAutoTransition()
          break
        case 'optimize_notifications':
          this.optimizeNotifications()
          break
        case 'enable_smart_reminders':
          this.enableSmartReminders()
          break
      }
      this.showSuccessMessage(`已应用优化建议：${suggestion.title}`)
    },

    applyWorkflowTemplate(template) {
      // 应用工作流模板
      this.showSuccessMessage(`正在应用工作流模板：${template.name}`)
      // 这里可以调用API来应用模板
    },

    exportWorkflowReport() {
      // 导出工作流报告
      const reportData = {
        user: this.user.name,
        date: new Date().toLocaleDateString(),
        efficiency: this.workflowEfficiency,
        metrics: this.workflowMetrics,
        suggestions: this.optimizationSuggestions.length
      }

      // 模拟导出功能
      console.log('导出工作流报告:', reportData)
      this.showSuccessMessage('工作流报告已导出')
    },

    // 优化功能实现
    enableAutoAssignment() {
      // 启用自动分配功能
      this.workflowMetrics.automationRate = Math.min(100, this.workflowMetrics.automationRate + 10)
    },

    enableAutoTransition() {
      // 启用自动流转功能
      this.workflowMetrics.automationRate = Math.min(100, this.workflowMetrics.automationRate + 15)
      this.workflowMetrics.avgResponseTime = Math.max(0.5, this.workflowMetrics.avgResponseTime - 0.5)
    },

    optimizeNotifications() {
      // 优化通知设置
      this.workflowMetrics.avgResponseTime = Math.max(0.5, this.workflowMetrics.avgResponseTime - 0.3)
    },

    enableSmartReminders() {
      // 启用智能提醒
      this.workflowMetrics.taskProcessingSpeed = Math.min(15, this.workflowMetrics.taskProcessingSpeed + 1.5)
      this.stats.onTimeRate = Math.min(100, this.stats.onTimeRate + 5)
    }
  }
}
</script>

<style scoped>
.workflow-template-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.workflow-template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.workflow-template-card .v-card-text {
  padding: 24px;
}

/* 工作流效率指标样式 */
.v-progress-linear {
  border-radius: 4px;
}

/* 待处理事项样式 */
.v-chip {
  cursor: pointer;
  transition: all 0.2s ease;
}

.v-chip:hover {
  transform: scale(1.05);
}

/* 优化建议列表样式 */
.v-list-item {
  border-radius: 8px;
  margin-bottom: 8px;
  transition: background-color 0.2s ease;
}

.v-list-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .workflow-template-card .v-card-text {
    padding: 16px;
  }

  .v-icon {
    font-size: 36px !important;
  }
}
</style>
