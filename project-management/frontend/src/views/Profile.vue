<template>
  <v-container>
    <v-row>
      <v-col cols="12">
        <div class="d-flex align-center justify-space-between mb-4">
          <div>
            <h1 class="text-h4">个人资料</h1>
            <p class="text-subtitle-1 text-medium-emphasis">
              查看和编辑您的个人信息
            </p>
          </div>
          <div>
            <v-btn
              color="primary"
              prepend-icon="mdi-pencil"
              @click="editMode = !editMode"
            >
              {{ editMode ? '取消编辑' : '编辑资料' }}
            </v-btn>
          </div>
        </div>
      </v-col>
    </v-row>

    <v-row>
      <v-col cols="12" md="4">
        <v-card class="mb-4">
          <v-card-text class="text-center">
            <v-avatar size="150" class="mb-4">
              <v-img :src="user.avatar" alt="用户头像"></v-img>
            </v-avatar>

            <h2 class="text-h5 mb-1">{{ user.name }}</h2>
            <p class="text-body-1 text-medium-emphasis">{{ user.position }}</p>

            <v-divider class="my-4"></v-divider>

            <v-btn
              v-if="editMode"
              color="primary"
              variant="text"
              block
              prepend-icon="mdi-camera"
              @click="showUploadDialog = true"
            >
              更换头像
            </v-btn>
          </v-card-text>
        </v-card>

        <v-card>
          <v-card-title>联系方式</v-card-title>
          <v-card-text>
            <div class="d-flex align-center mb-3">
              <v-icon icon="mdi-email-outline" class="me-3"></v-icon>
              <div>
                <div class="text-caption text-medium-emphasis">邮箱</div>
                <div class="text-body-1">{{ user.email }}</div>
              </div>
            </div>

            <div class="d-flex align-center mb-3">
              <v-icon icon="mdi-phone-outline" class="me-3"></v-icon>
              <div>
                <div class="text-caption text-medium-emphasis">手机</div>
                <div class="text-body-1">{{ user.phone }}</div>
              </div>
            </div>

            <div class="d-flex align-center mb-3">
              <v-icon icon="mdi-office-building-outline" class="me-3"></v-icon>
              <div>
                <div class="text-caption text-medium-emphasis">部门</div>
                <div class="text-body-1">{{ user.department }}</div>
              </div>
            </div>

            <div class="d-flex align-center">
              <v-icon icon="mdi-map-marker-outline" class="me-3"></v-icon>
              <div>
                <div class="text-caption text-medium-emphasis">地址</div>
                <div class="text-body-1">{{ user.address }}</div>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="8">
        <v-card class="mb-4">
          <v-card-title>个人资料</v-card-title>
          <v-card-text>
            <v-form ref="form" v-model="valid">
              <v-row>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="user.name"
                    label="姓名"
                    :rules="nameRules"
                    variant="outlined"
                    :readonly="!editMode"
                    required
                  ></v-text-field>
                </v-col>

                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="user.email"
                    label="邮箱"
                    :rules="emailRules"
                    variant="outlined"
                    readonly
                    disabled
                  ></v-text-field>
                </v-col>

                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="user.phone"
                    label="手机号码"
                    :rules="phoneRules"
                    variant="outlined"
                    :readonly="!editMode"
                  ></v-text-field>
                </v-col>

                <v-col cols="12" md="6">
                  <v-select
                    v-model="user.department"
                    :items="departments"
                    label="部门"
                    variant="outlined"
                    :readonly="!editMode"
                  ></v-select>
                </v-col>

                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="user.position"
                    label="职位"
                    variant="outlined"
                    :readonly="!editMode"
                  ></v-text-field>
                </v-col>

                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="user.joinDate"
                    label="入职日期"
                    variant="outlined"
                    readonly
                    disabled
                  ></v-text-field>
                </v-col>

                <v-col cols="12">
                  <v-text-field
                    v-model="user.address"
                    label="地址"
                    variant="outlined"
                    :readonly="!editMode"
                  ></v-text-field>
                </v-col>

                <v-col cols="12">
                  <v-textarea
                    v-model="user.bio"
                    label="个人简介"
                    variant="outlined"
                    :readonly="!editMode"
                    auto-grow
                    rows="3"
                  ></v-textarea>
                </v-col>
              </v-row>

              <v-row v-if="editMode">
                <v-col cols="12" class="d-flex justify-end">
                  <v-btn
                    color="primary"
                    :disabled="!valid"
                    @click="saveProfile"
                  >
                    保存资料
                  </v-btn>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>

        <v-card>
          <v-card-title>
            <div class="d-flex align-center justify-space-between">
              <span>工作统计</span>
              <v-btn
                color="primary"
                variant="text"
                size="small"
                prepend-icon="mdi-chart-line"
                @click="showWorkflowOptimization = true"
              >
                工作流优化
              </v-btn>
            </div>
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="6" md="3">
                <div class="text-center">
                  <div class="text-h4 font-weight-bold">{{ stats.projects }}</div>
                  <div class="text-caption text-medium-emphasis">参与项目</div>
                </div>
              </v-col>

              <v-col cols="6" md="3">
                <div class="text-center">
                  <div class="text-h4 font-weight-bold">{{ stats.tasks }}</div>
                  <div class="text-caption text-medium-emphasis">完成任务</div>
                </div>
              </v-col>

              <v-col cols="6" md="3">
                <div class="text-center">
                  <div class="text-h4 font-weight-bold">{{ stats.meetings }}</div>
                  <div class="text-caption text-medium-emphasis">参加会议</div>
                </div>
              </v-col>

              <v-col cols="6" md="3">
                <div class="text-center">
                  <div class="text-h4 font-weight-bold">{{ stats.documents }}</div>
                  <div class="text-caption text-medium-emphasis">提交文档</div>
                </div>
              </v-col>
            </v-row>

            <v-divider class="my-4"></v-divider>

            <div class="mb-4">
              <div class="d-flex align-center justify-space-between mb-2">
                <div class="text-body-1">任务完成率</div>
                <div class="text-body-1 font-weight-bold">{{ stats.taskCompletionRate }}%</div>
              </div>
              <v-progress-linear
                :model-value="stats.taskCompletionRate"
                color="success"
                height="8"
                rounded
              ></v-progress-linear>
            </div>

            <div class="mb-4">
              <div class="d-flex align-center justify-space-between mb-2">
                <div class="text-body-1">按时完成率</div>
                <div class="text-body-1 font-weight-bold">{{ stats.onTimeRate }}%</div>
              </div>
              <v-progress-linear
                :model-value="stats.onTimeRate"
                color="info"
                height="8"
                rounded
              ></v-progress-linear>
            </div>

            <!-- 工作流效率指标 -->
            <div class="mb-4">
              <div class="d-flex align-center justify-space-between mb-2">
                <div class="text-body-1">工作流效率</div>
                <div class="text-body-1 font-weight-bold">{{ workflowEfficiency }}%</div>
              </div>
              <v-progress-linear
                :model-value="workflowEfficiency"
                :color="getEfficiencyColor(workflowEfficiency)"
                height="8"
                rounded
              ></v-progress-linear>
            </div>

            <!-- 待处理事项 -->
            <div v-if="pendingItems.length > 0">
              <v-divider class="my-4"></v-divider>
              <div class="text-subtitle-2 mb-2">待处理事项</div>
              <v-chip
                v-for="item in pendingItems"
                :key="item.id"
                :color="item.priority === 'high' ? 'error' : item.priority === 'medium' ? 'warning' : 'info'"
                size="small"
                class="me-2 mb-2"
                @click="handlePendingItem(item)"
              >
                {{ item.title }}
              </v-chip>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 上传头像对话框 -->
    <v-dialog v-model="showUploadDialog" max-width="500">
      <v-card>
        <v-card-title>上传头像</v-card-title>
        <v-card-text>
          <v-file-input
            v-model="avatarFile"
            label="选择图片"
            accept="image/*"
            show-size
            truncate-length="15"
            variant="outlined"
          ></v-file-input>

          <div v-if="avatarPreview" class="text-center mt-4">
            <v-avatar size="150">
              <v-img :src="avatarPreview" alt="Avatar Preview"></v-img>
            </v-avatar>
          </div>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="grey-darken-1"
            variant="text"
            @click="showUploadDialog = false"
          >
            取消
          </v-btn>
          <v-btn
            color="primary"
            variant="text"
            :disabled="!avatarFile"
            @click="uploadAvatar"
          >
            上传
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 工作流优化对话框 -->
    <v-dialog v-model="showWorkflowOptimization" max-width="1200">
      <v-card>
        <v-card-title class="d-flex align-center">
          <v-icon icon="mdi-chart-line" class="me-2"></v-icon>
          工作流优化分析
        </v-card-title>

        <v-card-text>
          <v-row>
            <!-- 工作流效率分析 -->
            <v-col cols="12" md="6">
              <v-card variant="outlined">
                <v-card-title class="text-h6">效率分析</v-card-title>
                <v-card-text>
                  <div class="mb-4">
                    <div class="d-flex justify-space-between mb-2">
                      <span>任务处理速度</span>
                      <span class="font-weight-bold">{{ workflowMetrics.taskProcessingSpeed }}个/天</span>
                    </div>
                    <v-progress-linear
                      :model-value="workflowMetrics.taskProcessingSpeed * 10"
                      color="primary"
                      height="6"
                    ></v-progress-linear>
                  </div>

                  <div class="mb-4">
                    <div class="d-flex justify-space-between mb-2">
                      <span>平均响应时间</span>
                      <span class="font-weight-bold">{{ workflowMetrics.avgResponseTime }}小时</span>
                    </div>
                    <v-progress-linear
                      :model-value="Math.max(0, 100 - workflowMetrics.avgResponseTime * 5)"
                      color="info"
                      height="6"
                    ></v-progress-linear>
                  </div>

                  <div class="mb-4">
                    <div class="d-flex justify-space-between mb-2">
                      <span>工作流自动化率</span>
                      <span class="font-weight-bold">{{ workflowMetrics.automationRate }}%</span>
                    </div>
                    <v-progress-linear
                      :model-value="workflowMetrics.automationRate"
                      color="success"
                      height="6"
                    ></v-progress-linear>
                  </div>
                </v-card-text>
              </v-card>
            </v-col>

            <!-- 优化建议 -->
            <v-col cols="12" md="6">
              <v-card variant="outlined">
                <v-card-title class="text-h6">优化建议</v-card-title>
                <v-card-text>
                  <v-list density="compact">
                    <v-list-item
                      v-for="suggestion in optimizationSuggestions"
                      :key="suggestion.id"
                      :prepend-icon="suggestion.icon"
                      :title="suggestion.title"
                      :subtitle="suggestion.description"
                      @click="applySuggestion(suggestion)"
                    >
                      <template v-slot:append>
                        <v-chip
                          :color="suggestion.impact === 'high' ? 'success' : suggestion.impact === 'medium' ? 'warning' : 'info'"
                          size="small"
                        >
                          {{ suggestion.impact === 'high' ? '高效果' : suggestion.impact === 'medium' ? '中效果' : '低效果' }}
                        </v-chip>
                      </template>
                    </v-list-item>
                  </v-list>
                </v-card-text>
              </v-card>
            </v-col>

            <!-- 工作流模板 -->
            <v-col cols="12">
              <v-card variant="outlined">
                <v-card-title class="text-h6">工作流模板</v-card-title>
                <v-card-text>
                  <v-row>
                    <v-col
                      v-for="template in workflowTemplates"
                      :key="template.id"
                      cols="12"
                      md="4"
                    >
                      <v-card
                        variant="outlined"
                        class="workflow-template-card"
                        @click="applyWorkflowTemplate(template)"
                      >
                        <v-card-text class="text-center">
                          <v-icon
                            :icon="template.icon"
                            size="48"
                            :color="template.color"
                            class="mb-2"
                          ></v-icon>
                          <div class="text-h6 mb-1">{{ template.name }}</div>
                          <div class="text-caption text-medium-emphasis">{{ template.description }}</div>
                        </v-card-text>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="grey-darken-1"
            variant="text"
            @click="showWorkflowOptimization = false"
          >
            关闭
          </v-btn>
          <v-btn
            color="primary"
            variant="text"
            @click="exportWorkflowReport"
          >
            导出报告
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 操作成功提示 -->
    <v-snackbar
      v-model="showSnackbar"
      color="success"
    >
      {{ snackbarText }}

      <template v-slot:actions>
        <v-btn
          variant="text"
          @click="showSnackbar = false"
        >
          关闭
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script>
export default {
  name: 'ProfileView',
  data() {
    return {
      editMode: false,
      valid: true,
      showUploadDialog: false,
      avatarFile: null,
      avatarPreview: null,
      showSnackbar: false,
      snackbarText: '',
      showWorkflowOptimization: false,

      nameRules: [
        v => !!v || '姓名不能为空',
        v => v.length <= 20 || '姓名不能超过20个字符'
      ],
      emailRules: [
        v => !!v || '邮箱不能为空',
        v => /.+@.+\..+/.test(v) || '邮箱格式不正确'
      ],
      phoneRules: [
        v => !!v || '手机号码不能为空',
        v => /^1[3-9]\d{9}$/.test(v) || '手机号码格式不正确'
      ],

      departments: ['技术部', '市场部', '销售部', '人力资源部', '财务部'],

      user: {
        name: '张三',
        email: '<EMAIL>',
        phone: '13800138000',
        department: '技术部',
        position: '项目经理',
        joinDate: '2023-01-15',
        address: '上海市浦东新区张江高科技园区',
        bio: '拥有8年项目管理经验，专注于软件开发领域。擅长敏捷开发方法，曾成功带领团队完成多个大型项目。',
        avatar: 'https://ui-avatars.com/api/?name=张三&background=random'
      },

      stats: {
        projects: 12,
        tasks: 156,
        meetings: 48,
        documents: 32,
        taskCompletionRate: 92,
        onTimeRate: 88
      },

      // 工作流优化相关数据
      workflowMetrics: {
        taskProcessingSpeed: 8.5,
        avgResponseTime: 2.3,
        automationRate: 75
      },

      pendingItems: [
        { id: 1, title: '项目A审批', priority: 'high', type: 'approval' },
        { id: 2, title: '任务B验收', priority: 'medium', type: 'review' },
        { id: 3, title: '会议记录确认', priority: 'low', type: 'confirmation' }
      ],

      optimizationSuggestions: [
        {
          id: 1,
          title: '启用任务自动分配',
          description: '根据团队成员工作负载自动分配新任务',
          icon: 'mdi-account-multiple-plus',
          impact: 'high',
          action: 'enable_auto_assignment'
        },
        {
          id: 2,
          title: '设置状态自动流转',
          description: '任务完成后自动触发下一阶段',
          icon: 'mdi-arrow-right-circle',
          impact: 'high',
          action: 'enable_auto_transition'
        },
        {
          id: 3,
          title: '优化通知频率',
          description: '减少非关键通知，提高工作专注度',
          icon: 'mdi-bell-outline',
          impact: 'medium',
          action: 'optimize_notifications'
        },
        {
          id: 4,
          title: '启用智能提醒',
          description: '基于历史数据预测任务延期风险',
          icon: 'mdi-brain',
          impact: 'high',
          action: 'enable_smart_reminders'
        }
      ],

      workflowTemplates: [
        {
          id: 1,
          name: '医疗项目标准流程',
          description: '适用于医疗项目的标准化工作流',
          icon: 'mdi-hospital-box',
          color: 'primary'
        },
        {
          id: 2,
          name: '敏捷开发流程',
          description: '快速迭代的敏捷开发工作流',
          icon: 'mdi-rocket-launch',
          color: 'success'
        },
        {
          id: 3,
          name: '审批密集型流程',
          description: '需要多层审批的严格工作流',
          icon: 'mdi-shield-check',
          color: 'warning'
        }
      ]
    }
  },
  computed: {
    workflowEfficiency() {
      // 基于任务完成率、按时完成率和自动化率计算工作流效率
      const taskWeight = 0.4
      const timeWeight = 0.3
      const autoWeight = 0.3

      return Math.round(
        this.stats.taskCompletionRate * taskWeight +
        this.stats.onTimeRate * timeWeight +
        this.workflowMetrics.automationRate * autoWeight
      )
    }
  },
  watch: {
    avatarFile(file) {
      if (file) {
        this.createImagePreview(file)
      } else {
        this.avatarPreview = null
      }
    }
  },
  methods: {
    saveProfile() {
      // 在实际应用中，这里会发送请求到后端保存个人资料
      this.editMode = false
      this.showSuccessMessage('个人资料保存成功')
    },
    createImagePreview(file) {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = e => {
        this.avatarPreview = e.target.result
      }
    },
    uploadAvatar() {
      // 在实际应用中，这里会发送请求到后端上传头像
      if (this.avatarPreview) {
        this.user.avatar = this.avatarPreview
      }
      this.showUploadDialog = false
      this.avatarFile = null
      this.showSuccessMessage('头像上传成功')
    },
    showSuccessMessage(text) {
      this.snackbarText = text
      this.showSnackbar = true
    },

    // 工作流优化相关方法
    getEfficiencyColor(efficiency) {
      if (efficiency >= 85) return 'success'
      if (efficiency >= 70) return 'warning'
      return 'error'
    },

    handlePendingItem(item) {
      // 处理待办事项
      switch (item.type) {
        case 'approval':
          this.$router.push('/projects')
          break
        case 'review':
          this.$router.push('/kanban')
          break
        case 'confirmation':
          this.$router.push('/meetings')
          break
      }
      this.showSuccessMessage(`正在处理：${item.title}`)
    },

    applySuggestion(suggestion) {
      // 应用优化建议
      switch (suggestion.action) {
        case 'enable_auto_assignment':
          this.enableAutoAssignment()
          break
        case 'enable_auto_transition':
          this.enableAutoTransition()
          break
        case 'optimize_notifications':
          this.optimizeNotifications()
          break
        case 'enable_smart_reminders':
          this.enableSmartReminders()
          break
      }
      this.showSuccessMessage(`已应用优化建议：${suggestion.title}`)
    },

    applyWorkflowTemplate(template) {
      // 应用工作流模板
      this.showSuccessMessage(`正在应用工作流模板：${template.name}`)
      // 这里可以调用API来应用模板
    },

    exportWorkflowReport() {
      // 导出工作流报告
      const reportData = {
        user: this.user.name,
        date: new Date().toLocaleDateString(),
        efficiency: this.workflowEfficiency,
        metrics: this.workflowMetrics,
        suggestions: this.optimizationSuggestions.length
      }

      // 模拟导出功能
      console.log('导出工作流报告:', reportData)
      this.showSuccessMessage('工作流报告已导出')
    },

    // 优化功能实现
    enableAutoAssignment() {
      // 启用自动分配功能
      this.workflowMetrics.automationRate = Math.min(100, this.workflowMetrics.automationRate + 10)
    },

    enableAutoTransition() {
      // 启用自动流转功能
      this.workflowMetrics.automationRate = Math.min(100, this.workflowMetrics.automationRate + 15)
      this.workflowMetrics.avgResponseTime = Math.max(0.5, this.workflowMetrics.avgResponseTime - 0.5)
    },

    optimizeNotifications() {
      // 优化通知设置
      this.workflowMetrics.avgResponseTime = Math.max(0.5, this.workflowMetrics.avgResponseTime - 0.3)
    },

    enableSmartReminders() {
      // 启用智能提醒
      this.workflowMetrics.taskProcessingSpeed = Math.min(15, this.workflowMetrics.taskProcessingSpeed + 1.5)
      this.stats.onTimeRate = Math.min(100, this.stats.onTimeRate + 5)
    }
  }
}
</script>

<style scoped>
.workflow-template-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.workflow-template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.workflow-template-card .v-card-text {
  padding: 24px;
}

/* 工作流效率指标样式 */
.v-progress-linear {
  border-radius: 4px;
}

/* 待处理事项样式 */
.v-chip {
  cursor: pointer;
  transition: all 0.2s ease;
}

.v-chip:hover {
  transform: scale(1.05);
}

/* 优化建议列表样式 */
.v-list-item {
  border-radius: 8px;
  margin-bottom: 8px;
  transition: background-color 0.2s ease;
}

.v-list-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .workflow-template-card .v-card-text {
    padding: 16px;
  }

  .v-icon {
    font-size: 36px !important;
  }
}
</style>
