/**
 * 工作流自动化服务
 * 提供工作流优化、自动化和智能分析功能
 */

import api from './api'

class WorkflowService {
  constructor() {
    this.automationRules = new Map()
    this.workflowTemplates = new Map()
    this.metrics = {
      taskProcessingSpeed: 0,
      avgResponseTime: 0,
      automationRate: 0,
      errorRate: 0
    }
  }

  /**
   * 工作流自动化规则管理
   */
  
  // 添加自动化规则
  addAutomationRule(rule) {
    this.automationRules.set(rule.id, {
      ...rule,
      createdAt: new Date(),
      isActive: true,
      executionCount: 0
    })
  }

  // 执行自动化规则
  async executeAutomationRule(ruleId, context) {
    const rule = this.automationRules.get(ruleId)
    if (!rule || !rule.isActive) return false

    try {
      switch (rule.type) {
        case 'task_auto_assignment':
          return await this.autoAssignTask(context)
        case 'status_auto_transition':
          return await this.autoTransitionStatus(context)
        case 'deadline_reminder':
          return await this.sendDeadlineReminder(context)
        case 'risk_auto_creation':
          return await this.autoCreateRisk(context)
        default:
          return false
      }
    } catch (error) {
      console.error('自动化规则执行失败:', error)
      return false
    }
  }

  /**
   * 任务自动分配
   */
  async autoAssignTask(context) {
    const { taskId, projectId } = context
    
    // 获取团队成员工作负载
    const teamMembers = await this.getTeamWorkload(projectId)
    
    // 选择工作负载最轻的成员
    const assignee = teamMembers.reduce((min, member) => 
      member.workload < min.workload ? member : min
    )

    // 分配任务
    return await api.put(`/tasks/${taskId}`, {
      assignee_id: assignee.id,
      assigned_at: new Date().toISOString(),
      assignment_reason: 'auto_assignment_by_workload'
    })
  }

  /**
   * 状态自动流转
   */
  async autoTransitionStatus(context) {
    const { taskId, currentStatus, projectId } = context
    
    // 获取工作流配置
    const workflow = await this.getProjectWorkflow(projectId)
    const nextStatus = workflow.getNextStatus(currentStatus)
    
    if (!nextStatus) return false

    // 检查流转条件
    const canTransition = await this.checkTransitionConditions(taskId, nextStatus)
    if (!canTransition) return false

    // 执行状态流转
    return await api.put(`/tasks/${taskId}/status`, {
      status: nextStatus,
      transition_reason: 'auto_transition',
      transition_time: new Date().toISOString()
    })
  }

  /**
   * 智能提醒系统
   */
  async sendDeadlineReminder(context) {
    const { taskId, dueDate, assigneeId } = context
    const now = new Date()
    const due = new Date(dueDate)
    const hoursUntilDue = (due - now) / (1000 * 60 * 60)

    // 根据紧急程度发送不同级别的提醒
    let reminderType = 'normal'
    if (hoursUntilDue <= 2) reminderType = 'urgent'
    else if (hoursUntilDue <= 24) reminderType = 'important'

    return await api.post('/notifications', {
      type: 'deadline_reminder',
      recipient_id: assigneeId,
      task_id: taskId,
      priority: reminderType,
      message: this.generateReminderMessage(hoursUntilDue, reminderType)
    })
  }

  /**
   * 风险自动创建
   */
  async autoCreateRisk(context) {
    const { taskId, projectId, riskType } = context
    
    const riskData = {
      project_id: projectId,
      task_id: taskId,
      title: this.generateRiskTitle(riskType),
      description: this.generateRiskDescription(context),
      probability: this.calculateRiskProbability(context),
      impact: this.calculateRiskImpact(context),
      status: 'open',
      created_by: 'system',
      auto_created: true
    }

    return await api.post('/risks', riskData)
  }

  /**
   * 工作流模板管理
   */
  
  // 创建工作流模板
  createWorkflowTemplate(template) {
    const workflowTemplate = {
      id: template.id,
      name: template.name,
      description: template.description,
      type: template.type, // medical, agile, waterfall
      stages: template.stages,
      automationRules: template.automationRules || [],
      approvalFlow: template.approvalFlow || [],
      createdAt: new Date()
    }
    
    this.workflowTemplates.set(template.id, workflowTemplate)
    return workflowTemplate
  }

  // 应用工作流模板
  async applyWorkflowTemplate(projectId, templateId) {
    const template = this.workflowTemplates.get(templateId)
    if (!template) throw new Error('工作流模板不存在')

    try {
      // 应用阶段配置
      await this.applyStageConfiguration(projectId, template.stages)
      
      // 应用自动化规则
      await this.applyAutomationRules(projectId, template.automationRules)
      
      // 应用审批流程
      await this.applyApprovalFlow(projectId, template.approvalFlow)
      
      return { success: true, message: '工作流模板应用成功' }
    } catch (error) {
      console.error('应用工作流模板失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 工作流分析和优化
   */
  
  // 分析工作流效率
  async analyzeWorkflowEfficiency(projectId, timeRange = 30) {
    const endDate = new Date()
    const startDate = new Date(endDate.getTime() - timeRange * 24 * 60 * 60 * 1000)
    
    const [tasks, meetings, risks] = await Promise.all([
      api.get(`/tasks?project_id=${projectId}&start_date=${startDate.toISOString()}&end_date=${endDate.toISOString()}`),
      api.get(`/meetings?project_id=${projectId}&start_date=${startDate.toISOString()}&end_date=${endDate.toISOString()}`),
      api.get(`/risks?project_id=${projectId}&start_date=${startDate.toISOString()}&end_date=${endDate.toISOString()}`)
    ])

    return {
      taskMetrics: this.calculateTaskMetrics(tasks.data),
      meetingMetrics: this.calculateMeetingMetrics(meetings.data),
      riskMetrics: this.calculateRiskMetrics(risks.data),
      overallEfficiency: this.calculateOverallEfficiency(tasks.data, meetings.data, risks.data),
      bottlenecks: this.identifyBottlenecks(tasks.data),
      suggestions: this.generateOptimizationSuggestions(tasks.data, meetings.data, risks.data)
    }
  }

  // 生成优化建议
  generateOptimizationSuggestions(tasks, meetings, risks) {
    const suggestions = []
    
    // 分析任务延期情况
    const overdueTasks = tasks.filter(task => new Date(task.due_date) < new Date() && task.status !== 'completed')
    if (overdueTasks.length > tasks.length * 0.2) {
      suggestions.push({
        type: 'deadline_management',
        title: '加强截止日期管理',
        description: '当前有较多任务延期，建议启用智能提醒和自动预警',
        impact: 'high',
        action: 'enable_smart_reminders'
      })
    }

    // 分析任务分配情况
    const assignmentDistribution = this.analyzeTaskAssignment(tasks)
    if (assignmentDistribution.imbalance > 0.3) {
      suggestions.push({
        type: 'workload_balance',
        title: '优化工作负载分配',
        description: '团队成员工作负载不均衡，建议启用自动分配',
        impact: 'high',
        action: 'enable_auto_assignment'
      })
    }

    // 分析会议效率
    const meetingEfficiency = this.calculateMeetingEfficiency(meetings)
    if (meetingEfficiency < 0.7) {
      suggestions.push({
        type: 'meeting_optimization',
        title: '优化会议管理',
        description: '会议效率较低，建议制定会议标准和跟踪行动项',
        impact: 'medium',
        action: 'optimize_meetings'
      })
    }

    return suggestions
  }

  /**
   * 辅助方法
   */
  
  generateReminderMessage(hoursUntilDue, type) {
    if (type === 'urgent') {
      return `⚠️ 紧急提醒：您的任务将在${Math.round(hoursUntilDue)}小时内到期，请尽快处理！`
    } else if (type === 'important') {
      return `📅 重要提醒：您的任务将在${Math.round(hoursUntilDue)}小时内到期，请及时处理。`
    }
    return `📋 友好提醒：您有任务即将到期，请关注进度。`
  }

  calculateTaskMetrics(tasks) {
    const completed = tasks.filter(t => t.status === 'completed').length
    const onTime = tasks.filter(t => t.status === 'completed' && new Date(t.completed_at) <= new Date(t.due_date)).length
    
    return {
      completionRate: tasks.length > 0 ? (completed / tasks.length) * 100 : 0,
      onTimeRate: completed > 0 ? (onTime / completed) * 100 : 0,
      avgCompletionTime: this.calculateAvgCompletionTime(tasks),
      totalTasks: tasks.length
    }
  }

  calculateAvgCompletionTime(tasks) {
    const completedTasks = tasks.filter(t => t.status === 'completed' && t.created_at && t.completed_at)
    if (completedTasks.length === 0) return 0
    
    const totalTime = completedTasks.reduce((sum, task) => {
      const created = new Date(task.created_at)
      const completed = new Date(task.completed_at)
      return sum + (completed - created)
    }, 0)
    
    return totalTime / completedTasks.length / (1000 * 60 * 60 * 24) // 转换为天数
  }

  async getTeamWorkload(projectId) {
    // 模拟获取团队工作负载数据
    return [
      { id: 1, name: '张三', workload: 0.8 },
      { id: 2, name: '李四', workload: 0.6 },
      { id: 3, name: '王五', workload: 0.9 }
    ]
  }
}

export default new WorkflowService()
