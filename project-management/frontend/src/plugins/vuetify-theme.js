/**
 * Vuetify 医疗主题配置
 * 集成Google Material Design和医疗行业特色
 */

import { createVuetify } from 'vuetify'
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'
import { mdi } from 'vuetify/iconsets/mdi'
import '@mdi/font/css/materialdesignicons.css'
import 'vuetify/styles'

// 医疗主题色彩定义
const medicalTheme = {
  dark: false,
  colors: {
    // 医疗主题色彩
    'medical-primary': '#1565C0',      // 医疗蓝
    'medical-secondary': '#2E7D32',    // 医疗绿
    'medical-accent': '#E53935',       // 医疗红
    'medical-warning': '#F57C00',      // 医疗橙
    'medical-info': '#1976D2',         // 信息蓝
    'medical-success': '#388E3C',      // 成功绿
    'medical-error': '#D32F2F',        // 错误红
    
    // Google Material Design 色彩
    'google-blue': '#4285F4',
    'google-red': '#EA4335',
    'google-yellow': '#FBBC04',
    'google-green': '#34A853',
    
    // 主要色彩映射到医疗主题
    primary: '#1565C0',
    secondary: '#2E7D32',
    accent: '#E53935',
    error: '#D32F2F',
    warning: '#F57C00',
    info: '#1976D2',
    success: '#388E3C',
    
    // 背景和表面色彩
    background: '#FAFAFA',
    surface: '#FFFFFF',
    'surface-variant': '#F5F5F5',
    'on-surface-variant': '#424242',
    
    // 文本色彩
    'on-primary': '#FFFFFF',
    'on-secondary': '#FFFFFF',
    'on-background': '#212121',
    'on-surface': '#212121',
    'on-error': '#FFFFFF',
    
    // 灰度色彩
    'grey-50': '#FAFAFA',
    'grey-100': '#F5F5F5',
    'grey-200': '#EEEEEE',
    'grey-300': '#E0E0E0',
    'grey-400': '#BDBDBD',
    'grey-500': '#9E9E9E',
    'grey-600': '#757575',
    'grey-700': '#616161',
    'grey-800': '#424242',
    'grey-900': '#212121'
  }
}

// 深色医疗主题
const medicalDarkTheme = {
  dark: true,
  colors: {
    // 医疗主题色彩 - 深色版本
    'medical-primary': '#42A5F5',      // 亮医疗蓝
    'medical-secondary': '#66BB6A',    // 亮医疗绿
    'medical-accent': '#EF5350',       // 亮医疗红
    'medical-warning': '#FFA726',      // 亮医疗橙
    'medical-info': '#42A5F5',         // 亮信息蓝
    'medical-success': '#66BB6A',      // 亮成功绿
    'medical-error': '#EF5350',        // 亮错误红
    
    // 主要色彩映射
    primary: '#42A5F5',
    secondary: '#66BB6A',
    accent: '#EF5350',
    error: '#EF5350',
    warning: '#FFA726',
    info: '#42A5F5',
    success: '#66BB6A',
    
    // 深色背景
    background: '#121212',
    surface: '#1E1E1E',
    'surface-variant': '#2D2D2D',
    'on-surface-variant': '#E0E0E0',
    
    // 深色文本
    'on-primary': '#000000',
    'on-secondary': '#000000',
    'on-background': '#FFFFFF',
    'on-surface': '#FFFFFF',
    'on-error': '#000000'
  }
}

// 组件默认配置
const componentDefaults = {
  VBtn: {
    style: 'text-transform: none; font-weight: 500;',
    rounded: 'lg'
  },
  VCard: {
    rounded: 'lg',
    elevation: 1
  },
  VTextField: {
    variant: 'outlined',
    density: 'comfortable'
  },
  VSelect: {
    variant: 'outlined',
    density: 'comfortable'
  },
  VTextarea: {
    variant: 'outlined',
    density: 'comfortable'
  },
  VChip: {
    rounded: 'lg'
  },
  VDialog: {
    rounded: 'lg'
  },
  VSheet: {
    rounded: 'lg'
  },
  VNavigationDrawer: {
    elevation: 1
  },
  VAppBar: {
    elevation: 1
  },
  VDataTable: {
    density: 'comfortable'
  }
}

// 创建Vuetify实例
export default createVuetify({
  components,
  directives,
  
  // 图标配置
  icons: {
    defaultSet: 'mdi',
    sets: {
      mdi
    }
  },
  
  // 主题配置
  theme: {
    defaultTheme: 'medicalLight',
    themes: {
      medicalLight: medicalTheme,
      medicalDark: medicalDarkTheme
    },
    variations: {
      colors: ['primary', 'secondary', 'accent', 'medical-primary', 'medical-secondary'],
      lighten: 5,
      darken: 5
    }
  },
  
  // 默认配置
  defaults: componentDefaults,
  
  // 显示配置
  display: {
    mobileBreakpoint: 'sm',
    thresholds: {
      xs: 0,
      sm: 600,
      md: 960,
      lg: 1280,
      xl: 1920
    }
  },
  
  // 本地化配置
  locale: {
    locale: 'zh-CN',
    fallback: 'en',
    messages: {
      'zh-CN': {
        // 可以在这里添加中文本地化消息
      }
    }
  }
})

// 医疗主题工具函数
export const medicalThemeUtils = {
  // 获取状态颜色
  getStatusColor(status) {
    const statusColors = {
      'completed': 'medical-success',
      'in-progress': 'medical-info',
      'pending': 'medical-warning',
      'delayed': 'medical-error',
      'cancelled': 'grey-500',
      'approved': 'medical-success',
      'rejected': 'medical-error',
      'under-review': 'medical-warning'
    }
    return statusColors[status] || 'grey-500'
  },
  
  // 获取优先级颜色
  getPriorityColor(priority) {
    const priorityColors = {
      'urgent': 'medical-error',
      'high': 'medical-accent',
      'medium': 'medical-warning',
      'low': 'medical-info',
      'normal': 'grey-500'
    }
    return priorityColors[priority] || 'grey-500'
  },
  
  // 获取医疗项目阶段颜色
  getMedicalPhaseColor(phase) {
    const phaseColors = {
      'planning': 'medical-info',
      'design': 'medical-warning',
      'development': 'medical-primary',
      'testing': 'medical-accent',
      'deployment': 'medical-secondary',
      'maintenance': 'medical-success',
      'completed': 'medical-success'
    }
    return phaseColors[phase] || 'grey-500'
  },
  
  // 获取风险等级颜色
  getRiskLevelColor(level) {
    const riskColors = {
      'critical': 'medical-error',
      'high': 'medical-accent',
      'medium': 'medical-warning',
      'low': 'medical-info',
      'minimal': 'medical-success'
    }
    return riskColors[level] || 'grey-500'
  },
  
  // 生成渐变样式
  generateGradient(color1, color2, direction = '135deg') {
    return `linear-gradient(${direction}, var(--v-theme-${color1}), var(--v-theme-${color2}))`
  },
  
  // 生成医疗主题卡片样式
  getMedicalCardStyle(type = 'default') {
    const styles = {
      'default': {
        borderRadius: '12px',
        boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
        border: '1px solid #E0E0E0'
      },
      'elevated': {
        borderRadius: '12px',
        boxShadow: '0 4px 8px rgba(0,0,0,0.12), 0 2px 4px rgba(0,0,0,0.08)',
        border: 'none'
      },
      'outlined': {
        borderRadius: '12px',
        boxShadow: 'none',
        border: '2px solid #E0E0E0'
      }
    }
    return styles[type] || styles.default
  }
}

// 医疗主题常量
export const MEDICAL_THEME_CONSTANTS = {
  // 医疗项目状态
  PROJECT_STATUSES: [
    { value: 'planning', text: '规划中', color: 'medical-info' },
    { value: 'in-progress', text: '进行中', color: 'medical-primary' },
    { value: 'testing', text: '测试中', color: 'medical-warning' },
    { value: 'completed', text: '已完成', color: 'medical-success' },
    { value: 'on-hold', text: '暂停', color: 'grey-500' },
    { value: 'cancelled', text: '已取消', color: 'medical-error' }
  ],
  
  // 任务优先级
  TASK_PRIORITIES: [
    { value: 'urgent', text: '紧急', color: 'medical-error' },
    { value: 'high', text: '高', color: 'medical-accent' },
    { value: 'medium', text: '中', color: 'medical-warning' },
    { value: 'low', text: '低', color: 'medical-info' }
  ],
  
  // 风险等级
  RISK_LEVELS: [
    { value: 'critical', text: '严重', color: 'medical-error' },
    { value: 'high', text: '高风险', color: 'medical-accent' },
    { value: 'medium', text: '中风险', color: 'medical-warning' },
    { value: 'low', text: '低风险', color: 'medical-info' },
    { value: 'minimal', text: '轻微', color: 'medical-success' }
  ],
  
  // 医疗项目阶段
  MEDICAL_PHASES: [
    { value: 'requirement', text: '需求分析', color: 'medical-info' },
    { value: 'design', text: '系统设计', color: 'medical-warning' },
    { value: 'development', text: '开发实施', color: 'medical-primary' },
    { value: 'integration', text: '系统集成', color: 'medical-accent' },
    { value: 'testing', text: '测试验收', color: 'medical-secondary' },
    { value: 'deployment', text: '部署上线', color: 'medical-success' },
    { value: 'training', text: '培训支持', color: 'medical-info' },
    { value: 'maintenance', text: '运维支持', color: 'medical-success' }
  ]
}
