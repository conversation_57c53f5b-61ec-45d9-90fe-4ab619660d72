import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import store from './store'
import vuetify from './plugins/vuetify'
import './assets/styles/main.css'
import './styles/medical-theme.css' // 引入医疗主题样式
import './utils/dataInspector' // 引入数据检查工具

// 创建Vue应用实例
const app = createApp(App)
const pinia = createPinia()

// 使用插件
app.use(pinia)
app.use(store)
app.use(router)
app.use(vuetify)

// 初始化认证状态
store.dispatch('auth/initAuth')

// 如果没有认证，自动登录一个演示用户
if (!store.getters['auth/isAuthenticated']) {
  store.dispatch('auth/login', { username: 'demo' })
}

// 挂载应用
app.mount('#app')
