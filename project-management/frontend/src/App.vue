<template>
  <v-app class="medical-app">
    <v-layout class="google-layout">
      <!-- 顶部导航栏 - Google风格 -->
      <v-app-bar
        color="white"
        app
        flat
        class="google-header"
        elevation="1"
      >
        <v-app-bar-nav-icon
          @click="drawer = !drawer"
          class="google-menu-icon"
        >
          <v-icon color="grey-darken-1">mdi-menu</v-icon>
        </v-app-bar-nav-icon>

        <!-- 医疗项目管理系统标题 -->
        <div class="d-flex align-center">
          <v-icon
            icon="mdi-hospital-box"
            color="medical-primary"
            size="32"
            class="me-3"
          ></v-icon>
          <div>
            <div class="google-title">医疗项目管理</div>
            <div class="google-subtitle">Healthcare Project Management</div>
          </div>
        </div>

        <v-spacer></v-spacer>

        <!-- 搜索框 - Google风格 -->
        <v-text-field
          v-model="searchQuery"
          placeholder="搜索项目、任务或文档..."
          prepend-inner-icon="mdi-magnify"
          variant="outlined"
          density="compact"
          hide-details
          class="google-search me-4"
          style="max-width: 400px;"
        ></v-text-field>

        <!-- 通知按钮 -->
        <v-btn
          icon
          variant="text"
          class="google-icon-btn me-2"
          @click="showNotifications = true"
        >
          <v-badge
            :content="notificationCount"
            :model-value="notificationCount > 0"
            color="medical-accent"
          >
            <v-icon color="grey-darken-1">mdi-bell-outline</v-icon>
          </v-badge>
        </v-btn>

        <!-- 用户菜单 -->
        <v-menu offset-y>
          <template v-slot:activator="{ props }">
            <v-btn
              icon
              variant="text"
              class="google-icon-btn"
              v-bind="props"
            >
              <v-avatar size="32" color="medical-primary">
                <v-icon color="white">mdi-account</v-icon>
              </v-avatar>
            </v-btn>
          </template>
          <v-card class="google-menu" min-width="280">
            <v-card-text class="pa-4">
              <div class="d-flex align-center mb-3">
                <v-avatar size="48" color="medical-primary" class="me-3">
                  <v-icon color="white" size="24">mdi-account</v-icon>
                </v-avatar>
                <div>
                  <div class="text-h6">{{ currentUser.name || '用户' }}</div>
                  <div class="text-caption text-medium-emphasis">{{ currentUser.email || '<EMAIL>' }}</div>
                </div>
              </div>
              <v-divider class="mb-3"></v-divider>
              <v-list density="compact" class="pa-0">
                <v-list-item
                  @click="navigateTo('/profile')"
                  prepend-icon="mdi-account-circle"
                  class="google-menu-item"
                >
                  <v-list-item-title>个人资料</v-list-item-title>
                </v-list-item>
                <v-list-item
                  @click="navigateTo('/settings')"
                  prepend-icon="mdi-cog"
                  class="google-menu-item"
                >
                  <v-list-item-title>系统设置</v-list-item-title>
                </v-list-item>
                <v-divider class="my-2"></v-divider>
                <v-list-item
                  @click="logout"
                  prepend-icon="mdi-logout"
                  class="google-menu-item"
                >
                  <v-list-item-title>退出登录</v-list-item-title>
                </v-list-item>
              </v-list>
            </v-card-text>
          </v-card>
        </v-menu>
      </v-app-bar>

      <!-- 侧边导航菜单 - Google风格 -->
      <v-navigation-drawer
        v-model="drawer"
        app
        class="google-drawer"
        width="280"
      >
        <!-- 导航头部 -->
        <div class="google-drawer-header">
          <div class="d-flex align-center pa-4">
            <v-icon
              icon="mdi-hospital-box"
              color="medical-primary"
              size="24"
              class="me-3"
            ></v-icon>
            <div>
              <div class="text-subtitle-1 font-weight-medium">医疗项目管理</div>
              <div class="text-caption text-medium-emphasis">Healthcare PM</div>
            </div>
          </div>
        </div>

        <v-divider></v-divider>

        <!-- 主要导航 -->
        <v-list class="google-nav-list" nav>
          <!-- 概览部分 -->
          <v-list-subheader class="google-nav-subheader">
            <v-icon icon="mdi-view-dashboard" size="16" class="me-2"></v-icon>
            概览
          </v-list-subheader>

          <v-list-item
            to="/"
            prepend-icon="mdi-view-dashboard"
            class="google-nav-item"
            color="medical-primary"
          >
            <v-list-item-title>仪表盘</v-list-item-title>
            <template v-slot:append>
              <v-chip size="x-small" color="medical-accent" variant="flat">NEW</v-chip>
            </template>
          </v-list-item>

          <!-- 项目管理部分 -->
          <v-list-subheader class="google-nav-subheader">
            <v-icon icon="mdi-hospital-building" size="16" class="me-2"></v-icon>
            医疗项目
          </v-list-subheader>

          <v-list-item
            to="/projects"
            prepend-icon="mdi-hospital-building"
            class="google-nav-item"
            color="medical-primary"
          >
            <v-list-item-title>项目管理</v-list-item-title>
          </v-list-item>

          <v-list-item
            to="/gantt"
            prepend-icon="mdi-chart-timeline-variant"
            class="google-nav-item"
            color="medical-primary"
          >
            <v-list-item-title>项目进度</v-list-item-title>
            <v-list-item-subtitle>甘特图</v-list-item-subtitle>
          </v-list-item>

          <v-list-item
            to="/modern-kanban"
            prepend-icon="mdi-view-column"
            class="google-nav-item"
            color="medical-primary"
          >
            <v-list-item-title>项目看板</v-list-item-title>
            <v-list-item-subtitle>敏捷管理</v-list-item-subtitle>
          </v-list-item>

          <!-- 任务与协作 -->
          <v-list-subheader class="google-nav-subheader">
            <v-icon icon="mdi-account-group" size="16" class="me-2"></v-icon>
            任务协作
          </v-list-subheader>

          <v-list-item
            to="/tasks"
            prepend-icon="mdi-clipboard-check-multiple"
            class="google-nav-item"
            color="medical-primary"
          >
            <v-list-item-title>任务管理</v-list-item-title>
          </v-list-item>

          <v-list-item
            to="/meetings"
            prepend-icon="mdi-video-account"
            class="google-nav-item"
            color="medical-primary"
          >
            <v-list-item-title>会议记录</v-list-item-title>
            <v-list-item-subtitle>团队协作</v-list-item-subtitle>
          </v-list-item>

          <!-- 质量与风险 -->
          <v-list-subheader class="google-nav-subheader">
            <v-icon icon="mdi-shield-check" size="16" class="me-2"></v-icon>
            质量控制
          </v-list-subheader>

          <v-list-item
            to="/risks"
            prepend-icon="mdi-alert-circle"
            class="google-nav-item"
            color="medical-primary"
          >
            <v-list-item-title>风险管理</v-list-item-title>
            <v-list-item-subtitle>质量保障</v-list-item-subtitle>
          </v-list-item>

          <!-- 分析与报告 -->
          <v-list-subheader class="google-nav-subheader">
            <v-icon icon="mdi-chart-line" size="16" class="me-2"></v-icon>
            数据分析
          </v-list-subheader>

          <v-list-item
            to="/reports"
            prepend-icon="mdi-chart-bar"
            class="google-nav-item"
            color="medical-primary"
          >
            <v-list-item-title>报表分析</v-list-item-title>
            <v-list-item-subtitle>数据洞察</v-list-item-subtitle>
          </v-list-item>
        </v-list>

        <!-- 底部快捷操作 -->
        <template v-slot:append>
          <div class="google-drawer-footer">
            <v-divider class="mb-2"></v-divider>
            <v-list density="compact">
              <v-list-item
                prepend-icon="mdi-help-circle"
                class="google-nav-item"
                @click="showHelp = true"
              >
                <v-list-item-title>帮助中心</v-list-item-title>
              </v-list-item>
              <v-list-item
                prepend-icon="mdi-cog"
                class="google-nav-item"
                to="/settings"
              >
                <v-list-item-title>系统设置</v-list-item-title>
              </v-list-item>
            </v-list>
          </div>
        </template>
      </v-navigation-drawer>

      <!-- 主内容区域 - Google风格 -->
      <v-main class="google-main">
        <div class="google-content">
          <router-view></router-view>
        </div>
      </v-main>

      <!-- 页脚 - Google风格 -->
      <v-footer
        app
        class="google-footer"
        color="white"
        elevation="1"
      >
        <div class="d-flex align-center justify-space-between w-100 px-4">
          <div class="d-flex align-center">
            <v-icon
              icon="mdi-hospital-box"
              color="medical-primary"
              size="20"
              class="me-2"
            ></v-icon>
            <span class="text-body-2 text-medium-emphasis">
              &copy; {{ new Date().getFullYear() }} 医疗项目管理系统 - Healthcare Project Management
            </span>
          </div>
          <div class="d-flex align-center">
            <v-chip
              size="small"
              color="medical-success"
              variant="flat"
              class="me-2"
            >
              <v-icon start icon="mdi-check-circle"></v-icon>
              系统正常
            </v-chip>
            <span class="text-caption text-medium-emphasis">
              版本 v2.1.0
            </span>
          </div>
        </div>
      </v-footer>
    </v-layout>

    <!-- 通知面板 - Google风格 -->
    <v-navigation-drawer
      v-model="showNotifications"
      location="right"
      temporary
      width="400"
      class="google-notification-panel"
    >
      <div class="google-notification-header">
        <div class="d-flex align-center justify-space-between pa-4">
          <div class="d-flex align-center">
            <v-icon icon="mdi-bell" color="medical-primary" class="me-2"></v-icon>
            <span class="text-h6">通知中心</span>
          </div>
          <v-btn
            icon
            variant="text"
            size="small"
            @click="showNotifications = false"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </div>
      </div>

      <v-divider></v-divider>

      <div class="google-notification-content">
        <v-list>
          <v-list-item
            v-for="notification in notifications"
            :key="notification.id"
            class="google-notification-item"
            @click="handleNotificationClick(notification)"
          >
            <template v-slot:prepend>
              <v-avatar
                :color="getNotificationColor(notification.type)"
                size="40"
              >
                <v-icon
                  :icon="getNotificationIcon(notification.type)"
                  color="white"
                ></v-icon>
              </v-avatar>
            </template>

            <v-list-item-title class="text-wrap">
              {{ notification.title }}
            </v-list-item-title>
            <v-list-item-subtitle class="text-wrap">
              {{ notification.message }}
            </v-list-item-subtitle>

            <template v-slot:append>
              <div class="text-caption text-medium-emphasis">
                {{ formatTime(notification.time) }}
              </div>
            </template>
          </v-list-item>
        </v-list>

        <div v-if="notifications.length === 0" class="text-center pa-8">
          <v-icon
            icon="mdi-bell-off"
            size="64"
            color="grey-lighten-2"
            class="mb-4"
          ></v-icon>
          <div class="text-h6 text-medium-emphasis mb-2">暂无通知</div>
          <div class="text-body-2 text-medium-emphasis">
            所有通知都已处理完毕
          </div>
        </div>
      </div>
    </v-navigation-drawer>

    <!-- 帮助对话框 -->
    <v-dialog v-model="showHelp" max-width="600">
      <v-card class="google-help-dialog">
        <v-card-title class="d-flex align-center">
          <v-icon icon="mdi-help-circle" color="medical-primary" class="me-2"></v-icon>
          帮助中心
        </v-card-title>
        <v-card-text>
          <div class="mb-4">
            <h3 class="text-h6 mb-2">快速入门</h3>
            <p class="text-body-2 text-medium-emphasis">
              欢迎使用医疗项目管理系统！这里是一些快速入门指南：
            </p>
          </div>

          <v-list density="compact">
            <v-list-item prepend-icon="mdi-hospital-building">
              <v-list-item-title>创建医疗项目</v-list-item-title>
              <v-list-item-subtitle>在项目管理中创建新的医疗实施项目</v-list-item-subtitle>
            </v-list-item>
            <v-list-item prepend-icon="mdi-clipboard-check-multiple">
              <v-list-item-title>管理任务</v-list-item-title>
              <v-list-item-subtitle>分配和跟踪项目任务进度</v-list-item-subtitle>
            </v-list-item>
            <v-list-item prepend-icon="mdi-chart-timeline-variant">
              <v-list-item-title>查看进度</v-list-item-title>
              <v-list-item-subtitle>使用甘特图和看板查看项目进度</v-list-item-subtitle>
            </v-list-item>
          </v-list>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="medical-primary" @click="showHelp = false">
            了解了
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 通知组件 -->
    <NotificationBar />
  </v-app>
</template>

<script>
import NotificationBar from '@/components/common/NotificationBar.vue'

export default {
  name: 'App',
  components: {
    NotificationBar
  },
  data() {
    return {
      drawer: true,
      searchQuery: '',
      showNotifications: false,
      showHelp: false,

      // 模拟通知数据
      notifications: [
        {
          id: 1,
          type: 'task',
          title: '任务即将到期',
          message: '医院A系统集成任务将在2小时后到期',
          time: new Date(Date.now() - 1000 * 60 * 30) // 30分钟前
        },
        {
          id: 2,
          type: 'project',
          title: '项目状态更新',
          message: '医院B数据迁移项目已完成第二阶段',
          time: new Date(Date.now() - 1000 * 60 * 60 * 2) // 2小时前
        },
        {
          id: 3,
          type: 'meeting',
          title: '会议提醒',
          message: '项目评审会议将在明天上午10:00开始',
          time: new Date(Date.now() - 1000 * 60 * 60 * 4) // 4小时前
        }
      ]
    }
  },

  computed: {
    currentUser() {
      return this.$store.getters['auth/user'] || {}
    },

    notificationCount() {
      return this.notifications.length
    }
  },

  methods: {
    navigateTo(route) {
      this.$router.push(route)
    },

    logout() {
      // 清除认证信息
      this.$store.dispatch('auth/logout')
      // 重定向到登录页
      this.$router.push('/login')
    },

    handleNotificationClick(notification) {
      // 根据通知类型跳转到相应页面
      switch (notification.type) {
        case 'task':
          this.$router.push('/tasks')
          break
        case 'project':
          this.$router.push('/projects')
          break
        case 'meeting':
          this.$router.push('/meetings')
          break
      }
      this.showNotifications = false
    },

    getNotificationColor(type) {
      const colors = {
        'task': 'medical-warning',
        'project': 'medical-primary',
        'meeting': 'medical-info',
        'risk': 'medical-error'
      }
      return colors[type] || 'grey'
    },

    getNotificationIcon(type) {
      const icons = {
        'task': 'mdi-clipboard-check',
        'project': 'mdi-hospital-building',
        'meeting': 'mdi-calendar',
        'risk': 'mdi-alert-circle'
      }
      return icons[type] || 'mdi-information'
    },

    formatTime(time) {
      const now = new Date()
      const diff = now - time
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (minutes < 60) {
        return `${minutes}分钟前`
      } else if (hours < 24) {
        return `${hours}小时前`
      } else {
        return `${days}天前`
      }
    }
  }
}
</script>

<style>
/* 医疗项目管理系统 - Google风格样式 */

/* CSS变量定义 - 医疗主题色彩 */
:root {
  /* 医疗主题色彩 */
  --medical-primary: #1565C0;        /* 医疗蓝 */
  --medical-secondary: #2E7D32;      /* 医疗绿 */
  --medical-accent: #E53935;         /* 医疗红 */
  --medical-warning: #F57C00;        /* 医疗橙 */
  --medical-info: #1976D2;           /* 信息蓝 */
  --medical-success: #388E3C;        /* 成功绿 */
  --medical-error: #D32F2F;          /* 错误红 */

  /* Google风格色彩 */
  --google-blue: #4285F4;
  --google-red: #EA4335;
  --google-yellow: #FBBC04;
  --google-green: #34A853;
  --google-grey-50: #FAFAFA;
  --google-grey-100: #F5F5F5;
  --google-grey-200: #EEEEEE;
  --google-grey-300: #E0E0E0;
  --google-grey-400: #BDBDBD;
  --google-grey-500: #9E9E9E;
  --google-grey-600: #757575;
  --google-grey-700: #616161;
  --google-grey-800: #424242;
  --google-grey-900: #212121;

  /* 阴影定义 */
  --google-shadow-1: 0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15);
  --google-shadow-2: 0 1px 2px 0 rgba(60,64,67,.3), 0 2px 6px 2px rgba(60,64,67,.15);
  --google-shadow-3: 0 4px 8px 3px rgba(60,64,67,.15), 0 1px 3px rgba(60,64,67,.3);
  --google-shadow-4: 0 6px 10px 4px rgba(60,64,67,.15), 0 2px 3px rgba(60,64,67,.3);

  /* 圆角定义 */
  --google-radius-small: 4px;
  --google-radius-medium: 8px;
  --google-radius-large: 12px;
  --google-radius-xl: 16px;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Google Sans', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
  margin: 0;
  padding: 0;
  background-color: var(--google-grey-50);
  color: var(--google-grey-800);
  line-height: 1.5;
}

/* 医疗应用主容器 */
.medical-app {
  background-color: var(--google-grey-50);
}

.google-layout {
  min-height: 100vh;
}

/* Google风格头部导航 */
.google-header {
  border-bottom: 1px solid var(--google-grey-200);
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.95) !important;
}

.google-menu-icon {
  border-radius: var(--google-radius-medium);
  transition: background-color 0.2s ease;
}

.google-menu-icon:hover {
  background-color: var(--google-grey-100);
}

.google-title {
  font-size: 1.375rem;
  font-weight: 500;
  color: var(--medical-primary);
  line-height: 1.2;
}

.google-subtitle {
  font-size: 0.75rem;
  color: var(--google-grey-600);
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Google风格搜索框 */
.google-search .v-field {
  border-radius: var(--google-radius-large) !important;
  background-color: var(--google-grey-100);
  border: none !important;
  box-shadow: none !important;
}

.google-search .v-field:hover {
  background-color: var(--google-grey-200);
  box-shadow: var(--google-shadow-1) !important;
}

.google-search .v-field--focused {
  background-color: white !important;
  box-shadow: var(--google-shadow-2) !important;
}

/* Google风格图标按钮 */
.google-icon-btn {
  border-radius: var(--google-radius-medium) !important;
  transition: all 0.2s ease !important;
}

.google-icon-btn:hover {
  background-color: var(--google-grey-100) !important;
}

/* Google风格菜单 */
.google-menu {
  border-radius: var(--google-radius-medium) !important;
  box-shadow: var(--google-shadow-3) !important;
  border: 1px solid var(--google-grey-200);
}

.google-menu-item {
  border-radius: var(--google-radius-small) !important;
  margin: 2px 0 !important;
  transition: background-color 0.2s ease !important;
}

.google-menu-item:hover {
  background-color: var(--google-grey-100) !important;
}

/* Google风格侧边导航 */
.google-drawer {
  border-right: 1px solid var(--google-grey-200) !important;
  background-color: white !important;
}

.google-drawer-header {
  background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-info) 100%);
  color: white;
}

.google-drawer-header .text-subtitle-1 {
  color: white !important;
}

.google-drawer-header .text-caption {
  color: rgba(255, 255, 255, 0.8) !important;
}

.google-nav-list {
  padding: 8px !important;
}

.google-nav-subheader {
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  color: var(--google-grey-600) !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  padding: 16px 16px 8px 16px !important;
  margin-top: 8px !important;
}

.google-nav-item {
  border-radius: var(--google-radius-medium) !important;
  margin: 2px 0 !important;
  transition: all 0.2s ease !important;
  font-weight: 500 !important;
}

.google-nav-item:hover {
  background-color: rgba(21, 101, 192, 0.08) !important;
  transform: translateX(2px);
}

.google-nav-item.v-list-item--active {
  background-color: rgba(21, 101, 192, 0.12) !important;
  color: var(--medical-primary) !important;
}

.google-nav-item .v-list-item-subtitle {
  font-size: 0.75rem !important;
  color: var(--google-grey-500) !important;
}

.google-drawer-footer {
  padding: 8px;
  background-color: var(--google-grey-50);
}

/* Google风格主内容区域 */
.google-main {
  background-color: var(--google-grey-50) !important;
}

.google-content {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Google风格页脚 */
.google-footer {
  border-top: 1px solid var(--google-grey-200) !important;
  background-color: white !important;
  min-height: 48px !important;
}

/* Google风格通知面板 */
.google-notification-panel {
  background-color: white !important;
  box-shadow: var(--google-shadow-4) !important;
}

.google-notification-header {
  background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-info) 100%);
  color: white;
}

.google-notification-header .text-h6 {
  color: white !important;
}

.google-notification-content {
  max-height: calc(100vh - 120px);
  overflow-y: auto;
}

.google-notification-item {
  border-radius: var(--google-radius-medium) !important;
  margin: 4px 8px !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
}

.google-notification-item:hover {
  background-color: var(--google-grey-100) !important;
  transform: translateX(2px);
}

/* Google风格帮助对话框 */
.google-help-dialog {
  border-radius: var(--google-radius-large) !important;
  box-shadow: var(--google-shadow-4) !important;
}

/* 医疗主题状态徽章 */
.medical-status-badge {
  border-radius: var(--google-radius-large);
  padding: 4px 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.medical-status-badge.completed {
  background-color: var(--medical-success);
  color: white;
}

.medical-status-badge.in-progress {
  background-color: var(--medical-info);
  color: white;
}

.medical-status-badge.pending {
  background-color: var(--medical-warning);
  color: white;
}

.medical-status-badge.delayed {
  background-color: var(--medical-error);
  color: white;
}

.medical-status-badge.approved {
  background-color: var(--medical-success);
  color: white;
}

.medical-status-badge.rejected {
  background-color: var(--medical-error);
  color: white;
}

/* 医疗主题卡片 */
.medical-card {
  border-radius: var(--google-radius-large) !important;
  box-shadow: var(--google-shadow-1) !important;
  border: 1px solid var(--google-grey-200) !important;
  transition: all 0.3s ease !important;
}

.medical-card:hover {
  box-shadow: var(--google-shadow-2) !important;
  transform: translateY(-2px);
}

.medical-card-header {
  background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-info) 100%);
  color: white;
  border-radius: var(--google-radius-large) var(--google-radius-large) 0 0 !important;
}

/* 医疗主题按钮 */
.medical-btn-primary {
  background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-info) 100%) !important;
  color: white !important;
  border-radius: var(--google-radius-medium) !important;
  font-weight: 600 !important;
  text-transform: none !important;
  box-shadow: var(--google-shadow-1) !important;
  transition: all 0.2s ease !important;
}

.medical-btn-primary:hover {
  box-shadow: var(--google-shadow-2) !important;
  transform: translateY(-1px);
}

.medical-btn-secondary {
  background-color: var(--medical-secondary) !important;
  color: white !important;
  border-radius: var(--google-radius-medium) !important;
  font-weight: 600 !important;
  text-transform: none !important;
}

/* 医疗主题图标 */
.medical-icon {
  color: var(--medical-primary);
  filter: drop-shadow(0 1px 2px rgba(21, 101, 192, 0.2));
}

.medical-icon-success {
  color: var(--medical-success);
}

.medical-icon-warning {
  color: var(--medical-warning);
}

.medical-icon-error {
  color: var(--medical-error);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .google-content {
    padding: 16px;
  }

  .google-drawer {
    width: 280px !important;
  }

  .google-search {
    display: none !important;
  }

  .google-title {
    font-size: 1.125rem;
  }

  .google-subtitle {
    display: none;
  }
}

@media (max-width: 480px) {
  .google-content {
    padding: 12px;
  }

  .google-notification-panel {
    width: 100vw !important;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--google-grey-100);
  border-radius: var(--google-radius-small);
}

::-webkit-scrollbar-thumb {
  background: var(--google-grey-400);
  border-radius: var(--google-radius-small);
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--google-grey-500);
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.3s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

/* 医疗主题渐变背景 */
.medical-gradient-bg {
  background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-info) 50%, var(--medical-secondary) 100%);
}

.medical-gradient-text {
  background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-info) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}
</style>
