# 🏥 医疗项目管理系统设计系统

## 📋 概述

本设计系统结合了Google Material Design的现代化设计理念与医疗行业的专业需求，为医疗项目管理系统提供一致、直观、专业的用户界面体验。

## 🎨 设计理念

### 核心原则
1. **专业性** - 体现医疗行业的严谨和专业
2. **清晰性** - 信息层次分明，易于理解
3. **效率性** - 优化工作流程，提升操作效率
4. **安全性** - 强调数据安全和操作可靠性
5. **一致性** - 保持界面元素的统一性

### Google风格特色
- **简洁明了** - 去除不必要的装饰元素
- **层次分明** - 使用阴影和层级表达信息架构
- **响应式设计** - 适配各种设备和屏幕尺寸
- **微交互** - 提供流畅的用户交互体验

## 🎯 色彩系统

### 主色调 (Primary Colors)
```css
--medical-primary: #1565C0        /* 医疗蓝 - 主要操作和品牌色 */
--medical-primary-light: #42A5F5   /* 浅医疗蓝 - 悬停状态 */
--medical-primary-dark: #0D47A1    /* 深医疗蓝 - 按下状态 */
```

### 辅助色调 (Secondary Colors)
```css
--medical-secondary: #2E7D32      /* 医疗绿 - 成功和健康 */
--medical-accent: #E53935         /* 医疗红 - 紧急和警告 */
--medical-warning: #F57C00        /* 医疗橙 - 注意和提醒 */
--medical-info: #1976D2           /* 信息蓝 - 信息提示 */
```

### 状态色彩 (Status Colors)
```css
--medical-success: #388E3C        /* 成功绿 - 完成状态 */
--medical-error: #D32F2F          /* 错误红 - 错误状态 */
```

### 中性色彩 (Neutral Colors)
```css
--google-grey-50: #FAFAFA         /* 背景色 */
--google-grey-100: #F5F5F5        /* 浅背景 */
--google-grey-200: #EEEEEE        /* 边框色 */
--google-grey-600: #757575        /* 次要文字 */
--google-grey-800: #424242        /* 主要文字 */
```

## 📝 字体系统

### 字体族
```css
--medical-font-family: 'Google Sans', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
```

### 字体大小
- **标题层级**
  - H1: 3rem (48px) - 页面主标题
  - H2: 2.25rem (36px) - 区域标题
  - H3: 1.875rem (30px) - 卡片标题
  - H4: 1.5rem (24px) - 子标题
  - H5: 1.25rem (20px) - 小标题
  - H6: 1.125rem (18px) - 最小标题

- **正文层级**
  - Large: 1.125rem (18px) - 重要正文
  - Base: 1rem (16px) - 标准正文
  - Small: 0.875rem (14px) - 次要正文
  - XSmall: 0.75rem (12px) - 辅助信息

### 字重
- Light: 300 - 装饰性文字
- Normal: 400 - 正文
- Medium: 500 - 强调文字
- Semibold: 600 - 小标题
- Bold: 700 - 标题

## 🔲 间距系统

### 基础间距单位
```css
--medical-space-1: 4px    /* 最小间距 */
--medical-space-2: 8px    /* 小间距 */
--medical-space-3: 12px   /* 中小间距 */
--medical-space-4: 16px   /* 标准间距 */
--medical-space-6: 24px   /* 大间距 */
--medical-space-8: 32px   /* 较大间距 */
--medical-space-12: 48px  /* 大间距 */
--medical-space-16: 64px  /* 超大间距 */
```

### 应用场景
- **组件内间距**: 4px, 8px, 12px
- **组件间间距**: 16px, 24px
- **区域间间距**: 32px, 48px
- **页面级间距**: 64px, 96px

## 🔘 圆角系统

```css
--medical-radius-sm: 4px    /* 小圆角 - 按钮、标签 */
--medical-radius-md: 8px    /* 中圆角 - 输入框、卡片 */
--medical-radius-lg: 12px   /* 大圆角 - 对话框、面板 */
--medical-radius-xl: 16px   /* 超大圆角 - 特殊容器 */
--medical-radius-full: 9999px /* 完全圆角 - 头像、徽章 */
```

## 🌟 阴影系统

### 层级定义
```css
--medical-shadow-1: 0 1px 2px rgba(21, 101, 192, 0.1)     /* 轻微提升 */
--medical-shadow-2: 0 2px 6px rgba(21, 101, 192, 0.1)     /* 标准提升 */
--medical-shadow-3: 0 4px 8px rgba(21, 101, 192, 0.1)     /* 明显提升 */
--medical-shadow-4: 0 6px 10px rgba(21, 101, 192, 0.1)    /* 强烈提升 */
```

### 使用场景
- **Level 1**: 按钮、输入框
- **Level 2**: 卡片、菜单
- **Level 3**: 对话框、抽屉
- **Level 4**: 模态框、浮层

## 🧩 组件规范

### 按钮 (Buttons)
#### 变体类型
- **Primary**: 主要操作按钮
- **Secondary**: 次要操作按钮
- **Outlined**: 边框按钮
- **Text**: 文字按钮
- **Icon**: 图标按钮

#### 医疗特定类型
- **Emergency**: 紧急操作 (红色脉冲动画)
- **Critical**: 关键操作 (红色边框)
- **Routine**: 常规操作 (标准蓝色)
- **Diagnostic**: 诊断操作 (信息蓝色)

#### 尺寸规格
- **Small**: 32px 高度
- **Default**: 40px 高度
- **Large**: 48px 高度

### 卡片 (Cards)
#### 变体类型
- **Default**: 标准卡片
- **Primary**: 主要信息卡片
- **Secondary**: 次要信息卡片
- **Outlined**: 边框卡片
- **Elevated**: 提升卡片

#### 状态指示
- **Active**: 进行中状态
- **Pending**: 待处理状态
- **Completed**: 已完成状态
- **Error**: 错误状态
- **Warning**: 警告状态

### 表单元素 (Form Elements)
#### 输入框
- **Outlined**: 边框样式 (推荐)
- **Filled**: 填充样式
- **Underlined**: 下划线样式

#### 选择器
- **Dropdown**: 下拉选择
- **Autocomplete**: 自动完成
- **Multi-select**: 多选

## 🎭 图标系统

### 图标库
使用 Material Design Icons (MDI) 作为主要图标库

### 医疗专用图标
- `mdi-hospital-box`: 医院/医疗机构
- `mdi-hospital-building`: 医院建筑
- `mdi-stethoscope`: 听诊器/医疗
- `mdi-heart-pulse`: 心率/生命体征
- `mdi-medical-bag`: 医疗包
- `mdi-pill`: 药物
- `mdi-clipboard-pulse`: 医疗记录
- `mdi-account-injury`: 患者
- `mdi-shield-cross`: 医疗安全

### 项目管理图标
- `mdi-chart-timeline-variant`: 甘特图
- `mdi-view-column`: 看板
- `mdi-clipboard-check-multiple`: 任务管理
- `mdi-alert-circle`: 风险管理
- `mdi-calendar-account`: 会议安排

## 🎨 主题应用

### 页面布局
```vue
<template>
  <div class="medical-page">
    <div class="medical-page-header">
      <!-- 页面头部 -->
    </div>
    <div class="medical-page-content">
      <!-- 页面内容 -->
    </div>
    <div class="medical-page-footer">
      <!-- 页面底部 -->
    </div>
  </div>
</template>
```

### 卡片布局
```vue
<template>
  <MedicalCard
    variant="primary"
    title="医院信息系统"
    subtitle="HIS Implementation"
    icon="mdi-hospital-box"
    status="active"
    elevated
    interactive
  >
    <!-- 卡片内容 -->
  </MedicalCard>
</template>
```

### 按钮使用
```vue
<template>
  <div class="medical-actions">
    <MedicalButton
      variant="primary"
      medical-type="routine"
      prepend-icon="mdi-content-save"
    >
      保存项目
    </MedicalButton>
    
    <MedicalButton
      variant="outlined"
      medical-type="diagnostic"
      prepend-icon="mdi-chart-line"
    >
      查看报告
    </MedicalButton>
    
    <MedicalButton
      variant="error"
      medical-type="emergency"
      prepend-icon="mdi-alert"
    >
      紧急处理
    </MedicalButton>
  </div>
</template>
```

## 📱 响应式设计

### 断点定义
```css
/* 移动设备 */
@media (max-width: 768px) { }

/* 平板设备 */
@media (min-width: 769px) and (max-width: 1024px) { }

/* 桌面设备 */
@media (min-width: 1025px) { }
```

### 适配策略
- **移动优先**: 从小屏幕开始设计
- **渐进增强**: 逐步增加大屏幕功能
- **触摸友好**: 确保触摸目标足够大
- **内容优先**: 优先显示核心内容

## 🔧 实施指南

### 1. 安装和配置
```bash
# 安装依赖
npm install @mdi/font vuetify

# 导入样式
import './styles/medical-theme.css'
```

### 2. 组件使用
```javascript
// 导入医疗组件
import MedicalCard from '@/components/medical/MedicalCard.vue'
import MedicalButton from '@/components/medical/MedicalButton.vue'
```

### 3. 样式应用
```vue
<style scoped>
.medical-container {
  padding: var(--medical-space-6);
  background-color: var(--google-grey-50);
}

.medical-title {
  color: var(--medical-primary);
  font-size: var(--medical-text-2xl);
  font-weight: var(--medical-font-semibold);
}
</style>
```

## 🎯 最佳实践

### 1. 色彩使用
- 主色调用于主要操作和品牌识别
- 状态色彩用于反馈和状态指示
- 中性色彩用于文字和背景

### 2. 间距应用
- 保持一致的间距比例
- 使用预定义的间距变量
- 避免使用任意数值

### 3. 组件组合
- 优先使用医疗主题组件
- 保持组件的一致性
- 遵循组件的使用规范

### 4. 可访问性
- 确保足够的颜色对比度
- 提供键盘导航支持
- 添加适当的ARIA标签

---

通过遵循这套设计系统，可以确保医疗项目管理系统具有专业、一致、易用的用户界面，提升用户体验和工作效率。
