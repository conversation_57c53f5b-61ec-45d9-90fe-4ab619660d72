{"ast": null, "code": "/**\n * 工作流自动化状态管理模块\n */\n\nimport workflowService from '@/services/workflowService';\nimport api from '@/services/api';\nconst state = {\n  // 自动化规则\n  automationRules: [],\n  activeRules: [],\n  // 工作流模板\n  workflowTemplates: [],\n  currentTemplate: null,\n  // 工作流指标\n  metrics: {\n    taskProcessingSpeed: 0,\n    avgResponseTime: 0,\n    automationRate: 0,\n    errorRate: 0,\n    efficiency: 0\n  },\n  // 优化建议\n  optimizationSuggestions: [],\n  appliedSuggestions: [],\n  // 工作流状态\n  isAutomationEnabled: false,\n  isAnalyzing: false,\n  lastAnalysisTime: null,\n  // 通知和提醒\n  pendingNotifications: [],\n  automationLogs: []\n};\nconst getters = {\n  // 获取活跃的自动化规则\n  getActiveRules: state => state.activeRules,\n  // 获取工作流效率\n  getWorkflowEfficiency: state => state.metrics.efficiency,\n  // 获取优化建议\n  getOptimizationSuggestions: state => state.optimizationSuggestions,\n  // 获取可用的工作流模板\n  getAvailableTemplates: state => state.workflowTemplates,\n  // 获取自动化状态\n  isAutomationActive: state => state.isAutomationEnabled,\n  // 获取待处理通知\n  getPendingNotifications: state => state.pendingNotifications,\n  // 获取自动化日志\n  getAutomationLogs: state => state.automationLogs.slice(-50) // 最近50条\n};\nconst mutations = {\n  // 设置自动化规则\n  SET_AUTOMATION_RULES(state, rules) {\n    state.automationRules = rules;\n    state.activeRules = rules.filter(rule => rule.isActive);\n  },\n  // 添加自动化规则\n  ADD_AUTOMATION_RULE(state, rule) {\n    state.automationRules.push(rule);\n    if (rule.isActive) {\n      state.activeRules.push(rule);\n    }\n  },\n  // 更新自动化规则\n  UPDATE_AUTOMATION_RULE(state, {\n    ruleId,\n    updates\n  }) {\n    const ruleIndex = state.automationRules.findIndex(rule => rule.id === ruleId);\n    if (ruleIndex !== -1) {\n      state.automationRules[ruleIndex] = {\n        ...state.automationRules[ruleIndex],\n        ...updates\n      };\n\n      // 更新活跃规则列表\n      state.activeRules = state.automationRules.filter(rule => rule.isActive);\n    }\n  },\n  // 设置工作流模板\n  SET_WORKFLOW_TEMPLATES(state, templates) {\n    state.workflowTemplates = templates;\n  },\n  // 设置当前模板\n  SET_CURRENT_TEMPLATE(state, template) {\n    state.currentTemplate = template;\n  },\n  // 更新工作流指标\n  UPDATE_METRICS(state, metrics) {\n    state.metrics = {\n      ...state.metrics,\n      ...metrics\n    };\n  },\n  // 设置优化建议\n  SET_OPTIMIZATION_SUGGESTIONS(state, suggestions) {\n    state.optimizationSuggestions = suggestions;\n  },\n  // 添加已应用的建议\n  ADD_APPLIED_SUGGESTION(state, suggestion) {\n    state.appliedSuggestions.push({\n      ...suggestion,\n      appliedAt: new Date().toISOString()\n    });\n  },\n  // 设置自动化状态\n  SET_AUTOMATION_ENABLED(state, enabled) {\n    state.isAutomationEnabled = enabled;\n  },\n  // 设置分析状态\n  SET_ANALYZING(state, analyzing) {\n    state.isAnalyzing = analyzing;\n  },\n  // 设置最后分析时间\n  SET_LAST_ANALYSIS_TIME(state, time) {\n    state.lastAnalysisTime = time;\n  },\n  // 添加通知\n  ADD_NOTIFICATION(state, notification) {\n    state.pendingNotifications.push({\n      ...notification,\n      id: Date.now(),\n      createdAt: new Date().toISOString()\n    });\n  },\n  // 移除通知\n  REMOVE_NOTIFICATION(state, notificationId) {\n    state.pendingNotifications = state.pendingNotifications.filter(notification => notification.id !== notificationId);\n  },\n  // 添加自动化日志\n  ADD_AUTOMATION_LOG(state, log) {\n    state.automationLogs.push({\n      ...log,\n      id: Date.now(),\n      timestamp: new Date().toISOString()\n    });\n  }\n};\nconst actions = {\n  // 初始化工作流自动化\n  async initializeWorkflow({\n    commit,\n    dispatch\n  }) {\n    try {\n      // 加载自动化规则\n      await dispatch('loadAutomationRules');\n\n      // 加载工作流模板\n      await dispatch('loadWorkflowTemplates');\n\n      // 启动自动化监控\n      dispatch('startAutomationMonitoring');\n    } catch (error) {\n      console.error('初始化工作流失败:', error);\n    }\n  },\n  // 加载自动化规则\n  async loadAutomationRules({\n    commit\n  }) {\n    try {\n      const response = await api.get('/workflow/automation-rules');\n      commit('SET_AUTOMATION_RULES', response.data);\n    } catch (error) {\n      console.error('加载自动化规则失败:', error);\n    }\n  },\n  // 创建自动化规则\n  async createAutomationRule({\n    commit\n  }, ruleData) {\n    try {\n      const response = await api.post('/workflow/automation-rules', ruleData);\n      commit('ADD_AUTOMATION_RULE', response.data);\n\n      // 记录日志\n      commit('ADD_AUTOMATION_LOG', {\n        type: 'rule_created',\n        message: `创建自动化规则: ${ruleData.name}`,\n        data: ruleData\n      });\n      return response.data;\n    } catch (error) {\n      console.error('创建自动化规则失败:', error);\n      throw error;\n    }\n  },\n  // 启用/禁用自动化规则\n  async toggleAutomationRule({\n    commit\n  }, {\n    ruleId,\n    enabled\n  }) {\n    try {\n      const response = await api.put(`/workflow/automation-rules/${ruleId}`, {\n        isActive: enabled\n      });\n      commit('UPDATE_AUTOMATION_RULE', {\n        ruleId,\n        updates: {\n          isActive: enabled\n        }\n      });\n      commit('ADD_AUTOMATION_LOG', {\n        type: 'rule_toggled',\n        message: `${enabled ? '启用' : '禁用'}自动化规则: ${ruleId}`,\n        data: {\n          ruleId,\n          enabled\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('切换自动化规则状态失败:', error);\n      throw error;\n    }\n  },\n  // 加载工作流模板\n  async loadWorkflowTemplates({\n    commit\n  }) {\n    try {\n      const response = await api.get('/workflow/templates');\n      commit('SET_WORKFLOW_TEMPLATES', response.data);\n    } catch (error) {\n      console.error('加载工作流模板失败:', error);\n    }\n  },\n  // 应用工作流模板\n  async applyWorkflowTemplate({\n    commit,\n    dispatch\n  }, {\n    projectId,\n    templateId\n  }) {\n    try {\n      const result = await workflowService.applyWorkflowTemplate(projectId, templateId);\n      if (result.success) {\n        commit('ADD_AUTOMATION_LOG', {\n          type: 'template_applied',\n          message: `应用工作流模板到项目 ${projectId}`,\n          data: {\n            projectId,\n            templateId\n          }\n        });\n\n        // 重新分析工作流\n        await dispatch('analyzeWorkflow', projectId);\n      }\n      return result;\n    } catch (error) {\n      console.error('应用工作流模板失败:', error);\n      throw error;\n    }\n  },\n  // 分析工作流效率\n  async analyzeWorkflow({\n    commit\n  }, projectId) {\n    commit('SET_ANALYZING', true);\n    try {\n      const analysis = await workflowService.analyzeWorkflowEfficiency(projectId);\n\n      // 更新指标\n      commit('UPDATE_METRICS', {\n        taskProcessingSpeed: analysis.taskMetrics.avgCompletionTime,\n        efficiency: analysis.overallEfficiency,\n        ...analysis.taskMetrics\n      });\n\n      // 设置优化建议\n      commit('SET_OPTIMIZATION_SUGGESTIONS', analysis.suggestions);\n\n      // 记录分析时间\n      commit('SET_LAST_ANALYSIS_TIME', new Date().toISOString());\n      commit('ADD_AUTOMATION_LOG', {\n        type: 'workflow_analyzed',\n        message: `完成项目 ${projectId} 工作流分析`,\n        data: analysis\n      });\n      return analysis;\n    } catch (error) {\n      console.error('工作流分析失败:', error);\n      throw error;\n    } finally {\n      commit('SET_ANALYZING', false);\n    }\n  },\n  // 应用优化建议\n  async applyOptimizationSuggestion({\n    commit,\n    dispatch\n  }, suggestion) {\n    try {\n      // 根据建议类型执行相应操作\n      switch (suggestion.action) {\n        case 'enable_auto_assignment':\n          await dispatch('enableAutoAssignment');\n          break;\n        case 'enable_auto_transition':\n          await dispatch('enableAutoTransition');\n          break;\n        case 'enable_smart_reminders':\n          await dispatch('enableSmartReminders');\n          break;\n        case 'optimize_notifications':\n          await dispatch('optimizeNotifications');\n          break;\n      }\n      commit('ADD_APPLIED_SUGGESTION', suggestion);\n      commit('ADD_AUTOMATION_LOG', {\n        type: 'suggestion_applied',\n        message: `应用优化建议: ${suggestion.title}`,\n        data: suggestion\n      });\n    } catch (error) {\n      console.error('应用优化建议失败:', error);\n      throw error;\n    }\n  },\n  // 启用任务自动分配\n  async enableAutoAssignment({\n    commit\n  }) {\n    const rule = {\n      id: `auto_assignment_${Date.now()}`,\n      name: '任务自动分配',\n      type: 'task_auto_assignment',\n      description: '根据团队成员工作负载自动分配新任务',\n      isActive: true,\n      conditions: {\n        trigger: 'task_created',\n        criteria: 'unassigned_task'\n      },\n      actions: {\n        type: 'assign_to_least_loaded_member'\n      }\n    };\n    workflowService.addAutomationRule(rule);\n    commit('ADD_AUTOMATION_RULE', rule);\n  },\n  // 启用状态自动流转\n  async enableAutoTransition({\n    commit\n  }) {\n    const rule = {\n      id: `auto_transition_${Date.now()}`,\n      name: '状态自动流转',\n      type: 'status_auto_transition',\n      description: '满足条件时自动流转任务状态',\n      isActive: true,\n      conditions: {\n        trigger: 'task_completed',\n        criteria: 'all_dependencies_met'\n      },\n      actions: {\n        type: 'transition_to_next_status'\n      }\n    };\n    workflowService.addAutomationRule(rule);\n    commit('ADD_AUTOMATION_RULE', rule);\n  },\n  // 启用智能提醒\n  async enableSmartReminders({\n    commit\n  }) {\n    const rule = {\n      id: `smart_reminders_${Date.now()}`,\n      name: '智能提醒',\n      type: 'deadline_reminder',\n      description: '基于历史数据智能发送截止日期提醒',\n      isActive: true,\n      conditions: {\n        trigger: 'time_based',\n        criteria: 'approaching_deadline'\n      },\n      actions: {\n        type: 'send_smart_reminder'\n      }\n    };\n    workflowService.addAutomationRule(rule);\n    commit('ADD_AUTOMATION_RULE', rule);\n  },\n  // 启动自动化监控\n  startAutomationMonitoring({\n    commit,\n    state\n  }) {\n    if (state.isAutomationEnabled) return;\n    commit('SET_AUTOMATION_ENABLED', true);\n\n    // 启动定时检查\n    setInterval(() => {\n      // 检查待处理的自动化任务\n      // 这里可以添加具体的监控逻辑\n    }, 60000); // 每分钟检查一次\n\n    commit('ADD_AUTOMATION_LOG', {\n      type: 'monitoring_started',\n      message: '工作流自动化监控已启动'\n    });\n  },\n  // 停止自动化监控\n  stopAutomationMonitoring({\n    commit\n  }) {\n    commit('SET_AUTOMATION_ENABLED', false);\n    commit('ADD_AUTOMATION_LOG', {\n      type: 'monitoring_stopped',\n      message: '工作流自动化监控已停止'\n    });\n  }\n};\nexport default {\n  namespaced: true,\n  state,\n  getters,\n  mutations,\n  actions\n};", "map": {"version": 3, "names": ["workflowService", "api", "state", "automationRules", "activeRules", "workflowTemplates", "currentTemplate", "metrics", "taskProcessingSpeed", "avgResponseTime", "automationRate", "errorRate", "efficiency", "optimizationSuggestions", "appliedSuggestions", "isAutomationEnabled", "isAnalyzing", "lastAnalysisTime", "pendingNotifications", "automationLogs", "getters", "getActiveRules", "getWorkflowEfficiency", "getOptimizationSuggestions", "getAvailableTemplates", "isAutomationActive", "getPendingNotifications", "getAutomationLogs", "slice", "mutations", "SET_AUTOMATION_RULES", "rules", "filter", "rule", "isActive", "ADD_AUTOMATION_RULE", "push", "UPDATE_AUTOMATION_RULE", "ruleId", "updates", "ruleIndex", "findIndex", "id", "SET_WORKFLOW_TEMPLATES", "templates", "SET_CURRENT_TEMPLATE", "template", "UPDATE_METRICS", "SET_OPTIMIZATION_SUGGESTIONS", "suggestions", "ADD_APPLIED_SUGGESTION", "suggestion", "appliedAt", "Date", "toISOString", "SET_AUTOMATION_ENABLED", "enabled", "SET_ANALYZING", "analyzing", "SET_LAST_ANALYSIS_TIME", "time", "ADD_NOTIFICATION", "notification", "now", "createdAt", "REMOVE_NOTIFICATION", "notificationId", "ADD_AUTOMATION_LOG", "log", "timestamp", "actions", "initializeWorkflow", "commit", "dispatch", "error", "console", "loadAutomationRules", "response", "get", "data", "createAutomationRule", "ruleData", "post", "type", "message", "name", "toggleAutomationRule", "put", "loadWorkflowTemplates", "applyWorkflowTemplate", "projectId", "templateId", "result", "success", "analyzeWorkflow", "analysis", "analyzeWorkflowEfficiency", "taskMetrics", "avgCompletionTime", "overallEfficiency", "applyOptimizationSuggestion", "action", "title", "enableAutoAssignment", "description", "conditions", "trigger", "criteria", "addAutomationRule", "enableAutoTransition", "enableSmartReminders", "startAutomationMonitoring", "setInterval", "stopAutomationMonitoring", "namespaced"], "sources": ["/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/技术测试/项目实施进度管理/project-management/frontend/src/store/modules/workflow.js"], "sourcesContent": ["/**\n * 工作流自动化状态管理模块\n */\n\nimport workflowService from '@/services/workflowService'\nimport api from '@/services/api'\n\nconst state = {\n  // 自动化规则\n  automationRules: [],\n  activeRules: [],\n  \n  // 工作流模板\n  workflowTemplates: [],\n  currentTemplate: null,\n  \n  // 工作流指标\n  metrics: {\n    taskProcessingSpeed: 0,\n    avgResponseTime: 0,\n    automationRate: 0,\n    errorRate: 0,\n    efficiency: 0\n  },\n  \n  // 优化建议\n  optimizationSuggestions: [],\n  appliedSuggestions: [],\n  \n  // 工作流状态\n  isAutomationEnabled: false,\n  isAnalyzing: false,\n  lastAnalysisTime: null,\n  \n  // 通知和提醒\n  pendingNotifications: [],\n  automationLogs: []\n}\n\nconst getters = {\n  // 获取活跃的自动化规则\n  getActiveRules: state => state.activeRules,\n  \n  // 获取工作流效率\n  getWorkflowEfficiency: state => state.metrics.efficiency,\n  \n  // 获取优化建议\n  getOptimizationSuggestions: state => state.optimizationSuggestions,\n  \n  // 获取可用的工作流模板\n  getAvailableTemplates: state => state.workflowTemplates,\n  \n  // 获取自动化状态\n  isAutomationActive: state => state.isAutomationEnabled,\n  \n  // 获取待处理通知\n  getPendingNotifications: state => state.pendingNotifications,\n  \n  // 获取自动化日志\n  getAutomationLogs: state => state.automationLogs.slice(-50) // 最近50条\n}\n\nconst mutations = {\n  // 设置自动化规则\n  SET_AUTOMATION_RULES(state, rules) {\n    state.automationRules = rules\n    state.activeRules = rules.filter(rule => rule.isActive)\n  },\n  \n  // 添加自动化规则\n  ADD_AUTOMATION_RULE(state, rule) {\n    state.automationRules.push(rule)\n    if (rule.isActive) {\n      state.activeRules.push(rule)\n    }\n  },\n  \n  // 更新自动化规则\n  UPDATE_AUTOMATION_RULE(state, { ruleId, updates }) {\n    const ruleIndex = state.automationRules.findIndex(rule => rule.id === ruleId)\n    if (ruleIndex !== -1) {\n      state.automationRules[ruleIndex] = { ...state.automationRules[ruleIndex], ...updates }\n      \n      // 更新活跃规则列表\n      state.activeRules = state.automationRules.filter(rule => rule.isActive)\n    }\n  },\n  \n  // 设置工作流模板\n  SET_WORKFLOW_TEMPLATES(state, templates) {\n    state.workflowTemplates = templates\n  },\n  \n  // 设置当前模板\n  SET_CURRENT_TEMPLATE(state, template) {\n    state.currentTemplate = template\n  },\n  \n  // 更新工作流指标\n  UPDATE_METRICS(state, metrics) {\n    state.metrics = { ...state.metrics, ...metrics }\n  },\n  \n  // 设置优化建议\n  SET_OPTIMIZATION_SUGGESTIONS(state, suggestions) {\n    state.optimizationSuggestions = suggestions\n  },\n  \n  // 添加已应用的建议\n  ADD_APPLIED_SUGGESTION(state, suggestion) {\n    state.appliedSuggestions.push({\n      ...suggestion,\n      appliedAt: new Date().toISOString()\n    })\n  },\n  \n  // 设置自动化状态\n  SET_AUTOMATION_ENABLED(state, enabled) {\n    state.isAutomationEnabled = enabled\n  },\n  \n  // 设置分析状态\n  SET_ANALYZING(state, analyzing) {\n    state.isAnalyzing = analyzing\n  },\n  \n  // 设置最后分析时间\n  SET_LAST_ANALYSIS_TIME(state, time) {\n    state.lastAnalysisTime = time\n  },\n  \n  // 添加通知\n  ADD_NOTIFICATION(state, notification) {\n    state.pendingNotifications.push({\n      ...notification,\n      id: Date.now(),\n      createdAt: new Date().toISOString()\n    })\n  },\n  \n  // 移除通知\n  REMOVE_NOTIFICATION(state, notificationId) {\n    state.pendingNotifications = state.pendingNotifications.filter(\n      notification => notification.id !== notificationId\n    )\n  },\n  \n  // 添加自动化日志\n  ADD_AUTOMATION_LOG(state, log) {\n    state.automationLogs.push({\n      ...log,\n      id: Date.now(),\n      timestamp: new Date().toISOString()\n    })\n  }\n}\n\nconst actions = {\n  // 初始化工作流自动化\n  async initializeWorkflow({ commit, dispatch }) {\n    try {\n      // 加载自动化规则\n      await dispatch('loadAutomationRules')\n      \n      // 加载工作流模板\n      await dispatch('loadWorkflowTemplates')\n      \n      // 启动自动化监控\n      dispatch('startAutomationMonitoring')\n      \n    } catch (error) {\n      console.error('初始化工作流失败:', error)\n    }\n  },\n  \n  // 加载自动化规则\n  async loadAutomationRules({ commit }) {\n    try {\n      const response = await api.get('/workflow/automation-rules')\n      commit('SET_AUTOMATION_RULES', response.data)\n    } catch (error) {\n      console.error('加载自动化规则失败:', error)\n    }\n  },\n  \n  // 创建自动化规则\n  async createAutomationRule({ commit }, ruleData) {\n    try {\n      const response = await api.post('/workflow/automation-rules', ruleData)\n      commit('ADD_AUTOMATION_RULE', response.data)\n      \n      // 记录日志\n      commit('ADD_AUTOMATION_LOG', {\n        type: 'rule_created',\n        message: `创建自动化规则: ${ruleData.name}`,\n        data: ruleData\n      })\n      \n      return response.data\n    } catch (error) {\n      console.error('创建自动化规则失败:', error)\n      throw error\n    }\n  },\n  \n  // 启用/禁用自动化规则\n  async toggleAutomationRule({ commit }, { ruleId, enabled }) {\n    try {\n      const response = await api.put(`/workflow/automation-rules/${ruleId}`, { isActive: enabled })\n      commit('UPDATE_AUTOMATION_RULE', { ruleId, updates: { isActive: enabled } })\n      \n      commit('ADD_AUTOMATION_LOG', {\n        type: 'rule_toggled',\n        message: `${enabled ? '启用' : '禁用'}自动化规则: ${ruleId}`,\n        data: { ruleId, enabled }\n      })\n      \n      return response.data\n    } catch (error) {\n      console.error('切换自动化规则状态失败:', error)\n      throw error\n    }\n  },\n  \n  // 加载工作流模板\n  async loadWorkflowTemplates({ commit }) {\n    try {\n      const response = await api.get('/workflow/templates')\n      commit('SET_WORKFLOW_TEMPLATES', response.data)\n    } catch (error) {\n      console.error('加载工作流模板失败:', error)\n    }\n  },\n  \n  // 应用工作流模板\n  async applyWorkflowTemplate({ commit, dispatch }, { projectId, templateId }) {\n    try {\n      const result = await workflowService.applyWorkflowTemplate(projectId, templateId)\n      \n      if (result.success) {\n        commit('ADD_AUTOMATION_LOG', {\n          type: 'template_applied',\n          message: `应用工作流模板到项目 ${projectId}`,\n          data: { projectId, templateId }\n        })\n        \n        // 重新分析工作流\n        await dispatch('analyzeWorkflow', projectId)\n      }\n      \n      return result\n    } catch (error) {\n      console.error('应用工作流模板失败:', error)\n      throw error\n    }\n  },\n  \n  // 分析工作流效率\n  async analyzeWorkflow({ commit }, projectId) {\n    commit('SET_ANALYZING', true)\n    \n    try {\n      const analysis = await workflowService.analyzeWorkflowEfficiency(projectId)\n      \n      // 更新指标\n      commit('UPDATE_METRICS', {\n        taskProcessingSpeed: analysis.taskMetrics.avgCompletionTime,\n        efficiency: analysis.overallEfficiency,\n        ...analysis.taskMetrics\n      })\n      \n      // 设置优化建议\n      commit('SET_OPTIMIZATION_SUGGESTIONS', analysis.suggestions)\n      \n      // 记录分析时间\n      commit('SET_LAST_ANALYSIS_TIME', new Date().toISOString())\n      \n      commit('ADD_AUTOMATION_LOG', {\n        type: 'workflow_analyzed',\n        message: `完成项目 ${projectId} 工作流分析`,\n        data: analysis\n      })\n      \n      return analysis\n    } catch (error) {\n      console.error('工作流分析失败:', error)\n      throw error\n    } finally {\n      commit('SET_ANALYZING', false)\n    }\n  },\n  \n  // 应用优化建议\n  async applyOptimizationSuggestion({ commit, dispatch }, suggestion) {\n    try {\n      // 根据建议类型执行相应操作\n      switch (suggestion.action) {\n        case 'enable_auto_assignment':\n          await dispatch('enableAutoAssignment')\n          break\n        case 'enable_auto_transition':\n          await dispatch('enableAutoTransition')\n          break\n        case 'enable_smart_reminders':\n          await dispatch('enableSmartReminders')\n          break\n        case 'optimize_notifications':\n          await dispatch('optimizeNotifications')\n          break\n      }\n      \n      commit('ADD_APPLIED_SUGGESTION', suggestion)\n      commit('ADD_AUTOMATION_LOG', {\n        type: 'suggestion_applied',\n        message: `应用优化建议: ${suggestion.title}`,\n        data: suggestion\n      })\n      \n    } catch (error) {\n      console.error('应用优化建议失败:', error)\n      throw error\n    }\n  },\n  \n  // 启用任务自动分配\n  async enableAutoAssignment({ commit }) {\n    const rule = {\n      id: `auto_assignment_${Date.now()}`,\n      name: '任务自动分配',\n      type: 'task_auto_assignment',\n      description: '根据团队成员工作负载自动分配新任务',\n      isActive: true,\n      conditions: {\n        trigger: 'task_created',\n        criteria: 'unassigned_task'\n      },\n      actions: {\n        type: 'assign_to_least_loaded_member'\n      }\n    }\n    \n    workflowService.addAutomationRule(rule)\n    commit('ADD_AUTOMATION_RULE', rule)\n  },\n  \n  // 启用状态自动流转\n  async enableAutoTransition({ commit }) {\n    const rule = {\n      id: `auto_transition_${Date.now()}`,\n      name: '状态自动流转',\n      type: 'status_auto_transition',\n      description: '满足条件时自动流转任务状态',\n      isActive: true,\n      conditions: {\n        trigger: 'task_completed',\n        criteria: 'all_dependencies_met'\n      },\n      actions: {\n        type: 'transition_to_next_status'\n      }\n    }\n    \n    workflowService.addAutomationRule(rule)\n    commit('ADD_AUTOMATION_RULE', rule)\n  },\n  \n  // 启用智能提醒\n  async enableSmartReminders({ commit }) {\n    const rule = {\n      id: `smart_reminders_${Date.now()}`,\n      name: '智能提醒',\n      type: 'deadline_reminder',\n      description: '基于历史数据智能发送截止日期提醒',\n      isActive: true,\n      conditions: {\n        trigger: 'time_based',\n        criteria: 'approaching_deadline'\n      },\n      actions: {\n        type: 'send_smart_reminder'\n      }\n    }\n    \n    workflowService.addAutomationRule(rule)\n    commit('ADD_AUTOMATION_RULE', rule)\n  },\n  \n  // 启动自动化监控\n  startAutomationMonitoring({ commit, state }) {\n    if (state.isAutomationEnabled) return\n    \n    commit('SET_AUTOMATION_ENABLED', true)\n    \n    // 启动定时检查\n    setInterval(() => {\n      // 检查待处理的自动化任务\n      // 这里可以添加具体的监控逻辑\n    }, 60000) // 每分钟检查一次\n    \n    commit('ADD_AUTOMATION_LOG', {\n      type: 'monitoring_started',\n      message: '工作流自动化监控已启动'\n    })\n  },\n  \n  // 停止自动化监控\n  stopAutomationMonitoring({ commit }) {\n    commit('SET_AUTOMATION_ENABLED', false)\n    commit('ADD_AUTOMATION_LOG', {\n      type: 'monitoring_stopped',\n      message: '工作流自动化监控已停止'\n    })\n  }\n}\n\nexport default {\n  namespaced: true,\n  state,\n  getters,\n  mutations,\n  actions\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA,OAAOA,eAAe,MAAM,4BAA4B;AACxD,OAAOC,GAAG,MAAM,gBAAgB;AAEhC,MAAMC,KAAK,GAAG;EACZ;EACAC,eAAe,EAAE,EAAE;EACnBC,WAAW,EAAE,EAAE;EAEf;EACAC,iBAAiB,EAAE,EAAE;EACrBC,eAAe,EAAE,IAAI;EAErB;EACAC,OAAO,EAAE;IACPC,mBAAmB,EAAE,CAAC;IACtBC,eAAe,EAAE,CAAC;IAClBC,cAAc,EAAE,CAAC;IACjBC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE;EACd,CAAC;EAED;EACAC,uBAAuB,EAAE,EAAE;EAC3BC,kBAAkB,EAAE,EAAE;EAEtB;EACAC,mBAAmB,EAAE,KAAK;EAC1BC,WAAW,EAAE,KAAK;EAClBC,gBAAgB,EAAE,IAAI;EAEtB;EACAC,oBAAoB,EAAE,EAAE;EACxBC,cAAc,EAAE;AAClB,CAAC;AAED,MAAMC,OAAO,GAAG;EACd;EACAC,cAAc,EAAEnB,KAAK,IAAIA,KAAK,CAACE,WAAW;EAE1C;EACAkB,qBAAqB,EAAEpB,KAAK,IAAIA,KAAK,CAACK,OAAO,CAACK,UAAU;EAExD;EACAW,0BAA0B,EAAErB,KAAK,IAAIA,KAAK,CAACW,uBAAuB;EAElE;EACAW,qBAAqB,EAAEtB,KAAK,IAAIA,KAAK,CAACG,iBAAiB;EAEvD;EACAoB,kBAAkB,EAAEvB,KAAK,IAAIA,KAAK,CAACa,mBAAmB;EAEtD;EACAW,uBAAuB,EAAExB,KAAK,IAAIA,KAAK,CAACgB,oBAAoB;EAE5D;EACAS,iBAAiB,EAAEzB,KAAK,IAAIA,KAAK,CAACiB,cAAc,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AAC9D,CAAC;AAED,MAAMC,SAAS,GAAG;EAChB;EACAC,oBAAoBA,CAAC5B,KAAK,EAAE6B,KAAK,EAAE;IACjC7B,KAAK,CAACC,eAAe,GAAG4B,KAAK;IAC7B7B,KAAK,CAACE,WAAW,GAAG2B,KAAK,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC;EACzD,CAAC;EAED;EACAC,mBAAmBA,CAACjC,KAAK,EAAE+B,IAAI,EAAE;IAC/B/B,KAAK,CAACC,eAAe,CAACiC,IAAI,CAACH,IAAI,CAAC;IAChC,IAAIA,IAAI,CAACC,QAAQ,EAAE;MACjBhC,KAAK,CAACE,WAAW,CAACgC,IAAI,CAACH,IAAI,CAAC;IAC9B;EACF,CAAC;EAED;EACAI,sBAAsBA,CAACnC,KAAK,EAAE;IAAEoC,MAAM;IAAEC;EAAQ,CAAC,EAAE;IACjD,MAAMC,SAAS,GAAGtC,KAAK,CAACC,eAAe,CAACsC,SAAS,CAACR,IAAI,IAAIA,IAAI,CAACS,EAAE,KAAKJ,MAAM,CAAC;IAC7E,IAAIE,SAAS,KAAK,CAAC,CAAC,EAAE;MACpBtC,KAAK,CAACC,eAAe,CAACqC,SAAS,CAAC,GAAG;QAAE,GAAGtC,KAAK,CAACC,eAAe,CAACqC,SAAS,CAAC;QAAE,GAAGD;MAAQ,CAAC;;MAEtF;MACArC,KAAK,CAACE,WAAW,GAAGF,KAAK,CAACC,eAAe,CAAC6B,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC;IACzE;EACF,CAAC;EAED;EACAS,sBAAsBA,CAACzC,KAAK,EAAE0C,SAAS,EAAE;IACvC1C,KAAK,CAACG,iBAAiB,GAAGuC,SAAS;EACrC,CAAC;EAED;EACAC,oBAAoBA,CAAC3C,KAAK,EAAE4C,QAAQ,EAAE;IACpC5C,KAAK,CAACI,eAAe,GAAGwC,QAAQ;EAClC,CAAC;EAED;EACAC,cAAcA,CAAC7C,KAAK,EAAEK,OAAO,EAAE;IAC7BL,KAAK,CAACK,OAAO,GAAG;MAAE,GAAGL,KAAK,CAACK,OAAO;MAAE,GAAGA;IAAQ,CAAC;EAClD,CAAC;EAED;EACAyC,4BAA4BA,CAAC9C,KAAK,EAAE+C,WAAW,EAAE;IAC/C/C,KAAK,CAACW,uBAAuB,GAAGoC,WAAW;EAC7C,CAAC;EAED;EACAC,sBAAsBA,CAAChD,KAAK,EAAEiD,UAAU,EAAE;IACxCjD,KAAK,CAACY,kBAAkB,CAACsB,IAAI,CAAC;MAC5B,GAAGe,UAAU;MACbC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC;EAED;EACAC,sBAAsBA,CAACrD,KAAK,EAAEsD,OAAO,EAAE;IACrCtD,KAAK,CAACa,mBAAmB,GAAGyC,OAAO;EACrC,CAAC;EAED;EACAC,aAAaA,CAACvD,KAAK,EAAEwD,SAAS,EAAE;IAC9BxD,KAAK,CAACc,WAAW,GAAG0C,SAAS;EAC/B,CAAC;EAED;EACAC,sBAAsBA,CAACzD,KAAK,EAAE0D,IAAI,EAAE;IAClC1D,KAAK,CAACe,gBAAgB,GAAG2C,IAAI;EAC/B,CAAC;EAED;EACAC,gBAAgBA,CAAC3D,KAAK,EAAE4D,YAAY,EAAE;IACpC5D,KAAK,CAACgB,oBAAoB,CAACkB,IAAI,CAAC;MAC9B,GAAG0B,YAAY;MACfpB,EAAE,EAAEW,IAAI,CAACU,GAAG,CAAC,CAAC;MACdC,SAAS,EAAE,IAAIX,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC;EAED;EACAW,mBAAmBA,CAAC/D,KAAK,EAAEgE,cAAc,EAAE;IACzChE,KAAK,CAACgB,oBAAoB,GAAGhB,KAAK,CAACgB,oBAAoB,CAACc,MAAM,CAC5D8B,YAAY,IAAIA,YAAY,CAACpB,EAAE,KAAKwB,cACtC,CAAC;EACH,CAAC;EAED;EACAC,kBAAkBA,CAACjE,KAAK,EAAEkE,GAAG,EAAE;IAC7BlE,KAAK,CAACiB,cAAc,CAACiB,IAAI,CAAC;MACxB,GAAGgC,GAAG;MACN1B,EAAE,EAAEW,IAAI,CAACU,GAAG,CAAC,CAAC;MACdM,SAAS,EAAE,IAAIhB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC,CAAC;EACJ;AACF,CAAC;AAED,MAAMgB,OAAO,GAAG;EACd;EACA,MAAMC,kBAAkBA,CAAC;IAAEC,MAAM;IAAEC;EAAS,CAAC,EAAE;IAC7C,IAAI;MACF;MACA,MAAMA,QAAQ,CAAC,qBAAqB,CAAC;;MAErC;MACA,MAAMA,QAAQ,CAAC,uBAAuB,CAAC;;MAEvC;MACAA,QAAQ,CAAC,2BAA2B,CAAC;IAEvC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;EAED;EACA,MAAME,mBAAmBA,CAAC;IAAEJ;EAAO,CAAC,EAAE;IACpC,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAM5E,GAAG,CAAC6E,GAAG,CAAC,4BAA4B,CAAC;MAC5DN,MAAM,CAAC,sBAAsB,EAAEK,QAAQ,CAACE,IAAI,CAAC;IAC/C,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IACpC;EACF,CAAC;EAED;EACA,MAAMM,oBAAoBA,CAAC;IAAER;EAAO,CAAC,EAAES,QAAQ,EAAE;IAC/C,IAAI;MACF,MAAMJ,QAAQ,GAAG,MAAM5E,GAAG,CAACiF,IAAI,CAAC,4BAA4B,EAAED,QAAQ,CAAC;MACvET,MAAM,CAAC,qBAAqB,EAAEK,QAAQ,CAACE,IAAI,CAAC;;MAE5C;MACAP,MAAM,CAAC,oBAAoB,EAAE;QAC3BW,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE,YAAYH,QAAQ,CAACI,IAAI,EAAE;QACpCN,IAAI,EAAEE;MACR,CAAC,CAAC;MAEF,OAAOJ,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMY,oBAAoBA,CAAC;IAAEd;EAAO,CAAC,EAAE;IAAElC,MAAM;IAAEkB;EAAQ,CAAC,EAAE;IAC1D,IAAI;MACF,MAAMqB,QAAQ,GAAG,MAAM5E,GAAG,CAACsF,GAAG,CAAC,8BAA8BjD,MAAM,EAAE,EAAE;QAAEJ,QAAQ,EAAEsB;MAAQ,CAAC,CAAC;MAC7FgB,MAAM,CAAC,wBAAwB,EAAE;QAAElC,MAAM;QAAEC,OAAO,EAAE;UAAEL,QAAQ,EAAEsB;QAAQ;MAAE,CAAC,CAAC;MAE5EgB,MAAM,CAAC,oBAAoB,EAAE;QAC3BW,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE,GAAG5B,OAAO,GAAG,IAAI,GAAG,IAAI,UAAUlB,MAAM,EAAE;QACnDyC,IAAI,EAAE;UAAEzC,MAAM;UAAEkB;QAAQ;MAC1B,CAAC,CAAC;MAEF,OAAOqB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMc,qBAAqBA,CAAC;IAAEhB;EAAO,CAAC,EAAE;IACtC,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAM5E,GAAG,CAAC6E,GAAG,CAAC,qBAAqB,CAAC;MACrDN,MAAM,CAAC,wBAAwB,EAAEK,QAAQ,CAACE,IAAI,CAAC;IACjD,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IACpC;EACF,CAAC;EAED;EACA,MAAMe,qBAAqBA,CAAC;IAAEjB,MAAM;IAAEC;EAAS,CAAC,EAAE;IAAEiB,SAAS;IAAEC;EAAW,CAAC,EAAE;IAC3E,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM5F,eAAe,CAACyF,qBAAqB,CAACC,SAAS,EAAEC,UAAU,CAAC;MAEjF,IAAIC,MAAM,CAACC,OAAO,EAAE;QAClBrB,MAAM,CAAC,oBAAoB,EAAE;UAC3BW,IAAI,EAAE,kBAAkB;UACxBC,OAAO,EAAE,cAAcM,SAAS,EAAE;UAClCX,IAAI,EAAE;YAAEW,SAAS;YAAEC;UAAW;QAChC,CAAC,CAAC;;QAEF;QACA,MAAMlB,QAAQ,CAAC,iBAAiB,EAAEiB,SAAS,CAAC;MAC9C;MAEA,OAAOE,MAAM;IACf,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMoB,eAAeA,CAAC;IAAEtB;EAAO,CAAC,EAAEkB,SAAS,EAAE;IAC3ClB,MAAM,CAAC,eAAe,EAAE,IAAI,CAAC;IAE7B,IAAI;MACF,MAAMuB,QAAQ,GAAG,MAAM/F,eAAe,CAACgG,yBAAyB,CAACN,SAAS,CAAC;;MAE3E;MACAlB,MAAM,CAAC,gBAAgB,EAAE;QACvBhE,mBAAmB,EAAEuF,QAAQ,CAACE,WAAW,CAACC,iBAAiB;QAC3DtF,UAAU,EAAEmF,QAAQ,CAACI,iBAAiB;QACtC,GAAGJ,QAAQ,CAACE;MACd,CAAC,CAAC;;MAEF;MACAzB,MAAM,CAAC,8BAA8B,EAAEuB,QAAQ,CAAC9C,WAAW,CAAC;;MAE5D;MACAuB,MAAM,CAAC,wBAAwB,EAAE,IAAInB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;MAE1DkB,MAAM,CAAC,oBAAoB,EAAE;QAC3BW,IAAI,EAAE,mBAAmB;QACzBC,OAAO,EAAE,QAAQM,SAAS,QAAQ;QAClCX,IAAI,EAAEgB;MACR,CAAC,CAAC;MAEF,OAAOA,QAAQ;IACjB,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChC,MAAMA,KAAK;IACb,CAAC,SAAS;MACRF,MAAM,CAAC,eAAe,EAAE,KAAK,CAAC;IAChC;EACF,CAAC;EAED;EACA,MAAM4B,2BAA2BA,CAAC;IAAE5B,MAAM;IAAEC;EAAS,CAAC,EAAEtB,UAAU,EAAE;IAClE,IAAI;MACF;MACA,QAAQA,UAAU,CAACkD,MAAM;QACvB,KAAK,wBAAwB;UAC3B,MAAM5B,QAAQ,CAAC,sBAAsB,CAAC;UACtC;QACF,KAAK,wBAAwB;UAC3B,MAAMA,QAAQ,CAAC,sBAAsB,CAAC;UACtC;QACF,KAAK,wBAAwB;UAC3B,MAAMA,QAAQ,CAAC,sBAAsB,CAAC;UACtC;QACF,KAAK,wBAAwB;UAC3B,MAAMA,QAAQ,CAAC,uBAAuB,CAAC;UACvC;MACJ;MAEAD,MAAM,CAAC,wBAAwB,EAAErB,UAAU,CAAC;MAC5CqB,MAAM,CAAC,oBAAoB,EAAE;QAC3BW,IAAI,EAAE,oBAAoB;QAC1BC,OAAO,EAAE,WAAWjC,UAAU,CAACmD,KAAK,EAAE;QACtCvB,IAAI,EAAE5B;MACR,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAM6B,oBAAoBA,CAAC;IAAE/B;EAAO,CAAC,EAAE;IACrC,MAAMvC,IAAI,GAAG;MACXS,EAAE,EAAE,mBAAmBW,IAAI,CAACU,GAAG,CAAC,CAAC,EAAE;MACnCsB,IAAI,EAAE,QAAQ;MACdF,IAAI,EAAE,sBAAsB;MAC5BqB,WAAW,EAAE,mBAAmB;MAChCtE,QAAQ,EAAE,IAAI;MACduE,UAAU,EAAE;QACVC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE;MACZ,CAAC;MACDrC,OAAO,EAAE;QACPa,IAAI,EAAE;MACR;IACF,CAAC;IAEDnF,eAAe,CAAC4G,iBAAiB,CAAC3E,IAAI,CAAC;IACvCuC,MAAM,CAAC,qBAAqB,EAAEvC,IAAI,CAAC;EACrC,CAAC;EAED;EACA,MAAM4E,oBAAoBA,CAAC;IAAErC;EAAO,CAAC,EAAE;IACrC,MAAMvC,IAAI,GAAG;MACXS,EAAE,EAAE,mBAAmBW,IAAI,CAACU,GAAG,CAAC,CAAC,EAAE;MACnCsB,IAAI,EAAE,QAAQ;MACdF,IAAI,EAAE,wBAAwB;MAC9BqB,WAAW,EAAE,eAAe;MAC5BtE,QAAQ,EAAE,IAAI;MACduE,UAAU,EAAE;QACVC,OAAO,EAAE,gBAAgB;QACzBC,QAAQ,EAAE;MACZ,CAAC;MACDrC,OAAO,EAAE;QACPa,IAAI,EAAE;MACR;IACF,CAAC;IAEDnF,eAAe,CAAC4G,iBAAiB,CAAC3E,IAAI,CAAC;IACvCuC,MAAM,CAAC,qBAAqB,EAAEvC,IAAI,CAAC;EACrC,CAAC;EAED;EACA,MAAM6E,oBAAoBA,CAAC;IAAEtC;EAAO,CAAC,EAAE;IACrC,MAAMvC,IAAI,GAAG;MACXS,EAAE,EAAE,mBAAmBW,IAAI,CAACU,GAAG,CAAC,CAAC,EAAE;MACnCsB,IAAI,EAAE,MAAM;MACZF,IAAI,EAAE,mBAAmB;MACzBqB,WAAW,EAAE,kBAAkB;MAC/BtE,QAAQ,EAAE,IAAI;MACduE,UAAU,EAAE;QACVC,OAAO,EAAE,YAAY;QACrBC,QAAQ,EAAE;MACZ,CAAC;MACDrC,OAAO,EAAE;QACPa,IAAI,EAAE;MACR;IACF,CAAC;IAEDnF,eAAe,CAAC4G,iBAAiB,CAAC3E,IAAI,CAAC;IACvCuC,MAAM,CAAC,qBAAqB,EAAEvC,IAAI,CAAC;EACrC,CAAC;EAED;EACA8E,yBAAyBA,CAAC;IAAEvC,MAAM;IAAEtE;EAAM,CAAC,EAAE;IAC3C,IAAIA,KAAK,CAACa,mBAAmB,EAAE;IAE/ByD,MAAM,CAAC,wBAAwB,EAAE,IAAI,CAAC;;IAEtC;IACAwC,WAAW,CAAC,MAAM;MAChB;MACA;IAAA,CACD,EAAE,KAAK,CAAC,EAAC;;IAEVxC,MAAM,CAAC,oBAAoB,EAAE;MAC3BW,IAAI,EAAE,oBAAoB;MAC1BC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED;EACA6B,wBAAwBA,CAAC;IAAEzC;EAAO,CAAC,EAAE;IACnCA,MAAM,CAAC,wBAAwB,EAAE,KAAK,CAAC;IACvCA,MAAM,CAAC,oBAAoB,EAAE;MAC3BW,IAAI,EAAE,oBAAoB;MAC1BC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ;AACF,CAAC;AAED,eAAe;EACb8B,UAAU,EAAE,IAAI;EAChBhH,KAAK;EACLkB,OAAO;EACPS,SAAS;EACTyC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}