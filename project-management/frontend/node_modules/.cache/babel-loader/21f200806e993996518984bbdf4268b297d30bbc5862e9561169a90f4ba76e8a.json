{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, createBlock as _createBlock, vShow as _vShow, withDirectives as _withDirectives, normalizeClass as _normalizeClass } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dashboard\"\n};\nconst _hoisted_2 = {\n  class: \"d-flex justify-space-between align-center\"\n};\nconst _hoisted_3 = {\n  class: \"text-subtitle-1 text-medium-emphasis\"\n};\nconst _hoisted_4 = {\n  class: \"d-flex align-center\"\n};\nconst _hoisted_5 = {\n  class: \"text-h3 font-weight-bold\"\n};\nconst _hoisted_6 = {\n  class: \"d-flex align-center\"\n};\nconst _hoisted_7 = {\n  class: \"text-h3 font-weight-bold\"\n};\nconst _hoisted_8 = {\n  class: \"d-flex align-center\"\n};\nconst _hoisted_9 = {\n  class: \"text-h3 font-weight-bold\"\n};\nconst _hoisted_10 = {\n  class: \"d-flex align-center\"\n};\nconst _hoisted_11 = {\n  class: \"text-h3 font-weight-bold\"\n};\nconst _hoisted_12 = {\n  key: 0,\n  class: \"d-flex justify-center align-center\",\n  style: {\n    \"height\": \"300px\"\n  }\n};\nconst _hoisted_13 = {\n  key: 1,\n  class: \"text-center py-8\"\n};\nconst _hoisted_14 = {\n  key: 2,\n  class: \"chart-container\",\n  style: {\n    \"height\": \"300px\"\n  }\n};\nconst _hoisted_15 = {\n  key: 0,\n  class: \"d-flex justify-center align-center\",\n  style: {\n    \"height\": \"100px\"\n  }\n};\nconst _hoisted_16 = {\n  key: 1,\n  class: \"text-center py-4\"\n};\nconst _hoisted_17 = {\n  key: 0,\n  class: \"d-flex justify-center align-center\",\n  style: {\n    \"height\": \"100px\"\n  }\n};\nconst _hoisted_18 = {\n  key: 1,\n  class: \"text-center py-4\"\n};\nconst _hoisted_19 = {\n  key: 0,\n  class: \"d-flex justify-center align-center\",\n  style: {\n    \"height\": \"200px\"\n  }\n};\nconst _hoisted_20 = {\n  key: 1,\n  class: \"text-center py-8\"\n};\nconst _hoisted_21 = {\n  key: 0,\n  class: \"d-flex justify-center align-center\",\n  style: {\n    \"height\": \"200px\"\n  }\n};\nconst _hoisted_22 = {\n  key: 1,\n  class: \"text-center py-8\"\n};\nconst _hoisted_23 = {\n  class: \"d-flex justify-space-between align-center\"\n};\nconst _hoisted_24 = {\n  class: \"text-body-1\"\n};\nconst _hoisted_25 = {\n  class: \"text-caption text-medium-emphasis\"\n};\nconst _hoisted_26 = {\n  class: \"text-caption text-medium-emphasis\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_v_icon = _resolveComponent(\"v-icon\");\n  const _component_v_col = _resolveComponent(\"v-col\");\n  const _component_v_row = _resolveComponent(\"v-row\");\n  const _component_v_alert = _resolveComponent(\"v-alert\");\n  const _component_v_btn = _resolveComponent(\"v-btn\");\n  const _component_v_card_title = _resolveComponent(\"v-card-title\");\n  const _component_v_card_subtitle = _resolveComponent(\"v-card-subtitle\");\n  const _component_v_card_item = _resolveComponent(\"v-card-item\");\n  const _component_v_card_text = _resolveComponent(\"v-card-text\");\n  const _component_v_card_actions = _resolveComponent(\"v-card-actions\");\n  const _component_v_card = _resolveComponent(\"v-card\");\n  const _component_v_progress_circular = _resolveComponent(\"v-progress-circular\");\n  const _component_BarChart = _resolveComponent(\"BarChart\");\n  const _component_v_avatar = _resolveComponent(\"v-avatar\");\n  const _component_v_list_item_title = _resolveComponent(\"v-list-item-title\");\n  const _component_v_list_item_subtitle = _resolveComponent(\"v-list-item-subtitle\");\n  const _component_v_list_item = _resolveComponent(\"v-list-item\");\n  const _component_v_list = _resolveComponent(\"v-list\");\n  const _component_v_spacer = _resolveComponent(\"v-spacer\");\n  const _component_ProjectRelationshipDashboard = _resolveComponent(\"ProjectRelationshipDashboard\");\n  const _component_v_expand_transition = _resolveComponent(\"v-expand-transition\");\n  const _component_v_checkbox = _resolveComponent(\"v-checkbox\");\n  const _component_v_chip = _resolveComponent(\"v-chip\");\n  const _component_v_timeline_item = _resolveComponent(\"v-timeline-item\");\n  const _component_v_timeline = _resolveComponent(\"v-timeline\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 系统标题 \"), _createVNode(_component_v_row, {\n    class: \"mb-6\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_v_col, {\n      cols: \"12\",\n      class: \"text-center\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_v_icon, {\n        size: \"64\",\n        color: \"primary\",\n        class: \"mb-4\"\n      }, {\n        default: _withCtx(() => _cache[9] || (_cache[9] = [_createTextVNode(\"mdi-hospital-building\")])),\n        _: 1 /* STABLE */,\n        __: [9]\n      }), _cache[10] || (_cache[10] = _createElementVNode(\"h1\", {\n        class: \"text-h3 font-weight-bold text-primary mb-2\"\n      }, \"医疗项目实施进度管理系统\", -1 /* HOISTED */)), _cache[11] || (_cache[11] = _createElementVNode(\"p\", {\n        class: \"text-h6 text-medium-emphasis\"\n      }, \"Medical Project Implementation Progress Management System\", -1 /* HOISTED */))]),\n      _: 1 /* STABLE */,\n      __: [10, 11]\n    })]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 系统状态 \"), _createVNode(_component_v_row, {\n    class: \"mb-4\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_v_col, {\n      cols: \"12\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_v_alert, {\n        type: \"success\",\n        variant: \"tonal\",\n        \"prepend-icon\": \"mdi-check-circle\"\n      }, {\n        default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\" 系统运行正常 | 数据库连接成功 | 端口: 8090 \")])),\n        _: 1 /* STABLE */,\n        __: [12]\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 页面标题 \"), _createVNode(_component_v_row, {\n    class: \"mb-4\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_v_col, {\n      cols: \"12\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", null, [_cache[13] || (_cache[13] = _createElementVNode(\"h2\", {\n        class: \"text-h4 font-weight-bold\"\n      }, \"项目管理仪表板\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_3, \" 欢迎回来，\" + _toDisplayString(_ctx.user ? _ctx.user.fullName || _ctx.user.username || _ctx.user.name : '演示用户') + \"！这里是您的项目概览。 \", 1 /* TEXT */)]), _createVNode(_component_v_btn, {\n        color: \"primary\",\n        size: \"large\",\n        \"prepend-icon\": \"mdi-plus\",\n        onClick: _cache[0] || (_cache[0] = $event => _ctx.$router.push('/projects/create'))\n      }, {\n        default: _withCtx(() => _cache[14] || (_cache[14] = [_createTextVNode(\" 新建项目 \")])),\n        _: 1 /* STABLE */,\n        __: [14]\n      })])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 项目概览卡片 \"), _createVNode(_component_v_row, null, {\n    default: _withCtx(() => [_createVNode(_component_v_col, {\n      cols: \"12\",\n      md: \"3\",\n      sm: \"6\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_v_card, {\n        class: \"dashboard-card\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_v_card_item, null, {\n          default: _withCtx(() => [_createVNode(_component_v_card_title, null, {\n            default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_v_icon, {\n              size: \"large\",\n              color: \"primary\",\n              class: \"me-2\"\n            }, {\n              default: _withCtx(() => _cache[15] || (_cache[15] = [_createTextVNode(\"mdi-folder-multiple\")])),\n              _: 1 /* STABLE */,\n              __: [15]\n            }), _cache[16] || (_cache[16] = _createElementVNode(\"span\", null, \"项目总数\", -1 /* HOISTED */))])]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_v_card_subtitle, null, {\n            default: _withCtx(() => _cache[17] || (_cache[17] = [_createTextVNode(\"所有项目\")])),\n            _: 1 /* STABLE */,\n            __: [17]\n          })]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_v_card_text, null, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, _toDisplayString(_ctx.projectsCount), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_v_card_actions, null, {\n          default: _withCtx(() => [_createVNode(_component_v_btn, {\n            variant: \"text\",\n            color: \"primary\",\n            onClick: _cache[1] || (_cache[1] = $event => _ctx.$router.push('/projects'))\n          }, {\n            default: _withCtx(() => [_cache[19] || (_cache[19] = _createTextVNode(\" 查看所有项目 \")), _createVNode(_component_v_icon, {\n              end: \"\"\n            }, {\n              default: _withCtx(() => _cache[18] || (_cache[18] = [_createTextVNode(\"mdi-arrow-right\")])),\n              _: 1 /* STABLE */,\n              __: [18]\n            })]),\n            _: 1 /* STABLE */,\n            __: [19]\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_v_col, {\n      cols: \"12\",\n      md: \"3\",\n      sm: \"6\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_v_card, {\n        class: \"dashboard-card\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_v_card_item, null, {\n          default: _withCtx(() => [_createVNode(_component_v_card_title, null, {\n            default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_v_icon, {\n              size: \"large\",\n              color: \"info\",\n              class: \"me-2\"\n            }, {\n              default: _withCtx(() => _cache[20] || (_cache[20] = [_createTextVNode(\"mdi-progress-clock\")])),\n              _: 1 /* STABLE */,\n              __: [20]\n            }), _cache[21] || (_cache[21] = _createElementVNode(\"span\", null, \"进行中\", -1 /* HOISTED */))])]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_v_card_subtitle, null, {\n            default: _withCtx(() => _cache[22] || (_cache[22] = [_createTextVNode(\"正在实施的项目\")])),\n            _: 1 /* STABLE */,\n            __: [22]\n          })]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_v_card_text, null, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_7, _toDisplayString(_ctx.inProgressProjectsCount), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_v_card_actions, null, {\n          default: _withCtx(() => [_createVNode(_component_v_btn, {\n            variant: \"text\",\n            color: \"info\",\n            onClick: _cache[2] || (_cache[2] = $event => _ctx.$router.push('/projects?status=in-progress'))\n          }, {\n            default: _withCtx(() => [_cache[24] || (_cache[24] = _createTextVNode(\" 查看进行中项目 \")), _createVNode(_component_v_icon, {\n              end: \"\"\n            }, {\n              default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\"mdi-arrow-right\")])),\n              _: 1 /* STABLE */,\n              __: [23]\n            })]),\n            _: 1 /* STABLE */,\n            __: [24]\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_v_col, {\n      cols: \"12\",\n      md: \"3\",\n      sm: \"6\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_v_card, {\n        class: \"dashboard-card\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_v_card_item, null, {\n          default: _withCtx(() => [_createVNode(_component_v_card_title, null, {\n            default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_v_icon, {\n              size: \"large\",\n              color: \"success\",\n              class: \"me-2\"\n            }, {\n              default: _withCtx(() => _cache[25] || (_cache[25] = [_createTextVNode(\"mdi-check-circle\")])),\n              _: 1 /* STABLE */,\n              __: [25]\n            }), _cache[26] || (_cache[26] = _createElementVNode(\"span\", null, \"已完成\", -1 /* HOISTED */))])]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_v_card_subtitle, null, {\n            default: _withCtx(() => _cache[27] || (_cache[27] = [_createTextVNode(\"已完成的项目\")])),\n            _: 1 /* STABLE */,\n            __: [27]\n          })]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_v_card_text, null, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_9, _toDisplayString(_ctx.completedProjectsCount), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_v_card_actions, null, {\n          default: _withCtx(() => [_createVNode(_component_v_btn, {\n            variant: \"text\",\n            color: \"success\",\n            onClick: _cache[3] || (_cache[3] = $event => _ctx.$router.push('/projects?status=completed'))\n          }, {\n            default: _withCtx(() => [_cache[29] || (_cache[29] = _createTextVNode(\" 查看已完成项目 \")), _createVNode(_component_v_icon, {\n              end: \"\"\n            }, {\n              default: _withCtx(() => _cache[28] || (_cache[28] = [_createTextVNode(\"mdi-arrow-right\")])),\n              _: 1 /* STABLE */,\n              __: [28]\n            })]),\n            _: 1 /* STABLE */,\n            __: [29]\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_v_col, {\n      cols: \"12\",\n      md: \"3\",\n      sm: \"6\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_v_card, {\n        class: \"dashboard-card\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_v_card_item, null, {\n          default: _withCtx(() => [_createVNode(_component_v_card_title, null, {\n            default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_v_icon, {\n              size: \"large\",\n              color: \"error\",\n              class: \"me-2\"\n            }, {\n              default: _withCtx(() => _cache[30] || (_cache[30] = [_createTextVNode(\"mdi-alert-circle\")])),\n              _: 1 /* STABLE */,\n              __: [30]\n            }), _cache[31] || (_cache[31] = _createElementVNode(\"span\", null, \"风险预警\", -1 /* HOISTED */))])]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_v_card_subtitle, null, {\n            default: _withCtx(() => _cache[32] || (_cache[32] = [_createTextVNode(\"需要关注的风险\")])),\n            _: 1 /* STABLE */,\n            __: [32]\n          })]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_v_card_text, null, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_11, _toDisplayString(_ctx.riskWarningsCount), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_v_card_actions, null, {\n          default: _withCtx(() => [_createVNode(_component_v_btn, {\n            variant: \"text\",\n            color: \"error\",\n            onClick: _cache[4] || (_cache[4] = $event => _ctx.$router.push('/risks'))\n          }, {\n            default: _withCtx(() => [_cache[34] || (_cache[34] = _createTextVNode(\" 查看风险详情 \")), _createVNode(_component_v_icon, {\n              end: \"\"\n            }, {\n              default: _withCtx(() => _cache[33] || (_cache[33] = [_createTextVNode(\"mdi-arrow-right\")])),\n              _: 1 /* STABLE */,\n              __: [33]\n            })]),\n            _: 1 /* STABLE */,\n            __: [34]\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 项目进度图表 \"), _createVNode(_component_v_row, {\n    class: \"mt-4\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_v_col, {\n      cols: \"12\",\n      lg: \"8\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_v_card, null, {\n        default: _withCtx(() => [_createVNode(_component_v_card_title, {\n          class: \"d-flex align-center\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_icon, {\n            color: \"primary\",\n            class: \"me-2\"\n          }, {\n            default: _withCtx(() => _cache[35] || (_cache[35] = [_createTextVNode(\"mdi-chart-timeline-variant\")])),\n            _: 1 /* STABLE */,\n            __: [35]\n          }), _cache[36] || (_cache[36] = _createTextVNode(\" 项目进度概览 \"))]),\n          _: 1 /* STABLE */,\n          __: [36]\n        }), _createVNode(_component_v_card_text, null, {\n          default: _withCtx(() => [_ctx.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_createVNode(_component_v_progress_circular, {\n            indeterminate: \"\",\n            color: \"primary\"\n          })])) : _ctx.projects.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [_createVNode(_component_v_icon, {\n            size: \"large\",\n            color: \"grey\"\n          }, {\n            default: _withCtx(() => _cache[37] || (_cache[37] = [_createTextVNode(\"mdi-chart-timeline-variant\")])),\n            _: 1 /* STABLE */,\n            __: [37]\n          }), _cache[38] || (_cache[38] = _createElementVNode(\"p\", {\n            class: \"text-body-1 text-medium-emphasis mt-2\"\n          }, \"暂无项目数据\", -1 /* HOISTED */))])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [_createCommentVNode(\" 这里将使用Chart.js渲染项目进度图表 \"), _createVNode(_component_BarChart, {\n            \"chart-data\": $options.projectProgressChartData,\n            options: $data.chartOptions\n          }, null, 8 /* PROPS */, [\"chart-data\", \"options\"])]))]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_v_col, {\n      cols: \"12\",\n      lg: \"4\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_v_card, {\n        class: \"mb-4\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_v_card_title, {\n          class: \"d-flex align-center\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_icon, {\n            color: \"primary\",\n            class: \"me-2\"\n          }, {\n            default: _withCtx(() => _cache[39] || (_cache[39] = [_createTextVNode(\"mdi-calendar-check\")])),\n            _: 1 /* STABLE */,\n            __: [39]\n          }), _cache[40] || (_cache[40] = _createTextVNode(\" 今日会议 \"))]),\n          _: 1 /* STABLE */,\n          __: [40]\n        }), _createVNode(_component_v_card_text, null, {\n          default: _withCtx(() => [_ctx.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [_createVNode(_component_v_progress_circular, {\n            indeterminate: \"\",\n            color: \"primary\"\n          })])) : $data.todayMeetings.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_16, [_createVNode(_component_v_icon, {\n            color: \"grey\"\n          }, {\n            default: _withCtx(() => _cache[41] || (_cache[41] = [_createTextVNode(\"mdi-calendar-blank\")])),\n            _: 1 /* STABLE */,\n            __: [41]\n          }), _cache[42] || (_cache[42] = _createElementVNode(\"p\", {\n            class: \"text-body-2 text-medium-emphasis mt-2\"\n          }, \"今日无会议安排\", -1 /* HOISTED */))])) : (_openBlock(), _createBlock(_component_v_list, {\n            key: 2,\n            lines: \"two\"\n          }, {\n            default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.todayMeetings, meeting => {\n              return _openBlock(), _createBlock(_component_v_list_item, {\n                key: meeting.id,\n                to: `/meetings/${meeting.id}`\n              }, {\n                prepend: _withCtx(() => [_createVNode(_component_v_avatar, {\n                  color: \"primary\",\n                  variant: \"tonal\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_v_icon, null, {\n                    default: _withCtx(() => [...(_cache[43] || (_cache[43] = [_createTextVNode(\"mdi-calendar-clock\")]))]),\n                    _: 1 /* STABLE */,\n                    __: [43]\n                  })]),\n                  _: 1 /* STABLE */\n                })]),\n                default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n                  default: _withCtx(() => [_createTextVNode(_toDisplayString(meeting.title), 1 /* TEXT */)]),\n                  _: 2 /* DYNAMIC */\n                }, 1024 /* DYNAMIC_SLOTS */), _createVNode(_component_v_list_item_subtitle, null, {\n                  default: _withCtx(() => [_createTextVNode(_toDisplayString($options.formatTime(meeting.startTime)) + \" - \" + _toDisplayString($options.formatTime(meeting.endTime)), 1 /* TEXT */)]),\n                  _: 2 /* DYNAMIC */\n                }, 1024 /* DYNAMIC_SLOTS */)]),\n                _: 2 /* DYNAMIC */\n              }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"to\"]);\n            }), 128 /* KEYED_FRAGMENT */))]),\n            _: 1 /* STABLE */\n          }))]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_v_card_actions, null, {\n          default: _withCtx(() => [_createVNode(_component_v_btn, {\n            variant: \"text\",\n            color: \"primary\",\n            onClick: _cache[5] || (_cache[5] = $event => _ctx.$router.push('/meetings'))\n          }, {\n            default: _withCtx(() => [_cache[45] || (_cache[45] = _createTextVNode(\" 查看所有会议 \")), _createVNode(_component_v_icon, {\n              end: \"\"\n            }, {\n              default: _withCtx(() => _cache[44] || (_cache[44] = [_createTextVNode(\"mdi-arrow-right\")])),\n              _: 1 /* STABLE */,\n              __: [44]\n            })]),\n            _: 1 /* STABLE */,\n            __: [45]\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_v_card, null, {\n        default: _withCtx(() => [_createVNode(_component_v_card_title, {\n          class: \"d-flex align-center\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_icon, {\n            color: \"error\",\n            class: \"me-2\"\n          }, {\n            default: _withCtx(() => _cache[46] || (_cache[46] = [_createTextVNode(\"mdi-alert\")])),\n            _: 1 /* STABLE */,\n            __: [46]\n          }), _cache[47] || (_cache[47] = _createTextVNode(\" 风险预警 \"))]),\n          _: 1 /* STABLE */,\n          __: [47]\n        }), _createVNode(_component_v_card_text, null, {\n          default: _withCtx(() => [_ctx.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, [_createVNode(_component_v_progress_circular, {\n            indeterminate: \"\",\n            color: \"primary\"\n          })])) : $data.riskWarnings.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [_createVNode(_component_v_icon, {\n            color: \"grey\"\n          }, {\n            default: _withCtx(() => _cache[48] || (_cache[48] = [_createTextVNode(\"mdi-shield-check\")])),\n            _: 1 /* STABLE */,\n            __: [48]\n          }), _cache[49] || (_cache[49] = _createElementVNode(\"p\", {\n            class: \"text-body-2 text-medium-emphasis mt-2\"\n          }, \"暂无风险预警\", -1 /* HOISTED */))])) : (_openBlock(), _createBlock(_component_v_list, {\n            key: 2,\n            lines: \"two\"\n          }, {\n            default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.riskWarnings, risk => {\n              return _openBlock(), _createBlock(_component_v_list_item, {\n                key: risk.id,\n                to: `/risks/${risk.id}`\n              }, {\n                prepend: _withCtx(() => [_createVNode(_component_v_avatar, {\n                  color: $options.getRiskColor(risk),\n                  variant: \"tonal\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_v_icon, null, {\n                    default: _withCtx(() => [...(_cache[50] || (_cache[50] = [_createTextVNode(\"mdi-alert-circle\")]))]),\n                    _: 1 /* STABLE */,\n                    __: [50]\n                  })]),\n                  _: 2 /* DYNAMIC */\n                }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"color\"])]),\n                default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n                  default: _withCtx(() => [_createTextVNode(_toDisplayString(risk.title), 1 /* TEXT */)]),\n                  _: 2 /* DYNAMIC */\n                }, 1024 /* DYNAMIC_SLOTS */), _createVNode(_component_v_list_item_subtitle, null, {\n                  default: _withCtx(() => [_createTextVNode(_toDisplayString(risk.projectName) + \" | 影响: \" + _toDisplayString(risk.impactLevel) + \" | 可能性: \" + _toDisplayString(risk.probability), 1 /* TEXT */)]),\n                  _: 2 /* DYNAMIC */\n                }, 1024 /* DYNAMIC_SLOTS */)]),\n                _: 2 /* DYNAMIC */\n              }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"to\"]);\n            }), 128 /* KEYED_FRAGMENT */))]),\n            _: 1 /* STABLE */\n          }))]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_v_card_actions, null, {\n          default: _withCtx(() => [_createVNode(_component_v_btn, {\n            variant: \"text\",\n            color: \"error\",\n            onClick: _cache[6] || (_cache[6] = $event => _ctx.$router.push('/risks'))\n          }, {\n            default: _withCtx(() => [_cache[52] || (_cache[52] = _createTextVNode(\" 查看所有风险 \")), _createVNode(_component_v_icon, {\n              end: \"\"\n            }, {\n              default: _withCtx(() => _cache[51] || (_cache[51] = [_createTextVNode(\"mdi-arrow-right\")])),\n              _: 1 /* STABLE */,\n              __: [51]\n            })]),\n            _: 1 /* STABLE */,\n            __: [52]\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 项目关联管理 \"), _createVNode(_component_v_row, {\n    class: \"mt-4\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_v_col, {\n      cols: \"12\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_v_card, null, {\n        default: _withCtx(() => [_createVNode(_component_v_card_title, {\n          class: \"d-flex align-center\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_icon, {\n            color: \"primary\",\n            class: \"me-2\"\n          }, {\n            default: _withCtx(() => _cache[53] || (_cache[53] = [_createTextVNode(\"mdi-chart-box\")])),\n            _: 1 /* STABLE */,\n            __: [53]\n          }), _cache[54] || (_cache[54] = _createTextVNode(\" 项目关联管理 \")), _createVNode(_component_v_spacer), _createVNode(_component_v_btn, {\n            variant: \"outlined\",\n            size: \"small\",\n            onClick: _cache[7] || (_cache[7] = $event => $data.showRelationshipDashboard = !$data.showRelationshipDashboard)\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($data.showRelationshipDashboard ? '收起' : '展开') + \" \", 1 /* TEXT */), _createVNode(_component_v_icon, {\n              end: \"\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($data.showRelationshipDashboard ? 'mdi-chevron-up' : 'mdi-chevron-down'), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */,\n          __: [54]\n        }), _createVNode(_component_v_expand_transition, null, {\n          default: _withCtx(() => [_withDirectives(_createElementVNode(\"div\", null, [_createVNode(_component_v_card_text, null, {\n            default: _withCtx(() => [_createVNode(_component_ProjectRelationshipDashboard)]),\n            _: 1 /* STABLE */\n          })], 512 /* NEED_PATCH */), [[_vShow, $data.showRelationshipDashboard]])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 最近任务和活动 \"), _createVNode(_component_v_row, {\n    class: \"mt-4\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_v_col, {\n      cols: \"12\",\n      md: \"6\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_v_card, null, {\n        default: _withCtx(() => [_createVNode(_component_v_card_title, {\n          class: \"d-flex align-center\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_icon, {\n            color: \"primary\",\n            class: \"me-2\"\n          }, {\n            default: _withCtx(() => _cache[55] || (_cache[55] = [_createTextVNode(\"mdi-clipboard-check\")])),\n            _: 1 /* STABLE */,\n            __: [55]\n          }), _cache[56] || (_cache[56] = _createTextVNode(\" 我的任务 \"))]),\n          _: 1 /* STABLE */,\n          __: [56]\n        }), _createVNode(_component_v_card_text, null, {\n          default: _withCtx(() => [_ctx.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_19, [_createVNode(_component_v_progress_circular, {\n            indeterminate: \"\",\n            color: \"primary\"\n          })])) : $data.myTasks.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, [_createVNode(_component_v_icon, {\n            size: \"large\",\n            color: \"grey\"\n          }, {\n            default: _withCtx(() => _cache[57] || (_cache[57] = [_createTextVNode(\"mdi-clipboard-check\")])),\n            _: 1 /* STABLE */,\n            __: [57]\n          }), _cache[58] || (_cache[58] = _createElementVNode(\"p\", {\n            class: \"text-body-1 text-medium-emphasis mt-2\"\n          }, \"暂无分配给您的任务\", -1 /* HOISTED */))])) : (_openBlock(), _createBlock(_component_v_list, {\n            key: 2,\n            lines: \"two\"\n          }, {\n            default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.myTasks, task => {\n              return _openBlock(), _createBlock(_component_v_list_item, {\n                key: task.id,\n                to: `/tasks/${task.id}`\n              }, {\n                prepend: _withCtx(() => [_createVNode(_component_v_checkbox, {\n                  modelValue: task.completed,\n                  \"onUpdate:modelValue\": $event => task.completed = $event,\n                  onChange: $event => $options.updateTaskStatus(task),\n                  \"hide-details\": \"\"\n                }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\", \"onChange\"])]),\n                append: _withCtx(() => [_createVNode(_component_v_chip, {\n                  color: $options.getTaskStatusColor(task.status),\n                  size: \"small\"\n                }, {\n                  default: _withCtx(() => [_createTextVNode(_toDisplayString($options.getTaskStatusText(task.status)), 1 /* TEXT */)]),\n                  _: 2 /* DYNAMIC */\n                }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"color\"])]),\n                default: _withCtx(() => [_createVNode(_component_v_list_item_title, {\n                  class: _normalizeClass({\n                    'text-decoration-line-through': task.status === 'completed'\n                  })\n                }, {\n                  default: _withCtx(() => [_createTextVNode(_toDisplayString(task.name), 1 /* TEXT */)]),\n                  _: 2 /* DYNAMIC */\n                }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"class\"]), _createVNode(_component_v_list_item_subtitle, null, {\n                  default: _withCtx(() => [_createTextVNode(_toDisplayString(task.projectName) + \" | \" + _toDisplayString(task.phaseName) + \" | 截止日期: \" + _toDisplayString($options.formatDate(task.dueDate)), 1 /* TEXT */)]),\n                  _: 2 /* DYNAMIC */\n                }, 1024 /* DYNAMIC_SLOTS */)]),\n                _: 2 /* DYNAMIC */\n              }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"to\"]);\n            }), 128 /* KEYED_FRAGMENT */))]),\n            _: 1 /* STABLE */\n          }))]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_v_card_actions, null, {\n          default: _withCtx(() => [_createVNode(_component_v_btn, {\n            variant: \"text\",\n            color: \"primary\",\n            onClick: _cache[8] || (_cache[8] = $event => _ctx.$router.push('/tasks'))\n          }, {\n            default: _withCtx(() => [_cache[60] || (_cache[60] = _createTextVNode(\" 查看所有任务 \")), _createVNode(_component_v_icon, {\n              end: \"\"\n            }, {\n              default: _withCtx(() => _cache[59] || (_cache[59] = [_createTextVNode(\"mdi-arrow-right\")])),\n              _: 1 /* STABLE */,\n              __: [59]\n            })]),\n            _: 1 /* STABLE */,\n            __: [60]\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_v_col, {\n      cols: \"12\",\n      md: \"6\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_v_card, null, {\n        default: _withCtx(() => [_createVNode(_component_v_card_title, {\n          class: \"d-flex align-center\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_icon, {\n            color: \"primary\",\n            class: \"me-2\"\n          }, {\n            default: _withCtx(() => _cache[61] || (_cache[61] = [_createTextVNode(\"mdi-history\")])),\n            _: 1 /* STABLE */,\n            __: [61]\n          }), _cache[62] || (_cache[62] = _createTextVNode(\" 最近活动 \"))]),\n          _: 1 /* STABLE */,\n          __: [62]\n        }), _createVNode(_component_v_card_text, null, {\n          default: _withCtx(() => [_ctx.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_21, [_createVNode(_component_v_progress_circular, {\n            indeterminate: \"\",\n            color: \"primary\"\n          })])) : $data.recentActivities.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_22, [_createVNode(_component_v_icon, {\n            size: \"large\",\n            color: \"grey\"\n          }, {\n            default: _withCtx(() => _cache[63] || (_cache[63] = [_createTextVNode(\"mdi-history\")])),\n            _: 1 /* STABLE */,\n            __: [63]\n          }), _cache[64] || (_cache[64] = _createElementVNode(\"p\", {\n            class: \"text-body-1 text-medium-emphasis mt-2\"\n          }, \"暂无最近活动\", -1 /* HOISTED */))])) : (_openBlock(), _createBlock(_component_v_timeline, {\n            key: 2,\n            align: \"start\",\n            side: \"end\",\n            \"truncate-line\": \"both\"\n          }, {\n            default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.recentActivities, activity => {\n              return _openBlock(), _createBlock(_component_v_timeline_item, {\n                key: activity.id,\n                \"dot-color\": $options.getActivityColor(activity.type),\n                icon: $options.getActivityIcon(activity.type),\n                size: \"small\"\n              }, {\n                default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"div\", null, [_createElementVNode(\"div\", _hoisted_24, _toDisplayString(activity.description), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_25, _toDisplayString(activity.projectName), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_26, _toDisplayString($options.formatDateTime(activity.timestamp)), 1 /* TEXT */)])]),\n                _: 2 /* DYNAMIC */\n              }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"dot-color\", \"icon\"]);\n            }), 128 /* KEYED_FRAGMENT */))]),\n            _: 1 /* STABLE */\n          }))]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "key", "style", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createVNode", "_component_v_row", "default", "_withCtx", "_component_v_col", "cols", "_component_v_icon", "size", "color", "_cache", "_createTextVNode", "_", "__", "_createElementVNode", "_component_v_alert", "type", "variant", "_hoisted_2", "_hoisted_3", "_toDisplayString", "_ctx", "user", "fullName", "username", "name", "_component_v_btn", "onClick", "$event", "$router", "push", "md", "sm", "_component_v_card", "_component_v_card_item", "_component_v_card_title", "_hoisted_4", "_component_v_card_subtitle", "_component_v_card_text", "_hoisted_5", "projectsCount", "_component_v_card_actions", "end", "_hoisted_6", "_hoisted_7", "inProgressProjectsCount", "_hoisted_8", "_hoisted_9", "completedProjectsCount", "_hoisted_10", "_hoisted_11", "riskWarningsCount", "lg", "loading", "_hoisted_12", "_component_v_progress_circular", "indeterminate", "projects", "length", "_hoisted_13", "_hoisted_14", "_component_<PERSON><PERSON><PERSON>", "$options", "projectProgressChartData", "options", "$data", "chartOptions", "_hoisted_15", "todayMeetings", "_hoisted_16", "_createBlock", "_component_v_list", "lines", "_Fragment", "_renderList", "meeting", "_component_v_list_item", "id", "to", "prepend", "_component_v_avatar", "_component_v_list_item_title", "title", "_component_v_list_item_subtitle", "formatTime", "startTime", "endTime", "_hoisted_17", "riskWarnings", "_hoisted_18", "risk", "getRiskColor", "projectName", "impactLevel", "probability", "_component_v_spacer", "showRelationshipDashboard", "_component_v_expand_transition", "_component_ProjectRelationshipDashboard", "_hoisted_19", "myTasks", "_hoisted_20", "task", "_component_v_checkbox", "modelValue", "completed", "onChange", "updateTaskStatus", "append", "_component_v_chip", "getTaskStatusColor", "status", "getTaskStatusText", "_normalizeClass", "phaseName", "formatDate", "dueDate", "_hoisted_21", "recentActivities", "_hoisted_22", "_component_v_timeline", "align", "side", "activity", "_component_v_timeline_item", "getActivityColor", "icon", "getActivityIcon", "_hoisted_23", "_hoisted_24", "description", "_hoisted_25", "_hoisted_26", "formatDateTime", "timestamp"], "sources": ["/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/技术测试/项目实施进度管理/project-management/frontend/src/views/Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard\">\n    <!-- 系统标题 -->\n    <v-row class=\"mb-6\">\n      <v-col cols=\"12\" class=\"text-center\">\n        <v-icon size=\"64\" color=\"primary\" class=\"mb-4\">mdi-hospital-building</v-icon>\n        <h1 class=\"text-h3 font-weight-bold text-primary mb-2\">医疗项目实施进度管理系统</h1>\n        <p class=\"text-h6 text-medium-emphasis\">Medical Project Implementation Progress Management System</p>\n      </v-col>\n    </v-row>\n\n    <!-- 系统状态 -->\n    <v-row class=\"mb-4\">\n      <v-col cols=\"12\">\n        <v-alert\n          type=\"success\"\n          variant=\"tonal\"\n          prepend-icon=\"mdi-check-circle\"\n        >\n          系统运行正常 | 数据库连接成功 | 端口: 8090\n        </v-alert>\n      </v-col>\n    </v-row>\n\n    <!-- 页面标题 -->\n    <v-row class=\"mb-4\">\n      <v-col cols=\"12\">\n        <div class=\"d-flex justify-space-between align-center\">\n          <div>\n            <h2 class=\"text-h4 font-weight-bold\">项目管理仪表板</h2>\n            <p class=\"text-subtitle-1 text-medium-emphasis\">\n              欢迎回来，{{ user ? user.fullName || user.username || user.name : '演示用户' }}！这里是您的项目概览。\n            </p>\n          </div>\n          <v-btn\n            color=\"primary\"\n            size=\"large\"\n            prepend-icon=\"mdi-plus\"\n            @click=\"$router.push('/projects/create')\"\n          >\n            新建项目\n          </v-btn>\n        </div>\n      </v-col>\n    </v-row>\n\n    <!-- 项目概览卡片 -->\n    <v-row>\n      <v-col cols=\"12\" md=\"3\" sm=\"6\">\n        <v-card class=\"dashboard-card\">\n          <v-card-item>\n            <v-card-title>\n              <div class=\"d-flex align-center\">\n                <v-icon size=\"large\" color=\"primary\" class=\"me-2\">mdi-folder-multiple</v-icon>\n                <span>项目总数</span>\n              </div>\n            </v-card-title>\n            <v-card-subtitle>所有项目</v-card-subtitle>\n          </v-card-item>\n          <v-card-text>\n            <div class=\"text-h3 font-weight-bold\">{{ projectsCount }}</div>\n          </v-card-text>\n          <v-card-actions>\n            <v-btn variant=\"text\" color=\"primary\" @click=\"$router.push('/projects')\">\n              查看所有项目\n              <v-icon end>mdi-arrow-right</v-icon>\n            </v-btn>\n          </v-card-actions>\n        </v-card>\n      </v-col>\n\n      <v-col cols=\"12\" md=\"3\" sm=\"6\">\n        <v-card class=\"dashboard-card\">\n          <v-card-item>\n            <v-card-title>\n              <div class=\"d-flex align-center\">\n                <v-icon size=\"large\" color=\"info\" class=\"me-2\">mdi-progress-clock</v-icon>\n                <span>进行中</span>\n              </div>\n            </v-card-title>\n            <v-card-subtitle>正在实施的项目</v-card-subtitle>\n          </v-card-item>\n          <v-card-text>\n            <div class=\"text-h3 font-weight-bold\">{{ inProgressProjectsCount }}</div>\n          </v-card-text>\n          <v-card-actions>\n            <v-btn variant=\"text\" color=\"info\" @click=\"$router.push('/projects?status=in-progress')\">\n              查看进行中项目\n              <v-icon end>mdi-arrow-right</v-icon>\n            </v-btn>\n          </v-card-actions>\n        </v-card>\n      </v-col>\n\n      <v-col cols=\"12\" md=\"3\" sm=\"6\">\n        <v-card class=\"dashboard-card\">\n          <v-card-item>\n            <v-card-title>\n              <div class=\"d-flex align-center\">\n                <v-icon size=\"large\" color=\"success\" class=\"me-2\">mdi-check-circle</v-icon>\n                <span>已完成</span>\n              </div>\n            </v-card-title>\n            <v-card-subtitle>已完成的项目</v-card-subtitle>\n          </v-card-item>\n          <v-card-text>\n            <div class=\"text-h3 font-weight-bold\">{{ completedProjectsCount }}</div>\n          </v-card-text>\n          <v-card-actions>\n            <v-btn variant=\"text\" color=\"success\" @click=\"$router.push('/projects?status=completed')\">\n              查看已完成项目\n              <v-icon end>mdi-arrow-right</v-icon>\n            </v-btn>\n          </v-card-actions>\n        </v-card>\n      </v-col>\n\n      <v-col cols=\"12\" md=\"3\" sm=\"6\">\n        <v-card class=\"dashboard-card\">\n          <v-card-item>\n            <v-card-title>\n              <div class=\"d-flex align-center\">\n                <v-icon size=\"large\" color=\"error\" class=\"me-2\">mdi-alert-circle</v-icon>\n                <span>风险预警</span>\n              </div>\n            </v-card-title>\n            <v-card-subtitle>需要关注的风险</v-card-subtitle>\n          </v-card-item>\n          <v-card-text>\n            <div class=\"text-h3 font-weight-bold\">{{ riskWarningsCount }}</div>\n          </v-card-text>\n          <v-card-actions>\n            <v-btn variant=\"text\" color=\"error\" @click=\"$router.push('/risks')\">\n              查看风险详情\n              <v-icon end>mdi-arrow-right</v-icon>\n            </v-btn>\n          </v-card-actions>\n        </v-card>\n      </v-col>\n    </v-row>\n\n    <!-- 项目进度图表 -->\n    <v-row class=\"mt-4\">\n      <v-col cols=\"12\" lg=\"8\">\n        <v-card>\n          <v-card-title class=\"d-flex align-center\">\n            <v-icon color=\"primary\" class=\"me-2\">mdi-chart-timeline-variant</v-icon>\n            项目进度概览\n          </v-card-title>\n          <v-card-text>\n            <div v-if=\"loading\" class=\"d-flex justify-center align-center\" style=\"height: 300px\">\n              <v-progress-circular indeterminate color=\"primary\"></v-progress-circular>\n            </div>\n            <div v-else-if=\"projects.length === 0\" class=\"text-center py-8\">\n              <v-icon size=\"large\" color=\"grey\">mdi-chart-timeline-variant</v-icon>\n              <p class=\"text-body-1 text-medium-emphasis mt-2\">暂无项目数据</p>\n            </div>\n            <div v-else class=\"chart-container\" style=\"height: 300px\">\n              <!-- 这里将使用Chart.js渲染项目进度图表 -->\n              <BarChart :chart-data=\"projectProgressChartData\" :options=\"chartOptions\" />\n            </div>\n          </v-card-text>\n        </v-card>\n      </v-col>\n\n      <v-col cols=\"12\" lg=\"4\">\n        <v-card class=\"mb-4\">\n          <v-card-title class=\"d-flex align-center\">\n            <v-icon color=\"primary\" class=\"me-2\">mdi-calendar-check</v-icon>\n            今日会议\n          </v-card-title>\n          <v-card-text>\n            <div v-if=\"loading\" class=\"d-flex justify-center align-center\" style=\"height: 100px\">\n              <v-progress-circular indeterminate color=\"primary\"></v-progress-circular>\n            </div>\n            <div v-else-if=\"todayMeetings.length === 0\" class=\"text-center py-4\">\n              <v-icon color=\"grey\">mdi-calendar-blank</v-icon>\n              <p class=\"text-body-2 text-medium-emphasis mt-2\">今日无会议安排</p>\n            </div>\n            <v-list v-else lines=\"two\">\n              <v-list-item v-for=\"meeting in todayMeetings\" :key=\"meeting.id\" :to=\"`/meetings/${meeting.id}`\">\n                <template v-slot:prepend>\n                  <v-avatar color=\"primary\" variant=\"tonal\">\n                    <v-icon>mdi-calendar-clock</v-icon>\n                  </v-avatar>\n                </template>\n                <v-list-item-title>{{ meeting.title }}</v-list-item-title>\n                <v-list-item-subtitle>\n                  {{ formatTime(meeting.startTime) }} - {{ formatTime(meeting.endTime) }}\n                </v-list-item-subtitle>\n              </v-list-item>\n            </v-list>\n          </v-card-text>\n          <v-card-actions>\n            <v-btn variant=\"text\" color=\"primary\" @click=\"$router.push('/meetings')\">\n              查看所有会议\n              <v-icon end>mdi-arrow-right</v-icon>\n            </v-btn>\n          </v-card-actions>\n        </v-card>\n\n        <v-card>\n          <v-card-title class=\"d-flex align-center\">\n            <v-icon color=\"error\" class=\"me-2\">mdi-alert</v-icon>\n            风险预警\n          </v-card-title>\n          <v-card-text>\n            <div v-if=\"loading\" class=\"d-flex justify-center align-center\" style=\"height: 100px\">\n              <v-progress-circular indeterminate color=\"primary\"></v-progress-circular>\n            </div>\n            <div v-else-if=\"riskWarnings.length === 0\" class=\"text-center py-4\">\n              <v-icon color=\"grey\">mdi-shield-check</v-icon>\n              <p class=\"text-body-2 text-medium-emphasis mt-2\">暂无风险预警</p>\n            </div>\n            <v-list v-else lines=\"two\">\n              <v-list-item v-for=\"risk in riskWarnings\" :key=\"risk.id\" :to=\"`/risks/${risk.id}`\">\n                <template v-slot:prepend>\n                  <v-avatar :color=\"getRiskColor(risk)\" variant=\"tonal\">\n                    <v-icon>mdi-alert-circle</v-icon>\n                  </v-avatar>\n                </template>\n                <v-list-item-title>{{ risk.title }}</v-list-item-title>\n                <v-list-item-subtitle>\n                  {{ risk.projectName }} | 影响: {{ risk.impactLevel }} | 可能性: {{ risk.probability }}\n                </v-list-item-subtitle>\n              </v-list-item>\n            </v-list>\n          </v-card-text>\n          <v-card-actions>\n            <v-btn variant=\"text\" color=\"error\" @click=\"$router.push('/risks')\">\n              查看所有风险\n              <v-icon end>mdi-arrow-right</v-icon>\n            </v-btn>\n          </v-card-actions>\n        </v-card>\n      </v-col>\n    </v-row>\n\n    <!-- 项目关联管理 -->\n    <v-row class=\"mt-4\">\n      <v-col cols=\"12\">\n        <v-card>\n          <v-card-title class=\"d-flex align-center\">\n            <v-icon color=\"primary\" class=\"me-2\">mdi-chart-box</v-icon>\n            项目关联管理\n            <v-spacer></v-spacer>\n            <v-btn\n              variant=\"outlined\"\n              size=\"small\"\n              @click=\"showRelationshipDashboard = !showRelationshipDashboard\"\n            >\n              {{ showRelationshipDashboard ? '收起' : '展开' }}\n              <v-icon end>{{ showRelationshipDashboard ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>\n            </v-btn>\n          </v-card-title>\n          <v-expand-transition>\n            <div v-show=\"showRelationshipDashboard\">\n              <v-card-text>\n                <ProjectRelationshipDashboard />\n              </v-card-text>\n            </div>\n          </v-expand-transition>\n        </v-card>\n      </v-col>\n    </v-row>\n\n    <!-- 最近任务和活动 -->\n    <v-row class=\"mt-4\">\n      <v-col cols=\"12\" md=\"6\">\n        <v-card>\n          <v-card-title class=\"d-flex align-center\">\n            <v-icon color=\"primary\" class=\"me-2\">mdi-clipboard-check</v-icon>\n            我的任务\n          </v-card-title>\n          <v-card-text>\n            <div v-if=\"loading\" class=\"d-flex justify-center align-center\" style=\"height: 200px\">\n              <v-progress-circular indeterminate color=\"primary\"></v-progress-circular>\n            </div>\n            <div v-else-if=\"myTasks.length === 0\" class=\"text-center py-8\">\n              <v-icon size=\"large\" color=\"grey\">mdi-clipboard-check</v-icon>\n              <p class=\"text-body-1 text-medium-emphasis mt-2\">暂无分配给您的任务</p>\n            </div>\n            <v-list v-else lines=\"two\">\n              <v-list-item v-for=\"task in myTasks\" :key=\"task.id\" :to=\"`/tasks/${task.id}`\">\n                <template v-slot:prepend>\n                  <v-checkbox v-model=\"task.completed\" @change=\"updateTaskStatus(task)\" hide-details></v-checkbox>\n                </template>\n                <v-list-item-title :class=\"{ 'text-decoration-line-through': task.status === 'completed' }\">\n                  {{ task.name }}\n                </v-list-item-title>\n                <v-list-item-subtitle>\n                  {{ task.projectName }} | {{ task.phaseName }} | 截止日期: {{ formatDate(task.dueDate) }}\n                </v-list-item-subtitle>\n                <template v-slot:append>\n                  <v-chip :color=\"getTaskStatusColor(task.status)\" size=\"small\">{{ getTaskStatusText(task.status) }}</v-chip>\n                </template>\n              </v-list-item>\n            </v-list>\n          </v-card-text>\n          <v-card-actions>\n            <v-btn variant=\"text\" color=\"primary\" @click=\"$router.push('/tasks')\">\n              查看所有任务\n              <v-icon end>mdi-arrow-right</v-icon>\n            </v-btn>\n          </v-card-actions>\n        </v-card>\n      </v-col>\n\n      <v-col cols=\"12\" md=\"6\">\n        <v-card>\n          <v-card-title class=\"d-flex align-center\">\n            <v-icon color=\"primary\" class=\"me-2\">mdi-history</v-icon>\n            最近活动\n          </v-card-title>\n          <v-card-text>\n            <div v-if=\"loading\" class=\"d-flex justify-center align-center\" style=\"height: 200px\">\n              <v-progress-circular indeterminate color=\"primary\"></v-progress-circular>\n            </div>\n            <div v-else-if=\"recentActivities.length === 0\" class=\"text-center py-8\">\n              <v-icon size=\"large\" color=\"grey\">mdi-history</v-icon>\n              <p class=\"text-body-1 text-medium-emphasis mt-2\">暂无最近活动</p>\n            </div>\n            <v-timeline v-else align=\"start\" side=\"end\" truncate-line=\"both\">\n              <v-timeline-item\n                v-for=\"activity in recentActivities\"\n                :key=\"activity.id\"\n                :dot-color=\"getActivityColor(activity.type)\"\n                :icon=\"getActivityIcon(activity.type)\"\n                size=\"small\"\n              >\n                <div class=\"d-flex justify-space-between align-center\">\n                  <div>\n                    <div class=\"text-body-1\">{{ activity.description }}</div>\n                    <div class=\"text-caption text-medium-emphasis\">{{ activity.projectName }}</div>\n                  </div>\n                  <div class=\"text-caption text-medium-emphasis\">{{ formatDateTime(activity.timestamp) }}</div>\n                </div>\n              </v-timeline-item>\n            </v-timeline>\n          </v-card-text>\n        </v-card>\n      </v-col>\n    </v-row>\n  </div>\n</template>\n\n<script>\nimport { mapGetters, mapActions } from 'vuex'\nimport BarChart from '@/components/charts/BarChart.vue'\nimport ProjectRelationshipDashboard from '@/components/ProjectRelationshipDashboard.vue'\n\nexport default {\n  name: 'DashboardView',\n  components: {\n    BarChart,\n    ProjectRelationshipDashboard\n  },\n  data() {\n    return {\n      // 项目关联管理显示状态\n      showRelationshipDashboard: false,\n      // 模拟数据，实际项目中将从API获取\n      todayMeetings: [],\n      riskWarnings: [],\n      myTasks: [],\n      recentActivities: [],\n      chartOptions: {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n          legend: {\n            position: 'bottom'\n          }\n        },\n        scales: {\n          y: {\n            beginAtZero: true,\n            max: 100,\n            ticks: {\n              callback: function(value) {\n                return value + '%'\n              }\n            }\n          }\n        }\n      }\n    }\n  },\n  computed: {\n    ...mapGetters({\n      user: 'auth/user',\n      loading: 'isLoading',\n      projects: 'projects/allProjects',\n      projectsCount: 'projects/projectsCount',\n      completedProjectsCount: 'projects/completedProjectsCount',\n      inProgressProjectsCount: 'projects/inProgressProjectsCount',\n      riskWarningsCount: 'risks/riskWarnings.length'\n    }),\n    projectProgressChartData() {\n      return {\n        labels: this.projects.slice(0, 10).map(p => p.name),\n        datasets: [\n          {\n            label: '项目进度',\n            data: this.projects.slice(0, 10).map(p => p.progress),\n            backgroundColor: 'rgba(25, 118, 210, 0.6)',\n            borderColor: 'rgba(25, 118, 210, 1)',\n            borderWidth: 1\n          }\n        ]\n      }\n    }\n  },\n  created() {\n    this.fetchDashboardData()\n  },\n  methods: {\n    ...mapActions({\n      fetchProjects: 'projects/fetchProjects',\n      fetchRisks: 'risks/fetchRisks',\n      fetchTasks: 'tasks/fetchTasks',\n      fetchMeetings: 'meetings/fetchMeetings'\n    }),\n    async fetchDashboardData() {\n      try {\n        await Promise.all([\n          this.fetchProjects(),\n          this.fetchRisks(),\n          this.fetchTasks(),\n          this.fetchMeetings()\n        ])\n\n        // 模拟获取今日会议\n        this.todayMeetings = [\n          {\n            id: 1,\n            title: '项目启动会议',\n            startTime: '09:00',\n            endTime: '10:30'\n          },\n          {\n            id: 2,\n            title: '风险评估讨论',\n            startTime: '14:00',\n            endTime: '15:30'\n          }\n        ]\n\n        // 模拟获取风险预警\n        this.riskWarnings = [\n          {\n            id: 1,\n            title: '接口数据格式不兼容',\n            projectName: '医院信息系统集成',\n            impactLevel: '高',\n            probability: '中'\n          },\n          {\n            id: 2,\n            title: '服务器性能不足',\n            projectName: '患者数据分析平台',\n            impactLevel: '高',\n            probability: '高'\n          }\n        ]\n\n        // 模拟获取我的任务\n        this.myTasks = [\n          {\n            id: 1,\n            name: '完成接口文档',\n            projectName: '医院信息系统集成',\n            phaseName: '接口文档数据提供后确认',\n            dueDate: '2025-05-28',\n            status: 'in-progress',\n            completed: false\n          },\n          {\n            id: 2,\n            name: '环境搭建测试',\n            projectName: '患者数据分析平台',\n            phaseName: '工程师进场搭建实施环境',\n            dueDate: '2025-05-26',\n            status: 'completed',\n            completed: true\n          },\n          {\n            id: 3,\n            name: '指标逻辑验证',\n            projectName: '医院信息系统集成',\n            phaseName: '指标逻辑计算',\n            dueDate: '2025-05-30',\n            status: 'pending',\n            completed: false\n          }\n        ]\n\n        // 模拟获取最近活动\n        this.recentActivities = [\n          {\n            id: 1,\n            type: 'task',\n            description: '完成了任务：环境搭建测试',\n            projectName: '患者数据分析平台',\n            timestamp: '2025-05-24T14:30:00'\n          },\n          {\n            id: 2,\n            type: 'meeting',\n            description: '参加了会议：项目进度评审',\n            projectName: '医院信息系统集成',\n            timestamp: '2025-05-24T10:15:00'\n          },\n          {\n            id: 3,\n            type: 'risk',\n            description: '添加了风险：接口数据格式不兼容',\n            projectName: '医院信息系统集成',\n            timestamp: '2025-05-23T16:45:00'\n          },\n          {\n            id: 4,\n            type: 'phase',\n            description: '完成了阶段：进场指标调研',\n            projectName: '患者数据分析平台',\n            timestamp: '2025-05-22T11:20:00'\n          }\n        ]\n      } catch (error) {\n        console.error('获取仪表盘数据失败:', error)\n      }\n    },\n    formatDate(dateString) {\n      if (!dateString) return ''\n      const date = new Date(dateString)\n      return date.toLocaleDateString('zh-CN')\n    },\n    formatTime(timeString) {\n      return timeString\n    },\n    formatDateTime(dateTimeString) {\n      if (!dateTimeString) return ''\n      const date = new Date(dateTimeString)\n      return date.toLocaleString('zh-CN')\n    },\n    getTaskStatusColor(status) {\n      const colors = {\n        'completed': 'success',\n        'in-progress': 'info',\n        'pending': 'warning',\n        'delayed': 'error'\n      }\n      return colors[status] || 'grey'\n    },\n    getTaskStatusText(status) {\n      const texts = {\n        'completed': '已完成',\n        'in-progress': '进行中',\n        'pending': '待处理',\n        'delayed': '已延期'\n      }\n      return texts[status] || '未知'\n    },\n    getRiskColor(risk) {\n      if (risk.impactLevel === '高' || risk.probability === '高') {\n        return 'error'\n      } else if (risk.impactLevel === '中' || risk.probability === '中') {\n        return 'warning'\n      }\n      return 'success'\n    },\n    getActivityColor(type) {\n      const colors = {\n        'task': 'info',\n        'meeting': 'primary',\n        'risk': 'error',\n        'phase': 'success',\n        'project': 'secondary'\n      }\n      return colors[type] || 'grey'\n    },\n    getActivityIcon(type) {\n      const icons = {\n        'task': 'mdi-clipboard-check',\n        'meeting': 'mdi-calendar-clock',\n        'risk': 'mdi-alert-circle',\n        'phase': 'mdi-flag-checkered',\n        'project': 'mdi-folder'\n      }\n      return icons[type] || 'mdi-circle'\n    },\n    async updateTaskStatus(task) {\n      try {\n        // 实际项目中，这里会调用API更新任务状态\n        const status = task.completed ? 'completed' : 'in-progress'\n        await this.$store.dispatch('tasks/updateTaskStatus', {\n          taskId: task.id,\n          status\n        })\n      } catch (error) {\n        console.error('更新任务状态失败:', error)\n        // 恢复原状态\n        task.completed = !task.completed\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.dashboard {\n  padding: 24px;\n}\n\n.dashboard-card {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.dashboard-card .v-card-text {\n  flex-grow: 1;\n}\n\n.chart-container {\n  position: relative;\n  width: 100%;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAW;;EA0BXA,KAAK,EAAC;AAA2C;;EAG/CA,KAAK,EAAC;AAAsC;;EAsBxCA,KAAK,EAAC;AAAqB;;EAQ7BA,KAAK,EAAC;AAA0B;;EAe9BA,KAAK,EAAC;AAAqB;;EAQ7BA,KAAK,EAAC;AAA0B;;EAe9BA,KAAK,EAAC;AAAqB;;EAQ7BA,KAAK,EAAC;AAA0B;;EAe9BA,KAAK,EAAC;AAAqB;;EAQ7BA,KAAK,EAAC;AAA0B;;EAjIjDC,GAAA;EAsJgCD,KAAK,EAAC,oCAAoC;EAACE,KAAqB,EAArB;IAAA;EAAA;;;EAtJ3ED,GAAA;EAyJmDD,KAAK,EAAC;;;EAzJzDC,GAAA;EA6JwBD,KAAK,EAAC,iBAAiB;EAACE,KAAqB,EAArB;IAAA;EAAA;;;EA7JhDD,GAAA;EA4KgCD,KAAK,EAAC,oCAAoC;EAACE,KAAqB,EAArB;IAAA;EAAA;;;EA5K3ED,GAAA;EA+KwDD,KAAK,EAAC;;;EA/K9DC,GAAA;EA+MgCD,KAAK,EAAC,oCAAoC;EAACE,KAAqB,EAArB;IAAA;EAAA;;;EA/M3ED,GAAA;EAkNuDD,KAAK,EAAC;;;EAlN7DC,GAAA;EAmRgCD,KAAK,EAAC,oCAAoC;EAACE,KAAqB,EAArB;IAAA;EAAA;;;EAnR3ED,GAAA;EAsRkDD,KAAK,EAAC;;;EAtRxDC,GAAA;EA2TgCD,KAAK,EAAC,oCAAoC;EAACE,KAAqB,EAArB;IAAA;EAAA;;;EA3T3ED,GAAA;EA8T2DD,KAAK,EAAC;;;EAY5CA,KAAK,EAAC;AAA2C;;EAE7CA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAmC;;EAE3CA,KAAK,EAAC;AAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA9U9DG,mBAAA,CAsVM,OAtVNC,UAsVM,GArVJC,mBAAA,UAAa,EACbC,YAAA,CAMQC,gBAAA;IANDP,KAAK,EAAC;EAAM;IAHvBQ,OAAA,EAAAC,QAAA,CAIM,MAIQ,CAJRH,YAAA,CAIQI,gBAAA;MAJDC,IAAI,EAAC,IAAI;MAACX,KAAK,EAAC;;MAJ7BQ,OAAA,EAAAC,QAAA,CAKQ,MAA6E,CAA7EH,YAAA,CAA6EM,iBAAA;QAArEC,IAAI,EAAC,IAAI;QAACC,KAAK,EAAC,SAAS;QAACd,KAAK,EAAC;;QALhDQ,OAAA,EAAAC,QAAA,CAKuD,MAAqBM,MAAA,QAAAA,MAAA,OAL5EC,gBAAA,CAKuD,uBAAqB,E;QAL5EC,CAAA;QAAAC,EAAA;sCAMQC,mBAAA,CAAwE;QAApEnB,KAAK,EAAC;MAA4C,GAAC,cAAY,sB,4BACnEmB,mBAAA,CAAqG;QAAlGnB,KAAK,EAAC;MAA8B,GAAC,2DAAyD,qB;MAPzGiB,CAAA;MAAAC,EAAA;;IAAAD,CAAA;MAWIZ,mBAAA,UAAa,EACbC,YAAA,CAUQC,gBAAA;IAVDP,KAAK,EAAC;EAAM;IAZvBQ,OAAA,EAAAC,QAAA,CAaM,MAQQ,CARRH,YAAA,CAQQI,gBAAA;MARDC,IAAI,EAAC;IAAI;MAbtBH,OAAA,EAAAC,QAAA,CAcQ,MAMU,CANVH,YAAA,CAMUc,kBAAA;QALRC,IAAI,EAAC,SAAS;QACdC,OAAO,EAAC,OAAO;QACf,cAAY,EAAC;;QAjBvBd,OAAA,EAAAC,QAAA,CAkBS,MAEDM,MAAA,SAAAA,MAAA,QApBRC,gBAAA,CAkBS,+BAED,E;QApBRC,CAAA;QAAAC,EAAA;;MAAAD,CAAA;;IAAAA,CAAA;MAwBIZ,mBAAA,UAAa,EACbC,YAAA,CAmBQC,gBAAA;IAnBDP,KAAK,EAAC;EAAM;IAzBvBQ,OAAA,EAAAC,QAAA,CA0BM,MAiBQ,CAjBRH,YAAA,CAiBQI,gBAAA;MAjBDC,IAAI,EAAC;IAAI;MA1BtBH,OAAA,EAAAC,QAAA,CA2BQ,MAeM,CAfNU,mBAAA,CAeM,OAfNI,UAeM,GAdJJ,mBAAA,CAKM,c,4BAJJA,mBAAA,CAAiD;QAA7CnB,KAAK,EAAC;MAA0B,GAAC,SAAO,sBAC5CmB,mBAAA,CAEI,KAFJK,UAEI,EAF4C,QACzC,GAAAC,gBAAA,CAAGC,IAAA,CAAAC,IAAI,GAAGD,IAAA,CAAAC,IAAI,CAACC,QAAQ,IAAIF,IAAA,CAAAC,IAAI,CAACE,QAAQ,IAAIH,IAAA,CAAAC,IAAI,CAACG,IAAI,aAAY,cACxE,gB,GAEFxB,YAAA,CAOQyB,gBAAA;QANNjB,KAAK,EAAC,SAAS;QACfD,IAAI,EAAC,OAAO;QACZ,cAAY,EAAC,UAAU;QACtBmB,OAAK,EAAAjB,MAAA,QAAAA,MAAA,MAAAkB,MAAA,IAAEP,IAAA,CAAAQ,OAAO,CAACC,IAAI;;QAtChC3B,OAAA,EAAAC,QAAA,CAuCW,MAEDM,MAAA,SAAAA,MAAA,QAzCVC,gBAAA,CAuCW,QAED,E;QAzCVC,CAAA;QAAAC,EAAA;;MAAAD,CAAA;;IAAAA,CAAA;MA8CIZ,mBAAA,YAAe,EACfC,YAAA,CA4FQC,gBAAA;IA3IZC,OAAA,EAAAC,QAAA,CAgDM,MAqBQ,CArBRH,YAAA,CAqBQI,gBAAA;MArBDC,IAAI,EAAC,IAAI;MAACyB,EAAE,EAAC,GAAG;MAACC,EAAE,EAAC;;MAhDjC7B,OAAA,EAAAC,QAAA,CAiDQ,MAmBS,CAnBTH,YAAA,CAmBSgC,iBAAA;QAnBDtC,KAAK,EAAC;MAAgB;QAjDtCQ,OAAA,EAAAC,QAAA,CAkDU,MAQc,CARdH,YAAA,CAQciC,sBAAA;UA1DxB/B,OAAA,EAAAC,QAAA,CAmDY,MAKe,CALfH,YAAA,CAKekC,uBAAA;YAxD3BhC,OAAA,EAAAC,QAAA,CAoDc,MAGM,CAHNU,mBAAA,CAGM,OAHNsB,UAGM,GAFJnC,YAAA,CAA8EM,iBAAA;cAAtEC,IAAI,EAAC,OAAO;cAACC,KAAK,EAAC,SAAS;cAACd,KAAK,EAAC;;cArD3DQ,OAAA,EAAAC,QAAA,CAqDkE,MAAmBM,MAAA,SAAAA,MAAA,QArDrFC,gBAAA,CAqDkE,qBAAmB,E;cArDrFC,CAAA;cAAAC,EAAA;4CAsDgBC,mBAAA,CAAiB,cAAX,MAAI,qB;YAtD1BF,CAAA;cAyDYX,YAAA,CAAuCoC,0BAAA;YAzDnDlC,OAAA,EAAAC,QAAA,CAyD6B,MAAIM,MAAA,SAAAA,MAAA,QAzDjCC,gBAAA,CAyD6B,MAAI,E;YAzDjCC,CAAA;YAAAC,EAAA;;UAAAD,CAAA;YA2DUX,YAAA,CAEcqC,sBAAA;UA7DxBnC,OAAA,EAAAC,QAAA,CA4DY,MAA+D,CAA/DU,mBAAA,CAA+D,OAA/DyB,UAA+D,EAAAnB,gBAAA,CAAtBC,IAAA,CAAAmB,aAAa,iB;UA5DlE5B,CAAA;YA8DUX,YAAA,CAKiBwC,yBAAA;UAnE3BtC,OAAA,EAAAC,QAAA,CA+DY,MAGQ,CAHRH,YAAA,CAGQyB,gBAAA;YAHDT,OAAO,EAAC,MAAM;YAACR,KAAK,EAAC,SAAS;YAAEkB,OAAK,EAAAjB,MAAA,QAAAA,MAAA,MAAAkB,MAAA,IAAEP,IAAA,CAAAQ,OAAO,CAACC,IAAI;;YA/DtE3B,OAAA,EAAAC,QAAA,CA+DqF,MAEvE,C,4BAjEdO,gBAAA,CA+DqF,UAEvE,IAAAV,YAAA,CAAoCM,iBAAA;cAA5BmC,GAAG,EAAH;YAAG;cAjEzBvC,OAAA,EAAAC,QAAA,CAiE0B,MAAeM,MAAA,SAAAA,MAAA,QAjEzCC,gBAAA,CAiE0B,iBAAe,E;cAjEzCC,CAAA;cAAAC,EAAA;;YAAAD,CAAA;YAAAC,EAAA;;UAAAD,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;QAuEMX,YAAA,CAqBQI,gBAAA;MArBDC,IAAI,EAAC,IAAI;MAACyB,EAAE,EAAC,GAAG;MAACC,EAAE,EAAC;;MAvEjC7B,OAAA,EAAAC,QAAA,CAwEQ,MAmBS,CAnBTH,YAAA,CAmBSgC,iBAAA;QAnBDtC,KAAK,EAAC;MAAgB;QAxEtCQ,OAAA,EAAAC,QAAA,CAyEU,MAQc,CARdH,YAAA,CAQciC,sBAAA;UAjFxB/B,OAAA,EAAAC,QAAA,CA0EY,MAKe,CALfH,YAAA,CAKekC,uBAAA;YA/E3BhC,OAAA,EAAAC,QAAA,CA2Ec,MAGM,CAHNU,mBAAA,CAGM,OAHN6B,UAGM,GAFJ1C,YAAA,CAA0EM,iBAAA;cAAlEC,IAAI,EAAC,OAAO;cAACC,KAAK,EAAC,MAAM;cAACd,KAAK,EAAC;;cA5ExDQ,OAAA,EAAAC,QAAA,CA4E+D,MAAkBM,MAAA,SAAAA,MAAA,QA5EjFC,gBAAA,CA4E+D,oBAAkB,E;cA5EjFC,CAAA;cAAAC,EAAA;4CA6EgBC,mBAAA,CAAgB,cAAV,KAAG,qB;YA7EzBF,CAAA;cAgFYX,YAAA,CAA0CoC,0BAAA;YAhFtDlC,OAAA,EAAAC,QAAA,CAgF6B,MAAOM,MAAA,SAAAA,MAAA,QAhFpCC,gBAAA,CAgF6B,SAAO,E;YAhFpCC,CAAA;YAAAC,EAAA;;UAAAD,CAAA;YAkFUX,YAAA,CAEcqC,sBAAA;UApFxBnC,OAAA,EAAAC,QAAA,CAmFY,MAAyE,CAAzEU,mBAAA,CAAyE,OAAzE8B,UAAyE,EAAAxB,gBAAA,CAAhCC,IAAA,CAAAwB,uBAAuB,iB;UAnF5EjC,CAAA;YAqFUX,YAAA,CAKiBwC,yBAAA;UA1F3BtC,OAAA,EAAAC,QAAA,CAsFY,MAGQ,CAHRH,YAAA,CAGQyB,gBAAA;YAHDT,OAAO,EAAC,MAAM;YAACR,KAAK,EAAC,MAAM;YAAEkB,OAAK,EAAAjB,MAAA,QAAAA,MAAA,MAAAkB,MAAA,IAAEP,IAAA,CAAAQ,OAAO,CAACC,IAAI;;YAtFnE3B,OAAA,EAAAC,QAAA,CAsFqG,MAEvF,C,4BAxFdO,gBAAA,CAsFqG,WAEvF,IAAAV,YAAA,CAAoCM,iBAAA;cAA5BmC,GAAG,EAAH;YAAG;cAxFzBvC,OAAA,EAAAC,QAAA,CAwF0B,MAAeM,MAAA,SAAAA,MAAA,QAxFzCC,gBAAA,CAwF0B,iBAAe,E;cAxFzCC,CAAA;cAAAC,EAAA;;YAAAD,CAAA;YAAAC,EAAA;;UAAAD,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;QA8FMX,YAAA,CAqBQI,gBAAA;MArBDC,IAAI,EAAC,IAAI;MAACyB,EAAE,EAAC,GAAG;MAACC,EAAE,EAAC;;MA9FjC7B,OAAA,EAAAC,QAAA,CA+FQ,MAmBS,CAnBTH,YAAA,CAmBSgC,iBAAA;QAnBDtC,KAAK,EAAC;MAAgB;QA/FtCQ,OAAA,EAAAC,QAAA,CAgGU,MAQc,CARdH,YAAA,CAQciC,sBAAA;UAxGxB/B,OAAA,EAAAC,QAAA,CAiGY,MAKe,CALfH,YAAA,CAKekC,uBAAA;YAtG3BhC,OAAA,EAAAC,QAAA,CAkGc,MAGM,CAHNU,mBAAA,CAGM,OAHNgC,UAGM,GAFJ7C,YAAA,CAA2EM,iBAAA;cAAnEC,IAAI,EAAC,OAAO;cAACC,KAAK,EAAC,SAAS;cAACd,KAAK,EAAC;;cAnG3DQ,OAAA,EAAAC,QAAA,CAmGkE,MAAgBM,MAAA,SAAAA,MAAA,QAnGlFC,gBAAA,CAmGkE,kBAAgB,E;cAnGlFC,CAAA;cAAAC,EAAA;4CAoGgBC,mBAAA,CAAgB,cAAV,KAAG,qB;YApGzBF,CAAA;cAuGYX,YAAA,CAAyCoC,0BAAA;YAvGrDlC,OAAA,EAAAC,QAAA,CAuG6B,MAAMM,MAAA,SAAAA,MAAA,QAvGnCC,gBAAA,CAuG6B,QAAM,E;YAvGnCC,CAAA;YAAAC,EAAA;;UAAAD,CAAA;YAyGUX,YAAA,CAEcqC,sBAAA;UA3GxBnC,OAAA,EAAAC,QAAA,CA0GY,MAAwE,CAAxEU,mBAAA,CAAwE,OAAxEiC,UAAwE,EAAA3B,gBAAA,CAA/BC,IAAA,CAAA2B,sBAAsB,iB;UA1G3EpC,CAAA;YA4GUX,YAAA,CAKiBwC,yBAAA;UAjH3BtC,OAAA,EAAAC,QAAA,CA6GY,MAGQ,CAHRH,YAAA,CAGQyB,gBAAA;YAHDT,OAAO,EAAC,MAAM;YAACR,KAAK,EAAC,SAAS;YAAEkB,OAAK,EAAAjB,MAAA,QAAAA,MAAA,MAAAkB,MAAA,IAAEP,IAAA,CAAAQ,OAAO,CAACC,IAAI;;YA7GtE3B,OAAA,EAAAC,QAAA,CA6GsG,MAExF,C,4BA/GdO,gBAAA,CA6GsG,WAExF,IAAAV,YAAA,CAAoCM,iBAAA;cAA5BmC,GAAG,EAAH;YAAG;cA/GzBvC,OAAA,EAAAC,QAAA,CA+G0B,MAAeM,MAAA,SAAAA,MAAA,QA/GzCC,gBAAA,CA+G0B,iBAAe,E;cA/GzCC,CAAA;cAAAC,EAAA;;YAAAD,CAAA;YAAAC,EAAA;;UAAAD,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;QAqHMX,YAAA,CAqBQI,gBAAA;MArBDC,IAAI,EAAC,IAAI;MAACyB,EAAE,EAAC,GAAG;MAACC,EAAE,EAAC;;MArHjC7B,OAAA,EAAAC,QAAA,CAsHQ,MAmBS,CAnBTH,YAAA,CAmBSgC,iBAAA;QAnBDtC,KAAK,EAAC;MAAgB;QAtHtCQ,OAAA,EAAAC,QAAA,CAuHU,MAQc,CARdH,YAAA,CAQciC,sBAAA;UA/HxB/B,OAAA,EAAAC,QAAA,CAwHY,MAKe,CALfH,YAAA,CAKekC,uBAAA;YA7H3BhC,OAAA,EAAAC,QAAA,CAyHc,MAGM,CAHNU,mBAAA,CAGM,OAHNmC,WAGM,GAFJhD,YAAA,CAAyEM,iBAAA;cAAjEC,IAAI,EAAC,OAAO;cAACC,KAAK,EAAC,OAAO;cAACd,KAAK,EAAC;;cA1HzDQ,OAAA,EAAAC,QAAA,CA0HgE,MAAgBM,MAAA,SAAAA,MAAA,QA1HhFC,gBAAA,CA0HgE,kBAAgB,E;cA1HhFC,CAAA;cAAAC,EAAA;4CA2HgBC,mBAAA,CAAiB,cAAX,MAAI,qB;YA3H1BF,CAAA;cA8HYX,YAAA,CAA0CoC,0BAAA;YA9HtDlC,OAAA,EAAAC,QAAA,CA8H6B,MAAOM,MAAA,SAAAA,MAAA,QA9HpCC,gBAAA,CA8H6B,SAAO,E;YA9HpCC,CAAA;YAAAC,EAAA;;UAAAD,CAAA;YAgIUX,YAAA,CAEcqC,sBAAA;UAlIxBnC,OAAA,EAAAC,QAAA,CAiIY,MAAmE,CAAnEU,mBAAA,CAAmE,OAAnEoC,WAAmE,EAAA9B,gBAAA,CAA1BC,IAAA,CAAA8B,iBAAiB,iB;UAjItEvC,CAAA;YAmIUX,YAAA,CAKiBwC,yBAAA;UAxI3BtC,OAAA,EAAAC,QAAA,CAoIY,MAGQ,CAHRH,YAAA,CAGQyB,gBAAA;YAHDT,OAAO,EAAC,MAAM;YAACR,KAAK,EAAC,OAAO;YAAEkB,OAAK,EAAAjB,MAAA,QAAAA,MAAA,MAAAkB,MAAA,IAAEP,IAAA,CAAAQ,OAAO,CAACC,IAAI;;YApIpE3B,OAAA,EAAAC,QAAA,CAoIgF,MAElE,C,4BAtIdO,gBAAA,CAoIgF,UAElE,IAAAV,YAAA,CAAoCM,iBAAA;cAA5BmC,GAAG,EAAH;YAAG;cAtIzBvC,OAAA,EAAAC,QAAA,CAsI0B,MAAeM,MAAA,SAAAA,MAAA,QAtIzCC,gBAAA,CAsI0B,iBAAe,E;cAtIzCC,CAAA;cAAAC,EAAA;;YAAAD,CAAA;YAAAC,EAAA;;UAAAD,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;MA6IIZ,mBAAA,YAAe,EACfC,YAAA,CA8FQC,gBAAA;IA9FDP,KAAK,EAAC;EAAM;IA9IvBQ,OAAA,EAAAC,QAAA,CA+IM,MAoBQ,CApBRH,YAAA,CAoBQI,gBAAA;MApBDC,IAAI,EAAC,IAAI;MAAC8C,EAAE,EAAC;;MA/I1BjD,OAAA,EAAAC,QAAA,CAgJQ,MAkBS,CAlBTH,YAAA,CAkBSgC,iBAAA;QAlKjB9B,OAAA,EAAAC,QAAA,CAiJU,MAGe,CAHfH,YAAA,CAGekC,uBAAA;UAHDxC,KAAK,EAAC;QAAqB;UAjJnDQ,OAAA,EAAAC,QAAA,CAkJY,MAAwE,CAAxEH,YAAA,CAAwEM,iBAAA;YAAhEE,KAAK,EAAC,SAAS;YAACd,KAAK,EAAC;;YAlJ1CQ,OAAA,EAAAC,QAAA,CAkJiD,MAA0BM,MAAA,SAAAA,MAAA,QAlJ3EC,gBAAA,CAkJiD,4BAA0B,E;YAlJ3EC,CAAA;YAAAC,EAAA;0CAAAF,gBAAA,CAkJoF,UAE1E,G;UApJVC,CAAA;UAAAC,EAAA;YAqJUZ,YAAA,CAYcqC,sBAAA;UAjKxBnC,OAAA,EAAAC,QAAA,CA2KY,MAMH,CA3BciB,IAAA,CAAAgC,OAAO,I,cAAlBvD,mBAAA,CAEM,OAFNwD,WAEM,GADJrD,YAAA,CAAyEsD,8BAAA;YAApDC,aAAa,EAAb,EAAa;YAAC/C,KAAK,EAAC;kBAE3BY,IAAA,CAAAoC,QAAQ,CAACC,MAAM,U,cAA/B5D,mBAAA,CAGM,OAHN6D,WAGM,GAFJ1D,YAAA,CAAqEM,iBAAA;YAA7DC,IAAI,EAAC,OAAO;YAACC,KAAK,EAAC;;YA1JzCN,OAAA,EAAAC,QAAA,CA0JgD,MAA0BM,MAAA,SAAAA,MAAA,QA1J1EC,gBAAA,CA0JgD,4BAA0B,E;YA1J1EC,CAAA;YAAAC,EAAA;0CA2JcC,mBAAA,CAA2D;YAAxDnB,KAAK,EAAC;UAAuC,GAAC,QAAM,qB,oBAEzDG,mBAAA,CAGM,OAHN8D,WAGM,GAFJ5D,mBAAA,2BAA8B,EAC9BC,YAAA,CAA2E4D,mBAAA;YAAhE,YAAU,EAAEC,QAAA,CAAAC,wBAAwB;YAAGC,OAAO,EAAEC,KAAA,CAAAC;;UA/JzEtD,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;QAqKMX,YAAA,CAsEQI,gBAAA;MAtEDC,IAAI,EAAC,IAAI;MAAC8C,EAAE,EAAC;;MArK1BjD,OAAA,EAAAC,QAAA,CAsKQ,MAiCS,CAjCTH,YAAA,CAiCSgC,iBAAA;QAjCDtC,KAAK,EAAC;MAAM;QAtK5BQ,OAAA,EAAAC,QAAA,CAuKU,MAGe,CAHfH,YAAA,CAGekC,uBAAA;UAHDxC,KAAK,EAAC;QAAqB;UAvKnDQ,OAAA,EAAAC,QAAA,CAwKY,MAAgE,CAAhEH,YAAA,CAAgEM,iBAAA;YAAxDE,KAAK,EAAC,SAAS;YAACd,KAAK,EAAC;;YAxK1CQ,OAAA,EAAAC,QAAA,CAwKiD,MAAkBM,MAAA,SAAAA,MAAA,QAxKnEC,gBAAA,CAwKiD,oBAAkB,E;YAxKnEC,CAAA;YAAAC,EAAA;0CAAAF,gBAAA,CAwK4E,QAElE,G;UA1KVC,CAAA;UAAAC,EAAA;YA2KUZ,YAAA,CAqBcqC,sBAAA;UAhMxBnC,OAAA,EAAAC,QAAA,CA4MU,MAOD,CAvCciB,IAAA,CAAAgC,OAAO,I,cAAlBvD,mBAAA,CAEM,OAFNqE,WAEM,GADJlE,YAAA,CAAyEsD,8BAAA;YAApDC,aAAa,EAAb,EAAa;YAAC/C,KAAK,EAAC;kBAE3BwD,KAAA,CAAAG,aAAa,CAACV,MAAM,U,cAApC5D,mBAAA,CAGM,OAHNuE,WAGM,GAFJpE,YAAA,CAAgDM,iBAAA;YAAxCE,KAAK,EAAC;UAAM;YAhLlCN,OAAA,EAAAC,QAAA,CAgLmC,MAAkBM,MAAA,SAAAA,MAAA,QAhLrDC,gBAAA,CAgLmC,oBAAkB,E;YAhLrDC,CAAA;YAAAC,EAAA;0CAiLcC,mBAAA,CAA4D;YAAzDnB,KAAK,EAAC;UAAuC,GAAC,SAAO,qB,oBAE1D2E,YAAA,CAYSC,iBAAA;YA/LrB3E,GAAA;YAmL2B4E,KAAK,EAAC;;YAnLjCrE,OAAA,EAAAC,QAAA,CAoL2B,MAAgC,E,kBAA7CN,mBAAA,CAUc2E,SAAA,QA9L5BC,WAAA,CAoL6CT,KAAA,CAAAG,aAAa,EAAxBO,OAAO;mCAA3BL,YAAA,CAUcM,sBAAA;gBAViChF,GAAG,EAAE+E,OAAO,CAACE,EAAE;gBAAGC,EAAE,eAAeH,OAAO,CAACE,EAAE;;gBACzEE,OAAO,EAAA3E,QAAA,CACtB,MAEW,CAFXH,YAAA,CAEW+E,mBAAA;kBAFDvE,KAAK,EAAC,SAAS;kBAACQ,OAAO,EAAC;;kBAtLpDd,OAAA,EAAAC,QAAA,CAuLoB,MAAmC,CAAnCH,YAAA,CAAmCM,iBAAA;oBAvLvDJ,OAAA,EAAAC,QAAA,CAuL4B,MAAkB,KAAAM,MAAA,SAAAA,MAAA,QAvL9CC,gBAAA,CAuL4B,oBAAkB,E;oBAvL9CC,CAAA;oBAAAC,EAAA;;kBAAAD,CAAA;;gBAAAT,OAAA,EAAAC,QAAA,CA0LgB,MAA0D,CAA1DH,YAAA,CAA0DgF,4BAAA;kBA1L1E9E,OAAA,EAAAC,QAAA,CA0LmC,MAAmB,CA1LtDO,gBAAA,CAAAS,gBAAA,CA0LsCuD,OAAO,CAACO,KAAK,iB;kBA1LnDtE,CAAA;8CA2LgBX,YAAA,CAEuBkF,+BAAA;kBA7LvChF,OAAA,EAAAC,QAAA,CA4LkB,MAAmC,CA5LrDO,gBAAA,CAAAS,gBAAA,CA4LqB0C,QAAA,CAAAsB,UAAU,CAACT,OAAO,CAACU,SAAS,KAAI,KAAG,GAAAjE,gBAAA,CAAG0C,QAAA,CAAAsB,UAAU,CAACT,OAAO,CAACW,OAAO,kB;kBA5LrF1E,CAAA;;gBAAAA,CAAA;;;YAAAA,CAAA;;UAAAA,CAAA;YAiMUX,YAAA,CAKiBwC,yBAAA;UAtM3BtC,OAAA,EAAAC,QAAA,CAkMY,MAGQ,CAHRH,YAAA,CAGQyB,gBAAA;YAHDT,OAAO,EAAC,MAAM;YAACR,KAAK,EAAC,SAAS;YAAEkB,OAAK,EAAAjB,MAAA,QAAAA,MAAA,MAAAkB,MAAA,IAAEP,IAAA,CAAAQ,OAAO,CAACC,IAAI;;YAlMtE3B,OAAA,EAAAC,QAAA,CAkMqF,MAEvE,C,4BApMdO,gBAAA,CAkMqF,UAEvE,IAAAV,YAAA,CAAoCM,iBAAA;cAA5BmC,GAAG,EAAH;YAAG;cApMzBvC,OAAA,EAAAC,QAAA,CAoM0B,MAAeM,MAAA,SAAAA,MAAA,QApMzCC,gBAAA,CAoM0B,iBAAe,E;cApMzCC,CAAA;cAAAC,EAAA;;YAAAD,CAAA;YAAAC,EAAA;;UAAAD,CAAA;;QAAAA,CAAA;UAyMQX,YAAA,CAiCSgC,iBAAA;QA1OjB9B,OAAA,EAAAC,QAAA,CA0MU,MAGe,CAHfH,YAAA,CAGekC,uBAAA;UAHDxC,KAAK,EAAC;QAAqB;UA1MnDQ,OAAA,EAAAC,QAAA,CA2MY,MAAqD,CAArDH,YAAA,CAAqDM,iBAAA;YAA7CE,KAAK,EAAC,OAAO;YAACd,KAAK,EAAC;;YA3MxCQ,OAAA,EAAAC,QAAA,CA2M+C,MAASM,MAAA,SAAAA,MAAA,QA3MxDC,gBAAA,CA2M+C,WAAS,E;YA3MxDC,CAAA;YAAAC,EAAA;0CAAAF,gBAAA,CA2MiE,QAEvD,G;UA7MVC,CAAA;UAAAC,EAAA;YA8MUZ,YAAA,CAqBcqC,sBAAA;UAnOxBnC,OAAA,EAAAC,QAAA,CAiQkB,MAOA,CAzDKiB,IAAA,CAAAgC,OAAO,I,cAAlBvD,mBAAA,CAEM,OAFNyF,WAEM,GADJtF,YAAA,CAAyEsD,8BAAA;YAApDC,aAAa,EAAb,EAAa;YAAC/C,KAAK,EAAC;kBAE3BwD,KAAA,CAAAuB,YAAY,CAAC9B,MAAM,U,cAAnC5D,mBAAA,CAGM,OAHN2F,WAGM,GAFJxF,YAAA,CAA8CM,iBAAA;YAAtCE,KAAK,EAAC;UAAM;YAnNlCN,OAAA,EAAAC,QAAA,CAmNmC,MAAgBM,MAAA,SAAAA,MAAA,QAnNnDC,gBAAA,CAmNmC,kBAAgB,E;YAnNnDC,CAAA;YAAAC,EAAA;0CAoNcC,mBAAA,CAA2D;YAAxDnB,KAAK,EAAC;UAAuC,GAAC,QAAM,qB,oBAEzD2E,YAAA,CAYSC,iBAAA;YAlOrB3E,GAAA;YAsN2B4E,KAAK,EAAC;;YAtNjCrE,OAAA,EAAAC,QAAA,CAuN2B,MAA4B,E,kBAAzCN,mBAAA,CAUc2E,SAAA,QAjO5BC,WAAA,CAuN0CT,KAAA,CAAAuB,YAAY,EAApBE,IAAI;mCAAxBpB,YAAA,CAUcM,sBAAA;gBAV6BhF,GAAG,EAAE8F,IAAI,CAACb,EAAE;gBAAGC,EAAE,YAAYY,IAAI,CAACb,EAAE;;gBAC5DE,OAAO,EAAA3E,QAAA,CACtB,MAEW,CAFXH,YAAA,CAEW+E,mBAAA;kBAFAvE,KAAK,EAAEqD,QAAA,CAAA6B,YAAY,CAACD,IAAI;kBAAGzE,OAAO,EAAC;;kBAzNhEd,OAAA,EAAAC,QAAA,CA0NoB,MAAiC,CAAjCH,YAAA,CAAiCM,iBAAA;oBA1NrDJ,OAAA,EAAAC,QAAA,CA0N4B,MAAgB,KAAAM,MAAA,SAAAA,MAAA,QA1N5CC,gBAAA,CA0N4B,kBAAgB,E;oBA1N5CC,CAAA;oBAAAC,EAAA;;kBAAAD,CAAA;;gBAAAT,OAAA,EAAAC,QAAA,CA6NgB,MAAuD,CAAvDH,YAAA,CAAuDgF,4BAAA;kBA7NvE9E,OAAA,EAAAC,QAAA,CA6NmC,MAAgB,CA7NnDO,gBAAA,CAAAS,gBAAA,CA6NsCsE,IAAI,CAACR,KAAK,iB;kBA7NhDtE,CAAA;8CA8NgBX,YAAA,CAEuBkF,+BAAA;kBAhOvChF,OAAA,EAAAC,QAAA,CA+NkB,MAAsB,CA/NxCO,gBAAA,CAAAS,gBAAA,CA+NqBsE,IAAI,CAACE,WAAW,IAAG,SAAO,GAAAxE,gBAAA,CAAGsE,IAAI,CAACG,WAAW,IAAG,UAAQ,GAAAzE,gBAAA,CAAGsE,IAAI,CAACI,WAAW,iB;kBA/NhGlF,CAAA;;gBAAAA,CAAA;;;YAAAA,CAAA;;UAAAA,CAAA;YAoOUX,YAAA,CAKiBwC,yBAAA;UAzO3BtC,OAAA,EAAAC,QAAA,CAqOY,MAGQ,CAHRH,YAAA,CAGQyB,gBAAA;YAHDT,OAAO,EAAC,MAAM;YAACR,KAAK,EAAC,OAAO;YAAEkB,OAAK,EAAAjB,MAAA,QAAAA,MAAA,MAAAkB,MAAA,IAAEP,IAAA,CAAAQ,OAAO,CAACC,IAAI;;YArOpE3B,OAAA,EAAAC,QAAA,CAqOgF,MAElE,C,4BAvOdO,gBAAA,CAqOgF,UAElE,IAAAV,YAAA,CAAoCM,iBAAA;cAA5BmC,GAAG,EAAH;YAAG;cAvOzBvC,OAAA,EAAAC,QAAA,CAuO0B,MAAeM,MAAA,SAAAA,MAAA,QAvOzCC,gBAAA,CAuO0B,iBAAe,E;cAvOzCC,CAAA;cAAAC,EAAA;;YAAAD,CAAA;YAAAC,EAAA;;UAAAD,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;MA8OIZ,mBAAA,YAAe,EACfC,YAAA,CAyBQC,gBAAA;IAzBDP,KAAK,EAAC;EAAM;IA/OvBQ,OAAA,EAAAC,QAAA,CAgPM,MAuBQ,CAvBRH,YAAA,CAuBQI,gBAAA;MAvBDC,IAAI,EAAC;IAAI;MAhPtBH,OAAA,EAAAC,QAAA,CAiPQ,MAqBS,CArBTH,YAAA,CAqBSgC,iBAAA;QAtQjB9B,OAAA,EAAAC,QAAA,CAkPU,MAYe,CAZfH,YAAA,CAYekC,uBAAA;UAZDxC,KAAK,EAAC;QAAqB;UAlPnDQ,OAAA,EAAAC,QAAA,CAmPY,MAA2D,CAA3DH,YAAA,CAA2DM,iBAAA;YAAnDE,KAAK,EAAC,SAAS;YAACd,KAAK,EAAC;;YAnP1CQ,OAAA,EAAAC,QAAA,CAmPiD,MAAaM,MAAA,SAAAA,MAAA,QAnP9DC,gBAAA,CAmPiD,eAAa,E;YAnP9DC,CAAA;YAAAC,EAAA;0CAAAF,gBAAA,CAmPuE,UAE3D,IAAAV,YAAA,CAAqB8F,mBAAA,GACrB9F,YAAA,CAOQyB,gBAAA;YANNT,OAAO,EAAC,UAAU;YAClBT,IAAI,EAAC,OAAO;YACXmB,OAAK,EAAAjB,MAAA,QAAAA,MAAA,MAAAkB,MAAA,IAAEqC,KAAA,CAAA+B,yBAAyB,IAAI/B,KAAA,CAAA+B,yBAAyB;;YAzP5E7F,OAAA,EAAAC,QAAA,CA2Pc,MAA6C,CA3P3DO,gBAAA,CAAAS,gBAAA,CA2PiB6C,KAAA,CAAA+B,yBAAyB,kBAAiB,GAC7C,iBAAA/F,YAAA,CAA4FM,iBAAA;cAApFmC,GAAG,EAAH;YAAG;cA5PzBvC,OAAA,EAAAC,QAAA,CA4P0B,MAAuE,CA5PjGO,gBAAA,CAAAS,gBAAA,CA4P6B6C,KAAA,CAAA+B,yBAAyB,yD;cA5PtDpF,CAAA;;YAAAA,CAAA;;UAAAA,CAAA;UAAAC,EAAA;YA+PUZ,YAAA,CAMsBgG,8BAAA;UArQhC9F,OAAA,EAAAC,QAAA,CAgQY,MAIM,C,gBAJNU,mBAAA,CAIM,cAHJb,YAAA,CAEcqC,sBAAA;YAnQ5BnC,OAAA,EAAAC,QAAA,CAkQgB,MAAgC,CAAhCH,YAAA,CAAgCiG,uCAAA,E;YAlQhDtF,CAAA;gDAgQyBqD,KAAA,CAAA+B,yBAAyB,E;UAhQlDpF,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;MA0QIZ,mBAAA,aAAgB,EAChBC,YAAA,CA2EQC,gBAAA;IA3EDP,KAAK,EAAC;EAAM;IA3QvBQ,OAAA,EAAAC,QAAA,CA4QM,MAsCQ,CAtCRH,YAAA,CAsCQI,gBAAA;MAtCDC,IAAI,EAAC,IAAI;MAACyB,EAAE,EAAC;;MA5Q1B5B,OAAA,EAAAC,QAAA,CA6QQ,MAoCS,CApCTH,YAAA,CAoCSgC,iBAAA;QAjTjB9B,OAAA,EAAAC,QAAA,CA8QU,MAGe,CAHfH,YAAA,CAGekC,uBAAA;UAHDxC,KAAK,EAAC;QAAqB;UA9QnDQ,OAAA,EAAAC,QAAA,CA+QY,MAAiE,CAAjEH,YAAA,CAAiEM,iBAAA;YAAzDE,KAAK,EAAC,SAAS;YAACd,KAAK,EAAC;;YA/Q1CQ,OAAA,EAAAC,QAAA,CA+QiD,MAAmBM,MAAA,SAAAA,MAAA,QA/QpEC,gBAAA,CA+QiD,qBAAmB,E;YA/QpEC,CAAA;YAAAC,EAAA;0CAAAF,gBAAA,CA+Q6E,QAEnE,G;UAjRVC,CAAA;UAAAC,EAAA;YAkRUZ,YAAA,CAwBcqC,sBAAA;UA1SxBnC,OAAA,EAAAC,QAAA,CA4WiC,MASpB,CAlGUiB,IAAA,CAAAgC,OAAO,I,cAAlBvD,mBAAA,CAEM,OAFNqG,WAEM,GADJlG,YAAA,CAAyEsD,8BAAA;YAApDC,aAAa,EAAb,EAAa;YAAC/C,KAAK,EAAC;kBAE3BwD,KAAA,CAAAmC,OAAO,CAAC1C,MAAM,U,cAA9B5D,mBAAA,CAGM,OAHNuG,WAGM,GAFJpG,YAAA,CAA8DM,iBAAA;YAAtDC,IAAI,EAAC,OAAO;YAACC,KAAK,EAAC;;YAvRzCN,OAAA,EAAAC,QAAA,CAuRgD,MAAmBM,MAAA,SAAAA,MAAA,QAvRnEC,gBAAA,CAuRgD,qBAAmB,E;YAvRnEC,CAAA;YAAAC,EAAA;0CAwRcC,mBAAA,CAA8D;YAA3DnB,KAAK,EAAC;UAAuC,GAAC,WAAS,qB,oBAE5D2E,YAAA,CAeSC,iBAAA;YAzSrB3E,GAAA;YA0R2B4E,KAAK,EAAC;;YA1RjCrE,OAAA,EAAAC,QAAA,CA2R2B,MAAuB,E,kBAApCN,mBAAA,CAac2E,SAAA,QAxS5BC,WAAA,CA2R0CT,KAAA,CAAAmC,OAAO,EAAfE,IAAI;mCAAxBhC,YAAA,CAacM,sBAAA;gBAbwBhF,GAAG,EAAE0G,IAAI,CAACzB,EAAE;gBAAGC,EAAE,YAAYwB,IAAI,CAACzB,EAAE;;gBACvDE,OAAO,EAAA3E,QAAA,CACtB,MAAgG,CAAhGH,YAAA,CAAgGsG,qBAAA;kBA7RlHC,UAAA,EA6RuCF,IAAI,CAACG,SAAS;kBA7RrD,uBAAA7E,MAAA,IA6RuC0E,IAAI,CAACG,SAAS,GAAA7E,MAAA;kBAAG8E,QAAM,EAAA9E,MAAA,IAAEkC,QAAA,CAAA6C,gBAAgB,CAACL,IAAI;kBAAG,cAAY,EAAZ;;gBAQvDM,MAAM,EAAAxG,QAAA,CACrB,MAA2G,CAA3GH,YAAA,CAA2G4G,iBAAA;kBAAlGpG,KAAK,EAAEqD,QAAA,CAAAgD,kBAAkB,CAACR,IAAI,CAACS,MAAM;kBAAGvG,IAAI,EAAC;;kBAtSxEL,OAAA,EAAAC,QAAA,CAsSgF,MAAoC,CAtSpHO,gBAAA,CAAAS,gBAAA,CAsSmF0C,QAAA,CAAAkD,iBAAiB,CAACV,IAAI,CAACS,MAAM,kB;kBAtShHnG,CAAA;;gBAAAT,OAAA,EAAAC,QAAA,CA+RgB,MAEoB,CAFpBH,YAAA,CAEoBgF,4BAAA;kBAFAtF,KAAK,EA/RzCsH,eAAA;oBAAA,gCA+R6EX,IAAI,CAACS,MAAM;kBAAA;;kBA/RxF5G,OAAA,EAAAC,QAAA,CAgSkB,MAAe,CAhSjCO,gBAAA,CAAAS,gBAAA,CAgSqBkF,IAAI,CAAC7E,IAAI,iB;kBAhS9Bb,CAAA;gEAkSgBX,YAAA,CAEuBkF,+BAAA;kBApSvChF,OAAA,EAAAC,QAAA,CAmSkB,MAAsB,CAnSxCO,gBAAA,CAAAS,gBAAA,CAmSqBkF,IAAI,CAACV,WAAW,IAAG,KAAG,GAAAxE,gBAAA,CAAGkF,IAAI,CAACY,SAAS,IAAG,WAAS,GAAA9F,gBAAA,CAAG0C,QAAA,CAAAqD,UAAU,CAACb,IAAI,CAACc,OAAO,kB;kBAnSlGxG,CAAA;;gBAAAA,CAAA;;;YAAAA,CAAA;;UAAAA,CAAA;YA2SUX,YAAA,CAKiBwC,yBAAA;UAhT3BtC,OAAA,EAAAC,QAAA,CA4SY,MAGQ,CAHRH,YAAA,CAGQyB,gBAAA;YAHDT,OAAO,EAAC,MAAM;YAACR,KAAK,EAAC,SAAS;YAAEkB,OAAK,EAAAjB,MAAA,QAAAA,MAAA,MAAAkB,MAAA,IAAEP,IAAA,CAAAQ,OAAO,CAACC,IAAI;;YA5StE3B,OAAA,EAAAC,QAAA,CA4SkF,MAEpE,C,4BA9SdO,gBAAA,CA4SkF,UAEpE,IAAAV,YAAA,CAAoCM,iBAAA;cAA5BmC,GAAG,EAAH;YAAG;cA9SzBvC,OAAA,EAAAC,QAAA,CA8S0B,MAAeM,MAAA,SAAAA,MAAA,QA9SzCC,gBAAA,CA8S0B,iBAAe,E;cA9SzCC,CAAA;cAAAC,EAAA;;YAAAD,CAAA;YAAAC,EAAA;;UAAAD,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;QAoTMX,YAAA,CAiCQI,gBAAA;MAjCDC,IAAI,EAAC,IAAI;MAACyB,EAAE,EAAC;;MApT1B5B,OAAA,EAAAC,QAAA,CAqTQ,MA+BS,CA/BTH,YAAA,CA+BSgC,iBAAA;QApVjB9B,OAAA,EAAAC,QAAA,CAsTU,MAGe,CAHfH,YAAA,CAGekC,uBAAA;UAHDxC,KAAK,EAAC;QAAqB;UAtTnDQ,OAAA,EAAAC,QAAA,CAuTY,MAAyD,CAAzDH,YAAA,CAAyDM,iBAAA;YAAjDE,KAAK,EAAC,SAAS;YAACd,KAAK,EAAC;;YAvT1CQ,OAAA,EAAAC,QAAA,CAuTiD,MAAWM,MAAA,SAAAA,MAAA,QAvT5DC,gBAAA,CAuTiD,aAAW,E;YAvT5DC,CAAA;YAAAC,EAAA;0CAAAF,gBAAA,CAuTqE,QAE3D,G;UAzTVC,CAAA;UAAAC,EAAA;YA0TUZ,YAAA,CAyBcqC,sBAAA;UAnVxBnC,OAAA,EAAAC,QAAA,CAqbK,MAQiC,CAlIfiB,IAAA,CAAAgC,OAAO,I,cAAlBvD,mBAAA,CAEM,OAFNuH,WAEM,GADJpH,YAAA,CAAyEsD,8BAAA;YAApDC,aAAa,EAAb,EAAa;YAAC/C,KAAK,EAAC;kBAE3BwD,KAAA,CAAAqD,gBAAgB,CAAC5D,MAAM,U,cAAvC5D,mBAAA,CAGM,OAHNyH,WAGM,GAFJtH,YAAA,CAAsDM,iBAAA;YAA9CC,IAAI,EAAC,OAAO;YAACC,KAAK,EAAC;;YA/TzCN,OAAA,EAAAC,QAAA,CA+TgD,MAAWM,MAAA,SAAAA,MAAA,QA/T3DC,gBAAA,CA+TgD,aAAW,E;YA/T3DC,CAAA;YAAAC,EAAA;0CAgUcC,mBAAA,CAA2D;YAAxDnB,KAAK,EAAC;UAAuC,GAAC,QAAM,qB,oBAEzD2E,YAAA,CAgBakD,qBAAA;YAlVzB5H,GAAA;YAkU+B6H,KAAK,EAAC,OAAO;YAACC,IAAI,EAAC,KAAK;YAAC,eAAa,EAAC;;YAlUtEvH,OAAA,EAAAC,QAAA,CAoUgB,MAAoC,E,kBADtCN,mBAAA,CAckB2E,SAAA,QAjVhCC,WAAA,CAoUmCT,KAAA,CAAAqD,gBAAgB,EAA5BK,QAAQ;mCADjBrD,YAAA,CAckBsD,0BAAA;gBAZfhI,GAAG,EAAE+H,QAAQ,CAAC9C,EAAE;gBAChB,WAAS,EAAEf,QAAA,CAAA+D,gBAAgB,CAACF,QAAQ,CAAC3G,IAAI;gBACzC8G,IAAI,EAAEhE,QAAA,CAAAiE,eAAe,CAACJ,QAAQ,CAAC3G,IAAI;gBACpCR,IAAI,EAAC;;gBAxUrBL,OAAA,EAAAC,QAAA,CA0UgB,MAMM,CANNU,mBAAA,CAMM,OANNkH,WAMM,GALJlH,mBAAA,CAGM,cAFJA,mBAAA,CAAyD,OAAzDmH,WAAyD,EAAA7G,gBAAA,CAA7BuG,QAAQ,CAACO,WAAW,kBAChDpH,mBAAA,CAA+E,OAA/EqH,WAA+E,EAAA/G,gBAAA,CAA7BuG,QAAQ,CAAC/B,WAAW,iB,GAExE9E,mBAAA,CAA6F,OAA7FsH,WAA6F,EAAAhH,gBAAA,CAA3C0C,QAAA,CAAAuE,cAAc,CAACV,QAAQ,CAACW,SAAS,kB;gBA/UrG1H,CAAA;;;YAAAA,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}