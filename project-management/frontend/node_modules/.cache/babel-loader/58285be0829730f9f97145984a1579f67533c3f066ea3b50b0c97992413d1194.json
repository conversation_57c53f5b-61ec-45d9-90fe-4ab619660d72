{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createElementVNode as _createElementVNode, mergeProps as _mergeProps, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"d-flex align-center\"\n};\nconst _hoisted_2 = {\n  class: \"d-flex align-center mb-3\"\n};\nconst _hoisted_3 = {\n  class: \"text-h6\"\n};\nconst _hoisted_4 = {\n  class: \"text-caption text-medium-emphasis\"\n};\nconst _hoisted_5 = {\n  class: \"google-drawer-header\"\n};\nconst _hoisted_6 = {\n  class: \"d-flex align-center pa-4\"\n};\nconst _hoisted_7 = {\n  class: \"google-drawer-footer\"\n};\nconst _hoisted_8 = {\n  class: \"google-content\"\n};\nconst _hoisted_9 = {\n  class: \"d-flex align-center justify-space-between w-100 px-4\"\n};\nconst _hoisted_10 = {\n  class: \"d-flex align-center\"\n};\nconst _hoisted_11 = {\n  class: \"text-body-2 text-medium-emphasis\"\n};\nconst _hoisted_12 = {\n  class: \"d-flex align-center\"\n};\nconst _hoisted_13 = {\n  class: \"google-notification-header\"\n};\nconst _hoisted_14 = {\n  class: \"d-flex align-center justify-space-between pa-4\"\n};\nconst _hoisted_15 = {\n  class: \"d-flex align-center\"\n};\nconst _hoisted_16 = {\n  class: \"google-notification-content\"\n};\nconst _hoisted_17 = {\n  class: \"text-caption text-medium-emphasis\"\n};\nconst _hoisted_18 = {\n  key: 0,\n  class: \"text-center pa-8\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_v_icon = _resolveComponent(\"v-icon\");\n  const _component_v_app_bar_nav_icon = _resolveComponent(\"v-app-bar-nav-icon\");\n  const _component_v_spacer = _resolveComponent(\"v-spacer\");\n  const _component_v_text_field = _resolveComponent(\"v-text-field\");\n  const _component_v_badge = _resolveComponent(\"v-badge\");\n  const _component_v_btn = _resolveComponent(\"v-btn\");\n  const _component_v_avatar = _resolveComponent(\"v-avatar\");\n  const _component_v_divider = _resolveComponent(\"v-divider\");\n  const _component_v_list_item_title = _resolveComponent(\"v-list-item-title\");\n  const _component_v_list_item = _resolveComponent(\"v-list-item\");\n  const _component_v_list = _resolveComponent(\"v-list\");\n  const _component_v_card_text = _resolveComponent(\"v-card-text\");\n  const _component_v_card = _resolveComponent(\"v-card\");\n  const _component_v_menu = _resolveComponent(\"v-menu\");\n  const _component_v_app_bar = _resolveComponent(\"v-app-bar\");\n  const _component_v_list_subheader = _resolveComponent(\"v-list-subheader\");\n  const _component_v_chip = _resolveComponent(\"v-chip\");\n  const _component_v_list_item_subtitle = _resolveComponent(\"v-list-item-subtitle\");\n  const _component_v_navigation_drawer = _resolveComponent(\"v-navigation-drawer\");\n  const _component_router_view = _resolveComponent(\"router-view\");\n  const _component_v_main = _resolveComponent(\"v-main\");\n  const _component_v_footer = _resolveComponent(\"v-footer\");\n  const _component_v_layout = _resolveComponent(\"v-layout\");\n  const _component_v_card_title = _resolveComponent(\"v-card-title\");\n  const _component_v_card_actions = _resolveComponent(\"v-card-actions\");\n  const _component_v_dialog = _resolveComponent(\"v-dialog\");\n  const _component_NotificationBar = _resolveComponent(\"NotificationBar\");\n  const _component_v_app = _resolveComponent(\"v-app\");\n  return _openBlock(), _createBlock(_component_v_app, {\n    class: \"medical-app\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_v_layout, {\n      class: \"google-layout\"\n    }, {\n      default: _withCtx(() => [_createCommentVNode(\" 顶部导航栏 - Google风格 \"), _createVNode(_component_v_app_bar, {\n        color: \"white\",\n        app: \"\",\n        flat: \"\",\n        class: \"google-header\",\n        elevation: \"1\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_v_app_bar_nav_icon, {\n          onClick: _cache[0] || (_cache[0] = $event => $data.drawer = !$data.drawer),\n          class: \"google-menu-icon\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_icon, {\n            color: \"grey-darken-1\"\n          }, {\n            default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\"mdi-menu\")])),\n            _: 1 /* STABLE */,\n            __: [11]\n          })]),\n          _: 1 /* STABLE */\n        }), _createCommentVNode(\" 医疗项目管理系统标题 \"), _createElementVNode(\"div\", _hoisted_1, [_createVNode(_component_v_icon, {\n          icon: \"mdi-hospital-box\",\n          color: \"medical-primary\",\n          size: \"32\",\n          class: \"me-3\"\n        }), _cache[12] || (_cache[12] = _createElementVNode(\"div\", null, [_createElementVNode(\"div\", {\n          class: \"google-title\"\n        }, \"医疗项目管理\"), _createElementVNode(\"div\", {\n          class: \"google-subtitle\"\n        }, \"Healthcare Project Management\")], -1 /* HOISTED */))]), _createVNode(_component_v_spacer), _createCommentVNode(\" 搜索框 - Google风格 \"), _createVNode(_component_v_text_field, {\n          modelValue: $data.searchQuery,\n          \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $data.searchQuery = $event),\n          placeholder: \"搜索项目、任务或文档...\",\n          \"prepend-inner-icon\": \"mdi-magnify\",\n          variant: \"outlined\",\n          density: \"compact\",\n          \"hide-details\": \"\",\n          class: \"google-search me-4\",\n          style: {\n            \"max-width\": \"400px\"\n          }\n        }, null, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 通知按钮 \"), _createVNode(_component_v_btn, {\n          icon: \"\",\n          variant: \"text\",\n          class: \"google-icon-btn me-2\",\n          onClick: _cache[2] || (_cache[2] = $event => $data.showNotifications = true)\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_badge, {\n            content: $options.notificationCount,\n            \"model-value\": $options.notificationCount > 0,\n            color: \"medical-accent\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_icon, {\n              color: \"grey-darken-1\"\n            }, {\n              default: _withCtx(() => _cache[13] || (_cache[13] = [_createTextVNode(\"mdi-bell-outline\")])),\n              _: 1 /* STABLE */,\n              __: [13]\n            })]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"content\", \"model-value\"])]),\n          _: 1 /* STABLE */\n        }), _createCommentVNode(\" 用户菜单 \"), _createVNode(_component_v_menu, {\n          \"offset-y\": \"\"\n        }, {\n          activator: _withCtx(({\n            props\n          }) => [_createVNode(_component_v_btn, _mergeProps({\n            icon: \"\",\n            variant: \"text\",\n            class: \"google-icon-btn\"\n          }, props), {\n            default: _withCtx(() => [_createVNode(_component_v_avatar, {\n              size: \"32\",\n              color: \"medical-primary\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_v_icon, {\n                color: \"white\"\n              }, {\n                default: _withCtx(() => _cache[14] || (_cache[14] = [_createTextVNode(\"mdi-account\")])),\n                _: 1 /* STABLE */,\n                __: [14]\n              })]),\n              _: 1 /* STABLE */\n            })]),\n            _: 2 /* DYNAMIC */\n          }, 1040 /* FULL_PROPS, DYNAMIC_SLOTS */)]),\n          default: _withCtx(() => [_createVNode(_component_v_card, {\n            class: \"google-menu\",\n            \"min-width\": \"280\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_card_text, {\n              class: \"pa-4\"\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_v_avatar, {\n                size: \"48\",\n                color: \"medical-primary\",\n                class: \"me-3\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_icon, {\n                  color: \"white\",\n                  size: \"24\"\n                }, {\n                  default: _withCtx(() => _cache[15] || (_cache[15] = [_createTextVNode(\"mdi-account\")])),\n                  _: 1 /* STABLE */,\n                  __: [15]\n                })]),\n                _: 1 /* STABLE */\n              }), _createElementVNode(\"div\", null, [_createElementVNode(\"div\", _hoisted_3, _toDisplayString($options.currentUser.name || '用户'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_4, _toDisplayString($options.currentUser.email || '<EMAIL>'), 1 /* TEXT */)])]), _createVNode(_component_v_divider, {\n                class: \"mb-3\"\n              }), _createVNode(_component_v_list, {\n                density: \"compact\",\n                class: \"pa-0\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_list_item, {\n                  onClick: _cache[3] || (_cache[3] = $event => $options.navigateTo('/profile')),\n                  \"prepend-icon\": \"mdi-account-circle\",\n                  class: \"google-menu-item\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n                    default: _withCtx(() => _cache[16] || (_cache[16] = [_createTextVNode(\"个人资料\")])),\n                    _: 1 /* STABLE */,\n                    __: [16]\n                  })]),\n                  _: 1 /* STABLE */\n                }), _createVNode(_component_v_list_item, {\n                  onClick: _cache[4] || (_cache[4] = $event => $options.navigateTo('/settings')),\n                  \"prepend-icon\": \"mdi-cog\",\n                  class: \"google-menu-item\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n                    default: _withCtx(() => _cache[17] || (_cache[17] = [_createTextVNode(\"系统设置\")])),\n                    _: 1 /* STABLE */,\n                    __: [17]\n                  })]),\n                  _: 1 /* STABLE */\n                }), _createVNode(_component_v_divider, {\n                  class: \"my-2\"\n                }), _createVNode(_component_v_list_item, {\n                  onClick: $options.logout,\n                  \"prepend-icon\": \"mdi-logout\",\n                  class: \"google-menu-item\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n                    default: _withCtx(() => _cache[18] || (_cache[18] = [_createTextVNode(\"退出登录\")])),\n                    _: 1 /* STABLE */,\n                    __: [18]\n                  })]),\n                  _: 1 /* STABLE */\n                }, 8 /* PROPS */, [\"onClick\"])]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 侧边导航菜单 - Google风格 \"), _createVNode(_component_v_navigation_drawer, {\n        modelValue: $data.drawer,\n        \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.drawer = $event),\n        app: \"\",\n        class: \"google-drawer\",\n        width: \"280\"\n      }, {\n        append: _withCtx(() => [_createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_v_divider, {\n          class: \"mb-2\"\n        }), _createVNode(_component_v_list, {\n          density: \"compact\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_list_item, {\n            \"prepend-icon\": \"mdi-help-circle\",\n            class: \"google-nav-item\",\n            onClick: _cache[5] || (_cache[5] = $event => $data.showHelp = true)\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n              default: _withCtx(() => _cache[39] || (_cache[39] = [_createTextVNode(\"帮助中心\")])),\n              _: 1 /* STABLE */,\n              __: [39]\n            })]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_v_list_item, {\n            \"prepend-icon\": \"mdi-cog\",\n            class: \"google-nav-item\",\n            to: \"/settings\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n              default: _withCtx(() => _cache[40] || (_cache[40] = [_createTextVNode(\"系统设置\")])),\n              _: 1 /* STABLE */,\n              __: [40]\n            })]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })])]),\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_v_icon, {\n          icon: \"mdi-hospital-box\",\n          color: \"medical-primary\",\n          size: \"24\",\n          class: \"me-3\"\n        }), _cache[19] || (_cache[19] = _createElementVNode(\"div\", null, [_createElementVNode(\"div\", {\n          class: \"text-subtitle-1 font-weight-medium\"\n        }, \"医疗项目管理\"), _createElementVNode(\"div\", {\n          class: \"text-caption text-medium-emphasis\"\n        }, \"Healthcare PM\")], -1 /* HOISTED */))])]), _createVNode(_component_v_divider), _createVNode(_component_v_list, {\n          class: \"google-nav-list\",\n          nav: \"\"\n        }, {\n          default: _withCtx(() => [_createCommentVNode(\" 概览部分 \"), _createVNode(_component_v_list_subheader, {\n            class: \"google-nav-subheader\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_icon, {\n              icon: \"mdi-view-dashboard\",\n              size: \"16\",\n              class: \"me-2\"\n            }), _cache[20] || (_cache[20] = _createTextVNode(\" 概览 \"))]),\n            _: 1 /* STABLE */,\n            __: [20]\n          }), _createVNode(_component_v_list_item, {\n            to: \"/\",\n            \"prepend-icon\": \"mdi-view-dashboard\",\n            class: \"google-nav-item\",\n            color: \"medical-primary\"\n          }, {\n            append: _withCtx(() => [_createVNode(_component_v_chip, {\n              size: \"x-small\",\n              color: \"medical-accent\",\n              variant: \"flat\"\n            }, {\n              default: _withCtx(() => _cache[22] || (_cache[22] = [_createTextVNode(\"NEW\")])),\n              _: 1 /* STABLE */,\n              __: [22]\n            })]),\n            default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n              default: _withCtx(() => _cache[21] || (_cache[21] = [_createTextVNode(\"仪表盘\")])),\n              _: 1 /* STABLE */,\n              __: [21]\n            })]),\n            _: 1 /* STABLE */\n          }), _createCommentVNode(\" 项目管理部分 \"), _createVNode(_component_v_list_subheader, {\n            class: \"google-nav-subheader\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_icon, {\n              icon: \"mdi-hospital-building\",\n              size: \"16\",\n              class: \"me-2\"\n            }), _cache[23] || (_cache[23] = _createTextVNode(\" 医疗项目 \"))]),\n            _: 1 /* STABLE */,\n            __: [23]\n          }), _createVNode(_component_v_list_item, {\n            to: \"/projects\",\n            \"prepend-icon\": \"mdi-hospital-building\",\n            class: \"google-nav-item\",\n            color: \"medical-primary\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n              default: _withCtx(() => _cache[24] || (_cache[24] = [_createTextVNode(\"项目管理\")])),\n              _: 1 /* STABLE */,\n              __: [24]\n            })]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_v_list_item, {\n            to: \"/gantt\",\n            \"prepend-icon\": \"mdi-chart-timeline-variant\",\n            class: \"google-nav-item\",\n            color: \"medical-primary\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n              default: _withCtx(() => _cache[25] || (_cache[25] = [_createTextVNode(\"项目进度\")])),\n              _: 1 /* STABLE */,\n              __: [25]\n            }), _createVNode(_component_v_list_item_subtitle, null, {\n              default: _withCtx(() => _cache[26] || (_cache[26] = [_createTextVNode(\"甘特图\")])),\n              _: 1 /* STABLE */,\n              __: [26]\n            })]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_v_list_item, {\n            to: \"/modern-kanban\",\n            \"prepend-icon\": \"mdi-view-column\",\n            class: \"google-nav-item\",\n            color: \"medical-primary\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n              default: _withCtx(() => _cache[27] || (_cache[27] = [_createTextVNode(\"项目看板\")])),\n              _: 1 /* STABLE */,\n              __: [27]\n            }), _createVNode(_component_v_list_item_subtitle, null, {\n              default: _withCtx(() => _cache[28] || (_cache[28] = [_createTextVNode(\"敏捷管理\")])),\n              _: 1 /* STABLE */,\n              __: [28]\n            })]),\n            _: 1 /* STABLE */\n          }), _createCommentVNode(\" 任务与协作 \"), _createVNode(_component_v_list_subheader, {\n            class: \"google-nav-subheader\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_icon, {\n              icon: \"mdi-account-group\",\n              size: \"16\",\n              class: \"me-2\"\n            }), _cache[29] || (_cache[29] = _createTextVNode(\" 任务协作 \"))]),\n            _: 1 /* STABLE */,\n            __: [29]\n          }), _createVNode(_component_v_list_item, {\n            to: \"/tasks\",\n            \"prepend-icon\": \"mdi-clipboard-check-multiple\",\n            class: \"google-nav-item\",\n            color: \"medical-primary\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n              default: _withCtx(() => _cache[30] || (_cache[30] = [_createTextVNode(\"任务管理\")])),\n              _: 1 /* STABLE */,\n              __: [30]\n            })]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_v_list_item, {\n            to: \"/meetings\",\n            \"prepend-icon\": \"mdi-video-account\",\n            class: \"google-nav-item\",\n            color: \"medical-primary\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n              default: _withCtx(() => _cache[31] || (_cache[31] = [_createTextVNode(\"会议记录\")])),\n              _: 1 /* STABLE */,\n              __: [31]\n            }), _createVNode(_component_v_list_item_subtitle, null, {\n              default: _withCtx(() => _cache[32] || (_cache[32] = [_createTextVNode(\"团队协作\")])),\n              _: 1 /* STABLE */,\n              __: [32]\n            })]),\n            _: 1 /* STABLE */\n          }), _createCommentVNode(\" 质量与风险 \"), _createVNode(_component_v_list_subheader, {\n            class: \"google-nav-subheader\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_icon, {\n              icon: \"mdi-shield-check\",\n              size: \"16\",\n              class: \"me-2\"\n            }), _cache[33] || (_cache[33] = _createTextVNode(\" 质量控制 \"))]),\n            _: 1 /* STABLE */,\n            __: [33]\n          }), _createVNode(_component_v_list_item, {\n            to: \"/risks\",\n            \"prepend-icon\": \"mdi-alert-circle\",\n            class: \"google-nav-item\",\n            color: \"medical-primary\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n              default: _withCtx(() => _cache[34] || (_cache[34] = [_createTextVNode(\"风险管理\")])),\n              _: 1 /* STABLE */,\n              __: [34]\n            }), _createVNode(_component_v_list_item_subtitle, null, {\n              default: _withCtx(() => _cache[35] || (_cache[35] = [_createTextVNode(\"质量保障\")])),\n              _: 1 /* STABLE */,\n              __: [35]\n            })]),\n            _: 1 /* STABLE */\n          }), _createCommentVNode(\" 分析与报告 \"), _createVNode(_component_v_list_subheader, {\n            class: \"google-nav-subheader\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_icon, {\n              icon: \"mdi-chart-line\",\n              size: \"16\",\n              class: \"me-2\"\n            }), _cache[36] || (_cache[36] = _createTextVNode(\" 数据分析 \"))]),\n            _: 1 /* STABLE */,\n            __: [36]\n          }), _createVNode(_component_v_list_item, {\n            to: \"/reports\",\n            \"prepend-icon\": \"mdi-chart-bar\",\n            class: \"google-nav-item\",\n            color: \"medical-primary\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n              default: _withCtx(() => _cache[37] || (_cache[37] = [_createTextVNode(\"报表分析\")])),\n              _: 1 /* STABLE */,\n              __: [37]\n            }), _createVNode(_component_v_list_item_subtitle, null, {\n              default: _withCtx(() => _cache[38] || (_cache[38] = [_createTextVNode(\"数据洞察\")])),\n              _: 1 /* STABLE */,\n              __: [38]\n            })]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 主内容区域 - Google风格 \"), _createVNode(_component_v_main, {\n        class: \"google-main\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_router_view)])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 页脚 - Google风格 \"), _createVNode(_component_v_footer, {\n        app: \"\",\n        class: \"google-footer\",\n        color: \"white\",\n        elevation: \"1\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_v_icon, {\n          icon: \"mdi-hospital-box\",\n          color: \"medical-primary\",\n          size: \"20\",\n          class: \"me-2\"\n        }), _createElementVNode(\"span\", _hoisted_11, \" © \" + _toDisplayString(new Date().getFullYear()) + \" 医疗项目管理系统 - Healthcare Project Management \", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_v_chip, {\n          size: \"small\",\n          color: \"medical-success\",\n          variant: \"flat\",\n          class: \"me-2\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_icon, {\n            start: \"\",\n            icon: \"mdi-check-circle\"\n          }), _cache[41] || (_cache[41] = _createTextVNode(\" 系统正常 \"))]),\n          _: 1 /* STABLE */,\n          __: [41]\n        }), _cache[42] || (_cache[42] = _createElementVNode(\"span\", {\n          class: \"text-caption text-medium-emphasis\"\n        }, \" 版本 v2.1.0 \", -1 /* HOISTED */))])])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 通知面板 - Google风格 \"), _createVNode(_component_v_navigation_drawer, {\n      modelValue: $data.showNotifications,\n      \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.showNotifications = $event),\n      location: \"right\",\n      temporary: \"\",\n      width: \"400\",\n      class: \"google-notification-panel\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_v_icon, {\n        icon: \"mdi-bell\",\n        color: \"medical-primary\",\n        class: \"me-2\"\n      }), _cache[43] || (_cache[43] = _createElementVNode(\"span\", {\n        class: \"text-h6\"\n      }, \"通知中心\", -1 /* HOISTED */))]), _createVNode(_component_v_btn, {\n        icon: \"\",\n        variant: \"text\",\n        size: \"small\",\n        onClick: _cache[7] || (_cache[7] = $event => $data.showNotifications = false)\n      }, {\n        default: _withCtx(() => [_createVNode(_component_v_icon, null, {\n          default: _withCtx(() => _cache[44] || (_cache[44] = [_createTextVNode(\"mdi-close\")])),\n          _: 1 /* STABLE */,\n          __: [44]\n        })]),\n        _: 1 /* STABLE */\n      })])]), _createVNode(_component_v_divider), _createElementVNode(\"div\", _hoisted_16, [_createVNode(_component_v_list, null, {\n        default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.notifications, notification => {\n          return _openBlock(), _createBlock(_component_v_list_item, {\n            key: notification.id,\n            class: \"google-notification-item\",\n            onClick: $event => $options.handleNotificationClick(notification)\n          }, {\n            prepend: _withCtx(() => [_createVNode(_component_v_avatar, {\n              color: $options.getNotificationColor(notification.type),\n              size: \"40\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_v_icon, {\n                icon: $options.getNotificationIcon(notification.type),\n                color: \"white\"\n              }, null, 8 /* PROPS */, [\"icon\"])]),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"color\"])]),\n            append: _withCtx(() => [_createElementVNode(\"div\", _hoisted_17, _toDisplayString($options.formatTime(notification.time)), 1 /* TEXT */)]),\n            default: _withCtx(() => [_createVNode(_component_v_list_item_title, {\n              class: \"text-wrap\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString(notification.title), 1 /* TEXT */)]),\n              _: 2 /* DYNAMIC */\n            }, 1024 /* DYNAMIC_SLOTS */), _createVNode(_component_v_list_item_subtitle, {\n              class: \"text-wrap\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString(notification.message), 1 /* TEXT */)]),\n              _: 2 /* DYNAMIC */\n            }, 1024 /* DYNAMIC_SLOTS */)]),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]);\n        }), 128 /* KEYED_FRAGMENT */))]),\n        _: 1 /* STABLE */\n      }), $data.notifications.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [_createVNode(_component_v_icon, {\n        icon: \"mdi-bell-off\",\n        size: \"64\",\n        color: \"grey-lighten-2\",\n        class: \"mb-4\"\n      }), _cache[45] || (_cache[45] = _createElementVNode(\"div\", {\n        class: \"text-h6 text-medium-emphasis mb-2\"\n      }, \"暂无通知\", -1 /* HOISTED */)), _cache[46] || (_cache[46] = _createElementVNode(\"div\", {\n        class: \"text-body-2 text-medium-emphasis\"\n      }, \" 所有通知都已处理完毕 \", -1 /* HOISTED */))])) : _createCommentVNode(\"v-if\", true)])]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 帮助对话框 \"), _createVNode(_component_v_dialog, {\n      modelValue: $data.showHelp,\n      \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $data.showHelp = $event),\n      \"max-width\": \"600\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_v_card, {\n        class: \"google-help-dialog\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_v_card_title, {\n          class: \"d-flex align-center\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_icon, {\n            icon: \"mdi-help-circle\",\n            color: \"medical-primary\",\n            class: \"me-2\"\n          }), _cache[47] || (_cache[47] = _createTextVNode(\" 帮助中心 \"))]),\n          _: 1 /* STABLE */,\n          __: [47]\n        }), _createVNode(_component_v_card_text, null, {\n          default: _withCtx(() => [_cache[54] || (_cache[54] = _createElementVNode(\"div\", {\n            class: \"mb-4\"\n          }, [_createElementVNode(\"h3\", {\n            class: \"text-h6 mb-2\"\n          }, \"快速入门\"), _createElementVNode(\"p\", {\n            class: \"text-body-2 text-medium-emphasis\"\n          }, \" 欢迎使用医疗项目管理系统！这里是一些快速入门指南： \")], -1 /* HOISTED */)), _createVNode(_component_v_list, {\n            density: \"compact\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_list_item, {\n              \"prepend-icon\": \"mdi-hospital-building\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n                default: _withCtx(() => _cache[48] || (_cache[48] = [_createTextVNode(\"创建医疗项目\")])),\n                _: 1 /* STABLE */,\n                __: [48]\n              }), _createVNode(_component_v_list_item_subtitle, null, {\n                default: _withCtx(() => _cache[49] || (_cache[49] = [_createTextVNode(\"在项目管理中创建新的医疗实施项目\")])),\n                _: 1 /* STABLE */,\n                __: [49]\n              })]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_v_list_item, {\n              \"prepend-icon\": \"mdi-clipboard-check-multiple\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n                default: _withCtx(() => _cache[50] || (_cache[50] = [_createTextVNode(\"管理任务\")])),\n                _: 1 /* STABLE */,\n                __: [50]\n              }), _createVNode(_component_v_list_item_subtitle, null, {\n                default: _withCtx(() => _cache[51] || (_cache[51] = [_createTextVNode(\"分配和跟踪项目任务进度\")])),\n                _: 1 /* STABLE */,\n                __: [51]\n              })]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_v_list_item, {\n              \"prepend-icon\": \"mdi-chart-timeline-variant\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n                default: _withCtx(() => _cache[52] || (_cache[52] = [_createTextVNode(\"查看进度\")])),\n                _: 1 /* STABLE */,\n                __: [52]\n              }), _createVNode(_component_v_list_item_subtitle, null, {\n                default: _withCtx(() => _cache[53] || (_cache[53] = [_createTextVNode(\"使用甘特图和看板查看项目进度\")])),\n                _: 1 /* STABLE */,\n                __: [53]\n              })]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */,\n          __: [54]\n        }), _createVNode(_component_v_card_actions, null, {\n          default: _withCtx(() => [_createVNode(_component_v_spacer), _createVNode(_component_v_btn, {\n            color: \"medical-primary\",\n            onClick: _cache[9] || (_cache[9] = $event => $data.showHelp = false)\n          }, {\n            default: _withCtx(() => _cache[55] || (_cache[55] = [_createTextVNode(\" 了解了 \")])),\n            _: 1 /* STABLE */,\n            __: [55]\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 通知组件 \"), _createVNode(_component_NotificationBar)]),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["class", "key", "_createBlock", "_component_v_app", "default", "_withCtx", "_createVNode", "_component_v_layout", "_createCommentVNode", "_component_v_app_bar", "color", "app", "flat", "elevation", "_component_v_app_bar_nav_icon", "onClick", "_cache", "$event", "$data", "drawer", "_component_v_icon", "_createTextVNode", "_", "__", "_createElementVNode", "_hoisted_1", "icon", "size", "_component_v_spacer", "_component_v_text_field", "modelValue", "searchQuery", "placeholder", "variant", "density", "style", "_component_v_btn", "showNotifications", "_component_v_badge", "content", "$options", "notificationCount", "_component_v_menu", "activator", "props", "_mergeProps", "_component_v_avatar", "_component_v_card", "_component_v_card_text", "_hoisted_2", "_hoisted_3", "_toDisplayString", "currentUser", "name", "_hoisted_4", "email", "_component_v_divider", "_component_v_list", "_component_v_list_item", "navigateTo", "_component_v_list_item_title", "logout", "_component_v_navigation_drawer", "width", "append", "_hoisted_7", "showHelp", "to", "_hoisted_5", "_hoisted_6", "nav", "_component_v_list_subheader", "_component_v_chip", "_component_v_list_item_subtitle", "_component_v_main", "_hoisted_8", "_component_router_view", "_component_v_footer", "_hoisted_9", "_hoisted_10", "_hoisted_11", "Date", "getFullYear", "_hoisted_12", "start", "location", "temporary", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_createElementBlock", "_Fragment", "_renderList", "notifications", "notification", "id", "handleNotificationClick", "prepend", "getNotificationColor", "type", "getNotificationIcon", "_hoisted_17", "formatTime", "time", "title", "message", "length", "_hoisted_18", "_component_v_dialog", "_component_v_card_title", "_component_v_card_actions", "_component_NotificationBar"], "sources": ["/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/技术测试/项目实施进度管理/project-management/frontend/src/App.vue"], "sourcesContent": ["<template>\n  <v-app class=\"medical-app\">\n    <v-layout class=\"google-layout\">\n      <!-- 顶部导航栏 - Google风格 -->\n      <v-app-bar\n        color=\"white\"\n        app\n        flat\n        class=\"google-header\"\n        elevation=\"1\"\n      >\n        <v-app-bar-nav-icon\n          @click=\"drawer = !drawer\"\n          class=\"google-menu-icon\"\n        >\n          <v-icon color=\"grey-darken-1\">mdi-menu</v-icon>\n        </v-app-bar-nav-icon>\n\n        <!-- 医疗项目管理系统标题 -->\n        <div class=\"d-flex align-center\">\n          <v-icon\n            icon=\"mdi-hospital-box\"\n            color=\"medical-primary\"\n            size=\"32\"\n            class=\"me-3\"\n          ></v-icon>\n          <div>\n            <div class=\"google-title\">医疗项目管理</div>\n            <div class=\"google-subtitle\">Healthcare Project Management</div>\n          </div>\n        </div>\n\n        <v-spacer></v-spacer>\n\n        <!-- 搜索框 - Google风格 -->\n        <v-text-field\n          v-model=\"searchQuery\"\n          placeholder=\"搜索项目、任务或文档...\"\n          prepend-inner-icon=\"mdi-magnify\"\n          variant=\"outlined\"\n          density=\"compact\"\n          hide-details\n          class=\"google-search me-4\"\n          style=\"max-width: 400px;\"\n        ></v-text-field>\n\n        <!-- 通知按钮 -->\n        <v-btn\n          icon\n          variant=\"text\"\n          class=\"google-icon-btn me-2\"\n          @click=\"showNotifications = true\"\n        >\n          <v-badge\n            :content=\"notificationCount\"\n            :model-value=\"notificationCount > 0\"\n            color=\"medical-accent\"\n          >\n            <v-icon color=\"grey-darken-1\">mdi-bell-outline</v-icon>\n          </v-badge>\n        </v-btn>\n\n        <!-- 用户菜单 -->\n        <v-menu offset-y>\n          <template v-slot:activator=\"{ props }\">\n            <v-btn\n              icon\n              variant=\"text\"\n              class=\"google-icon-btn\"\n              v-bind=\"props\"\n            >\n              <v-avatar size=\"32\" color=\"medical-primary\">\n                <v-icon color=\"white\">mdi-account</v-icon>\n              </v-avatar>\n            </v-btn>\n          </template>\n          <v-card class=\"google-menu\" min-width=\"280\">\n            <v-card-text class=\"pa-4\">\n              <div class=\"d-flex align-center mb-3\">\n                <v-avatar size=\"48\" color=\"medical-primary\" class=\"me-3\">\n                  <v-icon color=\"white\" size=\"24\">mdi-account</v-icon>\n                </v-avatar>\n                <div>\n                  <div class=\"text-h6\">{{ currentUser.name || '用户' }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">{{ currentUser.email || '<EMAIL>' }}</div>\n                </div>\n              </div>\n              <v-divider class=\"mb-3\"></v-divider>\n              <v-list density=\"compact\" class=\"pa-0\">\n                <v-list-item\n                  @click=\"navigateTo('/profile')\"\n                  prepend-icon=\"mdi-account-circle\"\n                  class=\"google-menu-item\"\n                >\n                  <v-list-item-title>个人资料</v-list-item-title>\n                </v-list-item>\n                <v-list-item\n                  @click=\"navigateTo('/settings')\"\n                  prepend-icon=\"mdi-cog\"\n                  class=\"google-menu-item\"\n                >\n                  <v-list-item-title>系统设置</v-list-item-title>\n                </v-list-item>\n                <v-divider class=\"my-2\"></v-divider>\n                <v-list-item\n                  @click=\"logout\"\n                  prepend-icon=\"mdi-logout\"\n                  class=\"google-menu-item\"\n                >\n                  <v-list-item-title>退出登录</v-list-item-title>\n                </v-list-item>\n              </v-list>\n            </v-card-text>\n          </v-card>\n        </v-menu>\n      </v-app-bar>\n\n      <!-- 侧边导航菜单 - Google风格 -->\n      <v-navigation-drawer\n        v-model=\"drawer\"\n        app\n        class=\"google-drawer\"\n        width=\"280\"\n      >\n        <!-- 导航头部 -->\n        <div class=\"google-drawer-header\">\n          <div class=\"d-flex align-center pa-4\">\n            <v-icon\n              icon=\"mdi-hospital-box\"\n              color=\"medical-primary\"\n              size=\"24\"\n              class=\"me-3\"\n            ></v-icon>\n            <div>\n              <div class=\"text-subtitle-1 font-weight-medium\">医疗项目管理</div>\n              <div class=\"text-caption text-medium-emphasis\">Healthcare PM</div>\n            </div>\n          </div>\n        </div>\n\n        <v-divider></v-divider>\n\n        <!-- 主要导航 -->\n        <v-list class=\"google-nav-list\" nav>\n          <!-- 概览部分 -->\n          <v-list-subheader class=\"google-nav-subheader\">\n            <v-icon icon=\"mdi-view-dashboard\" size=\"16\" class=\"me-2\"></v-icon>\n            概览\n          </v-list-subheader>\n\n          <v-list-item\n            to=\"/\"\n            prepend-icon=\"mdi-view-dashboard\"\n            class=\"google-nav-item\"\n            color=\"medical-primary\"\n          >\n            <v-list-item-title>仪表盘</v-list-item-title>\n            <template v-slot:append>\n              <v-chip size=\"x-small\" color=\"medical-accent\" variant=\"flat\">NEW</v-chip>\n            </template>\n          </v-list-item>\n\n          <!-- 项目管理部分 -->\n          <v-list-subheader class=\"google-nav-subheader\">\n            <v-icon icon=\"mdi-hospital-building\" size=\"16\" class=\"me-2\"></v-icon>\n            医疗项目\n          </v-list-subheader>\n\n          <v-list-item\n            to=\"/projects\"\n            prepend-icon=\"mdi-hospital-building\"\n            class=\"google-nav-item\"\n            color=\"medical-primary\"\n          >\n            <v-list-item-title>项目管理</v-list-item-title>\n          </v-list-item>\n\n          <v-list-item\n            to=\"/gantt\"\n            prepend-icon=\"mdi-chart-timeline-variant\"\n            class=\"google-nav-item\"\n            color=\"medical-primary\"\n          >\n            <v-list-item-title>项目进度</v-list-item-title>\n            <v-list-item-subtitle>甘特图</v-list-item-subtitle>\n          </v-list-item>\n\n          <v-list-item\n            to=\"/modern-kanban\"\n            prepend-icon=\"mdi-view-column\"\n            class=\"google-nav-item\"\n            color=\"medical-primary\"\n          >\n            <v-list-item-title>项目看板</v-list-item-title>\n            <v-list-item-subtitle>敏捷管理</v-list-item-subtitle>\n          </v-list-item>\n\n          <!-- 任务与协作 -->\n          <v-list-subheader class=\"google-nav-subheader\">\n            <v-icon icon=\"mdi-account-group\" size=\"16\" class=\"me-2\"></v-icon>\n            任务协作\n          </v-list-subheader>\n\n          <v-list-item\n            to=\"/tasks\"\n            prepend-icon=\"mdi-clipboard-check-multiple\"\n            class=\"google-nav-item\"\n            color=\"medical-primary\"\n          >\n            <v-list-item-title>任务管理</v-list-item-title>\n          </v-list-item>\n\n          <v-list-item\n            to=\"/meetings\"\n            prepend-icon=\"mdi-video-account\"\n            class=\"google-nav-item\"\n            color=\"medical-primary\"\n          >\n            <v-list-item-title>会议记录</v-list-item-title>\n            <v-list-item-subtitle>团队协作</v-list-item-subtitle>\n          </v-list-item>\n\n          <!-- 质量与风险 -->\n          <v-list-subheader class=\"google-nav-subheader\">\n            <v-icon icon=\"mdi-shield-check\" size=\"16\" class=\"me-2\"></v-icon>\n            质量控制\n          </v-list-subheader>\n\n          <v-list-item\n            to=\"/risks\"\n            prepend-icon=\"mdi-alert-circle\"\n            class=\"google-nav-item\"\n            color=\"medical-primary\"\n          >\n            <v-list-item-title>风险管理</v-list-item-title>\n            <v-list-item-subtitle>质量保障</v-list-item-subtitle>\n          </v-list-item>\n\n          <!-- 分析与报告 -->\n          <v-list-subheader class=\"google-nav-subheader\">\n            <v-icon icon=\"mdi-chart-line\" size=\"16\" class=\"me-2\"></v-icon>\n            数据分析\n          </v-list-subheader>\n\n          <v-list-item\n            to=\"/reports\"\n            prepend-icon=\"mdi-chart-bar\"\n            class=\"google-nav-item\"\n            color=\"medical-primary\"\n          >\n            <v-list-item-title>报表分析</v-list-item-title>\n            <v-list-item-subtitle>数据洞察</v-list-item-subtitle>\n          </v-list-item>\n        </v-list>\n\n        <!-- 底部快捷操作 -->\n        <template v-slot:append>\n          <div class=\"google-drawer-footer\">\n            <v-divider class=\"mb-2\"></v-divider>\n            <v-list density=\"compact\">\n              <v-list-item\n                prepend-icon=\"mdi-help-circle\"\n                class=\"google-nav-item\"\n                @click=\"showHelp = true\"\n              >\n                <v-list-item-title>帮助中心</v-list-item-title>\n              </v-list-item>\n              <v-list-item\n                prepend-icon=\"mdi-cog\"\n                class=\"google-nav-item\"\n                to=\"/settings\"\n              >\n                <v-list-item-title>系统设置</v-list-item-title>\n              </v-list-item>\n            </v-list>\n          </div>\n        </template>\n      </v-navigation-drawer>\n\n      <!-- 主内容区域 - Google风格 -->\n      <v-main class=\"google-main\">\n        <div class=\"google-content\">\n          <router-view></router-view>\n        </div>\n      </v-main>\n\n      <!-- 页脚 - Google风格 -->\n      <v-footer\n        app\n        class=\"google-footer\"\n        color=\"white\"\n        elevation=\"1\"\n      >\n        <div class=\"d-flex align-center justify-space-between w-100 px-4\">\n          <div class=\"d-flex align-center\">\n            <v-icon\n              icon=\"mdi-hospital-box\"\n              color=\"medical-primary\"\n              size=\"20\"\n              class=\"me-2\"\n            ></v-icon>\n            <span class=\"text-body-2 text-medium-emphasis\">\n              &copy; {{ new Date().getFullYear() }} 医疗项目管理系统 - Healthcare Project Management\n            </span>\n          </div>\n          <div class=\"d-flex align-center\">\n            <v-chip\n              size=\"small\"\n              color=\"medical-success\"\n              variant=\"flat\"\n              class=\"me-2\"\n            >\n              <v-icon start icon=\"mdi-check-circle\"></v-icon>\n              系统正常\n            </v-chip>\n            <span class=\"text-caption text-medium-emphasis\">\n              版本 v2.1.0\n            </span>\n          </div>\n        </div>\n      </v-footer>\n    </v-layout>\n\n    <!-- 通知面板 - Google风格 -->\n    <v-navigation-drawer\n      v-model=\"showNotifications\"\n      location=\"right\"\n      temporary\n      width=\"400\"\n      class=\"google-notification-panel\"\n    >\n      <div class=\"google-notification-header\">\n        <div class=\"d-flex align-center justify-space-between pa-4\">\n          <div class=\"d-flex align-center\">\n            <v-icon icon=\"mdi-bell\" color=\"medical-primary\" class=\"me-2\"></v-icon>\n            <span class=\"text-h6\">通知中心</span>\n          </div>\n          <v-btn\n            icon\n            variant=\"text\"\n            size=\"small\"\n            @click=\"showNotifications = false\"\n          >\n            <v-icon>mdi-close</v-icon>\n          </v-btn>\n        </div>\n      </div>\n\n      <v-divider></v-divider>\n\n      <div class=\"google-notification-content\">\n        <v-list>\n          <v-list-item\n            v-for=\"notification in notifications\"\n            :key=\"notification.id\"\n            class=\"google-notification-item\"\n            @click=\"handleNotificationClick(notification)\"\n          >\n            <template v-slot:prepend>\n              <v-avatar\n                :color=\"getNotificationColor(notification.type)\"\n                size=\"40\"\n              >\n                <v-icon\n                  :icon=\"getNotificationIcon(notification.type)\"\n                  color=\"white\"\n                ></v-icon>\n              </v-avatar>\n            </template>\n\n            <v-list-item-title class=\"text-wrap\">\n              {{ notification.title }}\n            </v-list-item-title>\n            <v-list-item-subtitle class=\"text-wrap\">\n              {{ notification.message }}\n            </v-list-item-subtitle>\n\n            <template v-slot:append>\n              <div class=\"text-caption text-medium-emphasis\">\n                {{ formatTime(notification.time) }}\n              </div>\n            </template>\n          </v-list-item>\n        </v-list>\n\n        <div v-if=\"notifications.length === 0\" class=\"text-center pa-8\">\n          <v-icon\n            icon=\"mdi-bell-off\"\n            size=\"64\"\n            color=\"grey-lighten-2\"\n            class=\"mb-4\"\n          ></v-icon>\n          <div class=\"text-h6 text-medium-emphasis mb-2\">暂无通知</div>\n          <div class=\"text-body-2 text-medium-emphasis\">\n            所有通知都已处理完毕\n          </div>\n        </div>\n      </div>\n    </v-navigation-drawer>\n\n    <!-- 帮助对话框 -->\n    <v-dialog v-model=\"showHelp\" max-width=\"600\">\n      <v-card class=\"google-help-dialog\">\n        <v-card-title class=\"d-flex align-center\">\n          <v-icon icon=\"mdi-help-circle\" color=\"medical-primary\" class=\"me-2\"></v-icon>\n          帮助中心\n        </v-card-title>\n        <v-card-text>\n          <div class=\"mb-4\">\n            <h3 class=\"text-h6 mb-2\">快速入门</h3>\n            <p class=\"text-body-2 text-medium-emphasis\">\n              欢迎使用医疗项目管理系统！这里是一些快速入门指南：\n            </p>\n          </div>\n\n          <v-list density=\"compact\">\n            <v-list-item prepend-icon=\"mdi-hospital-building\">\n              <v-list-item-title>创建医疗项目</v-list-item-title>\n              <v-list-item-subtitle>在项目管理中创建新的医疗实施项目</v-list-item-subtitle>\n            </v-list-item>\n            <v-list-item prepend-icon=\"mdi-clipboard-check-multiple\">\n              <v-list-item-title>管理任务</v-list-item-title>\n              <v-list-item-subtitle>分配和跟踪项目任务进度</v-list-item-subtitle>\n            </v-list-item>\n            <v-list-item prepend-icon=\"mdi-chart-timeline-variant\">\n              <v-list-item-title>查看进度</v-list-item-title>\n              <v-list-item-subtitle>使用甘特图和看板查看项目进度</v-list-item-subtitle>\n            </v-list-item>\n          </v-list>\n        </v-card-text>\n        <v-card-actions>\n          <v-spacer></v-spacer>\n          <v-btn color=\"medical-primary\" @click=\"showHelp = false\">\n            了解了\n          </v-btn>\n        </v-card-actions>\n      </v-card>\n    </v-dialog>\n\n    <!-- 通知组件 -->\n    <NotificationBar />\n  </v-app>\n</template>\n\n<script>\nimport NotificationBar from '@/components/common/NotificationBar.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    NotificationBar\n  },\n  data() {\n    return {\n      drawer: true,\n      searchQuery: '',\n      showNotifications: false,\n      showHelp: false,\n\n      // 模拟通知数据\n      notifications: [\n        {\n          id: 1,\n          type: 'task',\n          title: '任务即将到期',\n          message: '医院A系统集成任务将在2小时后到期',\n          time: new Date(Date.now() - 1000 * 60 * 30) // 30分钟前\n        },\n        {\n          id: 2,\n          type: 'project',\n          title: '项目状态更新',\n          message: '医院B数据迁移项目已完成第二阶段',\n          time: new Date(Date.now() - 1000 * 60 * 60 * 2) // 2小时前\n        },\n        {\n          id: 3,\n          type: 'meeting',\n          title: '会议提醒',\n          message: '项目评审会议将在明天上午10:00开始',\n          time: new Date(Date.now() - 1000 * 60 * 60 * 4) // 4小时前\n        }\n      ]\n    }\n  },\n\n  computed: {\n    currentUser() {\n      return this.$store.getters['auth/user'] || {}\n    },\n\n    notificationCount() {\n      return this.notifications.length\n    }\n  },\n\n  methods: {\n    navigateTo(route) {\n      this.$router.push(route)\n    },\n\n    logout() {\n      // 清除认证信息\n      this.$store.dispatch('auth/logout')\n      // 重定向到登录页\n      this.$router.push('/login')\n    },\n\n    handleNotificationClick(notification) {\n      // 根据通知类型跳转到相应页面\n      switch (notification.type) {\n        case 'task':\n          this.$router.push('/tasks')\n          break\n        case 'project':\n          this.$router.push('/projects')\n          break\n        case 'meeting':\n          this.$router.push('/meetings')\n          break\n      }\n      this.showNotifications = false\n    },\n\n    getNotificationColor(type) {\n      const colors = {\n        'task': 'medical-warning',\n        'project': 'medical-primary',\n        'meeting': 'medical-info',\n        'risk': 'medical-error'\n      }\n      return colors[type] || 'grey'\n    },\n\n    getNotificationIcon(type) {\n      const icons = {\n        'task': 'mdi-clipboard-check',\n        'project': 'mdi-hospital-building',\n        'meeting': 'mdi-calendar',\n        'risk': 'mdi-alert-circle'\n      }\n      return icons[type] || 'mdi-information'\n    },\n\n    formatTime(time) {\n      const now = new Date()\n      const diff = now - time\n      const minutes = Math.floor(diff / (1000 * 60))\n      const hours = Math.floor(diff / (1000 * 60 * 60))\n      const days = Math.floor(diff / (1000 * 60 * 60 * 24))\n\n      if (minutes < 60) {\n        return `${minutes}分钟前`\n      } else if (hours < 24) {\n        return `${hours}小时前`\n      } else {\n        return `${days}天前`\n      }\n    }\n  }\n}\n</script>\n\n<style>\n/* 医疗项目管理系统 - Google风格样式 */\n\n/* CSS变量定义 - 医疗主题色彩 */\n:root {\n  /* 医疗主题色彩 */\n  --medical-primary: #1565C0;        /* 医疗蓝 */\n  --medical-secondary: #2E7D32;      /* 医疗绿 */\n  --medical-accent: #E53935;         /* 医疗红 */\n  --medical-warning: #F57C00;        /* 医疗橙 */\n  --medical-info: #1976D2;           /* 信息蓝 */\n  --medical-success: #388E3C;        /* 成功绿 */\n  --medical-error: #D32F2F;          /* 错误红 */\n\n  /* Google风格色彩 */\n  --google-blue: #4285F4;\n  --google-red: #EA4335;\n  --google-yellow: #FBBC04;\n  --google-green: #34A853;\n  --google-grey-50: #FAFAFA;\n  --google-grey-100: #F5F5F5;\n  --google-grey-200: #EEEEEE;\n  --google-grey-300: #E0E0E0;\n  --google-grey-400: #BDBDBD;\n  --google-grey-500: #9E9E9E;\n  --google-grey-600: #757575;\n  --google-grey-700: #616161;\n  --google-grey-800: #424242;\n  --google-grey-900: #212121;\n\n  /* 阴影定义 */\n  --google-shadow-1: 0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15);\n  --google-shadow-2: 0 1px 2px 0 rgba(60,64,67,.3), 0 2px 6px 2px rgba(60,64,67,.15);\n  --google-shadow-3: 0 4px 8px 3px rgba(60,64,67,.15), 0 1px 3px rgba(60,64,67,.3);\n  --google-shadow-4: 0 6px 10px 4px rgba(60,64,67,.15), 0 2px 3px rgba(60,64,67,.3);\n\n  /* 圆角定义 */\n  --google-radius-small: 4px;\n  --google-radius-medium: 8px;\n  --google-radius-large: 12px;\n  --google-radius-xl: 16px;\n}\n\n/* 全局样式重置 */\n* {\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: 'Google Sans', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;\n  margin: 0;\n  padding: 0;\n  background-color: var(--google-grey-50);\n  color: var(--google-grey-800);\n  line-height: 1.5;\n}\n\n/* 医疗应用主容器 */\n.medical-app {\n  background-color: var(--google-grey-50);\n}\n\n.google-layout {\n  min-height: 100vh;\n}\n\n/* Google风格头部导航 */\n.google-header {\n  border-bottom: 1px solid var(--google-grey-200);\n  backdrop-filter: blur(10px);\n  background-color: rgba(255, 255, 255, 0.95) !important;\n}\n\n.google-menu-icon {\n  border-radius: var(--google-radius-medium);\n  transition: background-color 0.2s ease;\n}\n\n.google-menu-icon:hover {\n  background-color: var(--google-grey-100);\n}\n\n.google-title {\n  font-size: 1.375rem;\n  font-weight: 500;\n  color: var(--medical-primary);\n  line-height: 1.2;\n}\n\n.google-subtitle {\n  font-size: 0.75rem;\n  color: var(--google-grey-600);\n  font-weight: 400;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n/* Google风格搜索框 */\n.google-search .v-field {\n  border-radius: var(--google-radius-large) !important;\n  background-color: var(--google-grey-100);\n  border: none !important;\n  box-shadow: none !important;\n}\n\n.google-search .v-field:hover {\n  background-color: var(--google-grey-200);\n  box-shadow: var(--google-shadow-1) !important;\n}\n\n.google-search .v-field--focused {\n  background-color: white !important;\n  box-shadow: var(--google-shadow-2) !important;\n}\n\n/* Google风格图标按钮 */\n.google-icon-btn {\n  border-radius: var(--google-radius-medium) !important;\n  transition: all 0.2s ease !important;\n}\n\n.google-icon-btn:hover {\n  background-color: var(--google-grey-100) !important;\n}\n\n/* Google风格菜单 */\n.google-menu {\n  border-radius: var(--google-radius-medium) !important;\n  box-shadow: var(--google-shadow-3) !important;\n  border: 1px solid var(--google-grey-200);\n}\n\n.google-menu-item {\n  border-radius: var(--google-radius-small) !important;\n  margin: 2px 0 !important;\n  transition: background-color 0.2s ease !important;\n}\n\n.google-menu-item:hover {\n  background-color: var(--google-grey-100) !important;\n}\n</style>\n"], "mappings": ";;EAmBaA,KAAK,EAAC;AAAqB;;EA2DrBA,KAAK,EAAC;AAA0B;;EAK5BA,KAAK,EAAC;AAAS;;EACfA,KAAK,EAAC;AAAmC;;EAyCnDA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAA0B;;EAmIhCA,KAAK,EAAC;AAAsB;;EAwB9BA,KAAK,EAAC;AAAgB;;EAYtBA,KAAK,EAAC;AAAsD;;EAC1DA,KAAK,EAAC;AAAqB;;EAOxBA,KAAK,EAAC;AAAkC;;EAI3CA,KAAK,EAAC;AAAqB;;EA0B/BA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAAgD;;EACpDA,KAAK,EAAC;AAAqB;;EAiB/BA,KAAK,EAAC;AAA6B;;EA4B3BA,KAAK,EAAC;AAAmC;;EA1X5DC,GAAA;EAiY+CD,KAAK,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAhYnDE,YAAA,CAwbQC,gBAAA;IAxbDH,KAAK,EAAC;EAAa;IAD5BI,OAAA,EAAAC,QAAA,CAEI,MA+TW,CA/TXC,YAAA,CA+TWC,mBAAA;MA/TDP,KAAK,EAAC;IAAe;MAFnCI,OAAA,EAAAC,QAAA,CAGM,MAAyB,CAAzBG,mBAAA,sBAAyB,EACzBF,YAAA,CA+GYG,oBAAA;QA9GVC,KAAK,EAAC,OAAO;QACbC,GAAG,EAAH,EAAG;QACHC,IAAI,EAAJ,EAAI;QACJZ,KAAK,EAAC,eAAe;QACrBa,SAAS,EAAC;;QATlBT,OAAA,EAAAC,QAAA,CAWQ,MAKqB,CALrBC,YAAA,CAKqBQ,6BAAA;UAJlBC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,KAAA,CAAAC,MAAM,IAAID,KAAA,CAAAC,MAAM;UACxBnB,KAAK,EAAC;;UAbhBI,OAAA,EAAAC,QAAA,CAeU,MAA+C,CAA/CC,YAAA,CAA+Cc,iBAAA;YAAvCV,KAAK,EAAC;UAAe;YAfvCN,OAAA,EAAAC,QAAA,CAewC,MAAQW,MAAA,SAAAA,MAAA,QAfhDK,gBAAA,CAewC,UAAQ,E;YAfhDC,CAAA;YAAAC,EAAA;;UAAAD,CAAA;YAkBQd,mBAAA,gBAAmB,EACnBgB,mBAAA,CAWM,OAXNC,UAWM,GAVJnB,YAAA,CAKUc,iBAAA;UAJRM,IAAI,EAAC,kBAAkB;UACvBhB,KAAK,EAAC,iBAAiB;UACvBiB,IAAI,EAAC,IAAI;UACT3B,KAAK,EAAC;wCAERwB,mBAAA,CAGM,cAFJA,mBAAA,CAAsC;UAAjCxB,KAAK,EAAC;QAAc,GAAC,QAAM,GAChCwB,mBAAA,CAAgE;UAA3DxB,KAAK,EAAC;QAAiB,GAAC,+BAA6B,E,wBAI9DM,YAAA,CAAqBsB,mBAAA,GAErBpB,mBAAA,oBAAuB,EACvBF,YAAA,CASgBuB,uBAAA;UA5CxBC,UAAA,EAoCmBZ,KAAA,CAAAa,WAAW;UApC9B,uBAAAf,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAoCmBC,KAAA,CAAAa,WAAW,GAAAd,MAAA;UACpBe,WAAW,EAAC,eAAe;UAC3B,oBAAkB,EAAC,aAAa;UAChCC,OAAO,EAAC,UAAU;UAClBC,OAAO,EAAC,SAAS;UACjB,cAAY,EAAZ,EAAY;UACZlC,KAAK,EAAC,oBAAoB;UAC1BmC,KAAyB,EAAzB;YAAA;UAAA;iDAGF3B,mBAAA,UAAa,EACbF,YAAA,CAaQ8B,gBAAA;UAZNV,IAAI,EAAJ,EAAI;UACJO,OAAO,EAAC,MAAM;UACdjC,KAAK,EAAC,sBAAsB;UAC3Be,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,KAAA,CAAAmB,iBAAiB;;UAnDnCjC,OAAA,EAAAC,QAAA,CAqDU,MAMU,CANVC,YAAA,CAMUgC,kBAAA;YALPC,OAAO,EAAEC,QAAA,CAAAC,iBAAiB;YAC1B,aAAW,EAAED,QAAA,CAAAC,iBAAiB;YAC/B/B,KAAK,EAAC;;YAxDlBN,OAAA,EAAAC,QAAA,CA0DY,MAAuD,CAAvDC,YAAA,CAAuDc,iBAAA;cAA/CV,KAAK,EAAC;YAAe;cA1DzCN,OAAA,EAAAC,QAAA,CA0D0C,MAAgBW,MAAA,SAAAA,MAAA,QA1D1DK,gBAAA,CA0D0C,kBAAgB,E;cA1D1DC,CAAA;cAAAC,EAAA;;YAAAD,CAAA;;UAAAA,CAAA;YA8DQd,mBAAA,UAAa,EACbF,YAAA,CAmDSoC,iBAAA;UAnDD,UAAQ,EAAR;QAAQ;UACGC,SAAS,EAAAtC,QAAA,CACxB,CASQ;YAVoBuC;UAAK,OACjCtC,YAAA,CASQ8B,gBAAA,EATRS,WAAA,CASQ;YARNnB,IAAI,EAAJ,EAAI;YACJO,OAAO,EAAC,MAAM;YACdjC,KAAK,EAAC;aACE4C,KAAK;YArE3BxC,OAAA,EAAAC,QAAA,CAuEc,MAEW,CAFXC,YAAA,CAEWwC,mBAAA;cAFDnB,IAAI,EAAC,IAAI;cAACjB,KAAK,EAAC;;cAvExCN,OAAA,EAAAC,QAAA,CAwEgB,MAA0C,CAA1CC,YAAA,CAA0Cc,iBAAA;gBAAlCV,KAAK,EAAC;cAAO;gBAxErCN,OAAA,EAAAC,QAAA,CAwEsC,MAAWW,MAAA,SAAAA,MAAA,QAxEjDK,gBAAA,CAwEsC,aAAW,E;gBAxEjDC,CAAA;gBAAAC,EAAA;;cAAAD,CAAA;;YAAAA,CAAA;;UAAAlB,OAAA,EAAAC,QAAA,CA4EU,MAqCS,CArCTC,YAAA,CAqCSyC,iBAAA;YArCD/C,KAAK,EAAC,aAAa;YAAC,WAAS,EAAC;;YA5EhDI,OAAA,EAAAC,QAAA,CA6EY,MAmCc,CAnCdC,YAAA,CAmCc0C,sBAAA;cAnCDhD,KAAK,EAAC;YAAM;cA7ErCI,OAAA,EAAAC,QAAA,CA8Ec,MAQM,CARNmB,mBAAA,CAQM,OARNyB,UAQM,GAPJ3C,YAAA,CAEWwC,mBAAA;gBAFDnB,IAAI,EAAC,IAAI;gBAACjB,KAAK,EAAC,iBAAiB;gBAACV,KAAK,EAAC;;gBA/ElEI,OAAA,EAAAC,QAAA,CAgFkB,MAAoD,CAApDC,YAAA,CAAoDc,iBAAA;kBAA5CV,KAAK,EAAC,OAAO;kBAACiB,IAAI,EAAC;;kBAhF7CvB,OAAA,EAAAC,QAAA,CAgFkD,MAAWW,MAAA,SAAAA,MAAA,QAhF7DK,gBAAA,CAgFkD,aAAW,E;kBAhF7DC,CAAA;kBAAAC,EAAA;;gBAAAD,CAAA;kBAkFgBE,mBAAA,CAGM,cAFJA,mBAAA,CAAyD,OAAzD0B,UAAyD,EAAAC,gBAAA,CAAjCX,QAAA,CAAAY,WAAW,CAACC,IAAI,0BACxC7B,mBAAA,CAAkG,OAAlG8B,UAAkG,EAAAH,gBAAA,CAAhDX,QAAA,CAAAY,WAAW,CAACG,KAAK,uC,KAGvEjD,YAAA,CAAoCkD,oBAAA;gBAAzBxD,KAAK,EAAC;cAAM,IACvBM,YAAA,CAuBSmD,iBAAA;gBAvBDvB,OAAO,EAAC,SAAS;gBAAClC,KAAK,EAAC;;gBAxF9CI,OAAA,EAAAC,QAAA,CAyFgB,MAMc,CANdC,YAAA,CAMcoD,sBAAA;kBALX3C,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEuB,QAAA,CAAAmB,UAAU;kBAClB,cAAY,EAAC,oBAAoB;kBACjC3D,KAAK,EAAC;;kBA5FxBI,OAAA,EAAAC,QAAA,CA8FkB,MAA2C,CAA3CC,YAAA,CAA2CsD,4BAAA;oBA9F7DxD,OAAA,EAAAC,QAAA,CA8FqC,MAAIW,MAAA,SAAAA,MAAA,QA9FzCK,gBAAA,CA8FqC,MAAI,E;oBA9FzCC,CAAA;oBAAAC,EAAA;;kBAAAD,CAAA;oBAgGgBhB,YAAA,CAMcoD,sBAAA;kBALX3C,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEuB,QAAA,CAAAmB,UAAU;kBAClB,cAAY,EAAC,SAAS;kBACtB3D,KAAK,EAAC;;kBAnGxBI,OAAA,EAAAC,QAAA,CAqGkB,MAA2C,CAA3CC,YAAA,CAA2CsD,4BAAA;oBArG7DxD,OAAA,EAAAC,QAAA,CAqGqC,MAAIW,MAAA,SAAAA,MAAA,QArGzCK,gBAAA,CAqGqC,MAAI,E;oBArGzCC,CAAA;oBAAAC,EAAA;;kBAAAD,CAAA;oBAuGgBhB,YAAA,CAAoCkD,oBAAA;kBAAzBxD,KAAK,EAAC;gBAAM,IACvBM,YAAA,CAMcoD,sBAAA;kBALX3C,OAAK,EAAEyB,QAAA,CAAAqB,MAAM;kBACd,cAAY,EAAC,YAAY;kBACzB7D,KAAK,EAAC;;kBA3GxBI,OAAA,EAAAC,QAAA,CA6GkB,MAA2C,CAA3CC,YAAA,CAA2CsD,4BAAA;oBA7G7DxD,OAAA,EAAAC,QAAA,CA6GqC,MAAIW,MAAA,SAAAA,MAAA,QA7GzCK,gBAAA,CA6GqC,MAAI,E;oBA7GzCC,CAAA;oBAAAC,EAAA;;kBAAAD,CAAA;;gBAAAA,CAAA;;cAAAA,CAAA;;YAAAA,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;UAqHMd,mBAAA,uBAA0B,EAC1BF,YAAA,CA+JsBwD,8BAAA;QArR5BhC,UAAA,EAuHiBZ,KAAA,CAAAC,MAAM;QAvHvB,uBAAAH,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAuHiBC,KAAA,CAAAC,MAAM,GAAAF,MAAA;QACfN,GAAG,EAAH,EAAG;QACHX,KAAK,EAAC,eAAe;QACrB+D,KAAK,EAAC;;QAsIWC,MAAM,EAAA3D,QAAA,CACrB,MAkBM,CAlBNmB,mBAAA,CAkBM,OAlBNyC,UAkBM,GAjBJ3D,YAAA,CAAoCkD,oBAAA;UAAzBxD,KAAK,EAAC;QAAM,IACvBM,YAAA,CAeSmD,iBAAA;UAfDvB,OAAO,EAAC;QAAS;UAnQrC9B,OAAA,EAAAC,QAAA,CAoQc,MAMc,CANdC,YAAA,CAMcoD,sBAAA;YALZ,cAAY,EAAC,iBAAiB;YAC9B1D,KAAK,EAAC,iBAAiB;YACtBe,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,KAAA,CAAAgD,QAAQ;;YAvQhC9D,OAAA,EAAAC,QAAA,CAyQgB,MAA2C,CAA3CC,YAAA,CAA2CsD,4BAAA;cAzQ3DxD,OAAA,EAAAC,QAAA,CAyQmC,MAAIW,MAAA,SAAAA,MAAA,QAzQvCK,gBAAA,CAyQmC,MAAI,E;cAzQvCC,CAAA;cAAAC,EAAA;;YAAAD,CAAA;cA2QchB,YAAA,CAMcoD,sBAAA;YALZ,cAAY,EAAC,SAAS;YACtB1D,KAAK,EAAC,iBAAiB;YACvBmE,EAAE,EAAC;;YA9QnB/D,OAAA,EAAAC,QAAA,CAgRgB,MAA2C,CAA3CC,YAAA,CAA2CsD,4BAAA;cAhR3DxD,OAAA,EAAAC,QAAA,CAgRmC,MAAIW,MAAA,SAAAA,MAAA,QAhRvCK,gBAAA,CAgRmC,MAAI,E;cAhRvCC,CAAA;cAAAC,EAAA;;YAAAD,CAAA;;UAAAA,CAAA;;QAAAlB,OAAA,EAAAC,QAAA,CA6HQ,MAaM,CAbNmB,mBAAA,CAaM,OAbN4C,UAaM,GAZJ5C,mBAAA,CAWM,OAXN6C,UAWM,GAVJ/D,YAAA,CAKUc,iBAAA;UAJRM,IAAI,EAAC,kBAAkB;UACvBhB,KAAK,EAAC,iBAAiB;UACvBiB,IAAI,EAAC,IAAI;UACT3B,KAAK,EAAC;wCAERwB,mBAAA,CAGM,cAFJA,mBAAA,CAA4D;UAAvDxB,KAAK,EAAC;QAAoC,GAAC,QAAM,GACtDwB,mBAAA,CAAkE;UAA7DxB,KAAK,EAAC;QAAmC,GAAC,eAAa,E,0BAKlEM,YAAA,CAAuBkD,oBAAA,GAGvBlD,YAAA,CA8GSmD,iBAAA;UA9GDzD,KAAK,EAAC,iBAAiB;UAACsE,GAAG,EAAH;;UA/IxClE,OAAA,EAAAC,QAAA,CAgJU,MAAa,CAAbG,mBAAA,UAAa,EACbF,YAAA,CAGmBiE,2BAAA;YAHDvE,KAAK,EAAC;UAAsB;YAjJxDI,OAAA,EAAAC,QAAA,CAkJY,MAAkE,CAAlEC,YAAA,CAAkEc,iBAAA;cAA1DM,IAAI,EAAC,oBAAoB;cAACC,IAAI,EAAC,IAAI;cAAC3B,KAAK,EAAC;4CAlJ9DqB,gBAAA,CAkJ8E,MAEpE,G;YApJVC,CAAA;YAAAC,EAAA;cAsJUjB,YAAA,CAUcoD,sBAAA;YATZS,EAAE,EAAC,GAAG;YACN,cAAY,EAAC,oBAAoB;YACjCnE,KAAK,EAAC,iBAAiB;YACvBU,KAAK,EAAC;;YAGWsD,MAAM,EAAA3D,QAAA,CACrB,MAAyE,CAAzEC,YAAA,CAAyEkE,iBAAA;cAAjE7C,IAAI,EAAC,SAAS;cAACjB,KAAK,EAAC,gBAAgB;cAACuB,OAAO,EAAC;;cA9JpE7B,OAAA,EAAAC,QAAA,CA8J2E,MAAGW,MAAA,SAAAA,MAAA,QA9J9EK,gBAAA,CA8J2E,KAAG,E;cA9J9EC,CAAA;cAAAC,EAAA;;YAAAnB,OAAA,EAAAC,QAAA,CA4JY,MAA0C,CAA1CC,YAAA,CAA0CsD,4BAAA;cA5JtDxD,OAAA,EAAAC,QAAA,CA4J+B,MAAGW,MAAA,SAAAA,MAAA,QA5JlCK,gBAAA,CA4J+B,KAAG,E;cA5JlCC,CAAA;cAAAC,EAAA;;YAAAD,CAAA;cAkKUd,mBAAA,YAAe,EACfF,YAAA,CAGmBiE,2BAAA;YAHDvE,KAAK,EAAC;UAAsB;YAnKxDI,OAAA,EAAAC,QAAA,CAoKY,MAAqE,CAArEC,YAAA,CAAqEc,iBAAA;cAA7DM,IAAI,EAAC,uBAAuB;cAACC,IAAI,EAAC,IAAI;cAAC3B,KAAK,EAAC;4CApKjEqB,gBAAA,CAoKiF,QAEvE,G;YAtKVC,CAAA;YAAAC,EAAA;cAwKUjB,YAAA,CAOcoD,sBAAA;YANZS,EAAE,EAAC,WAAW;YACd,cAAY,EAAC,uBAAuB;YACpCnE,KAAK,EAAC,iBAAiB;YACvBU,KAAK,EAAC;;YA5KlBN,OAAA,EAAAC,QAAA,CA8KY,MAA2C,CAA3CC,YAAA,CAA2CsD,4BAAA;cA9KvDxD,OAAA,EAAAC,QAAA,CA8K+B,MAAIW,MAAA,SAAAA,MAAA,QA9KnCK,gBAAA,CA8K+B,MAAI,E;cA9KnCC,CAAA;cAAAC,EAAA;;YAAAD,CAAA;cAiLUhB,YAAA,CAQcoD,sBAAA;YAPZS,EAAE,EAAC,QAAQ;YACX,cAAY,EAAC,4BAA4B;YACzCnE,KAAK,EAAC,iBAAiB;YACvBU,KAAK,EAAC;;YArLlBN,OAAA,EAAAC,QAAA,CAuLY,MAA2C,CAA3CC,YAAA,CAA2CsD,4BAAA;cAvLvDxD,OAAA,EAAAC,QAAA,CAuL+B,MAAIW,MAAA,SAAAA,MAAA,QAvLnCK,gBAAA,CAuL+B,MAAI,E;cAvLnCC,CAAA;cAAAC,EAAA;gBAwLYjB,YAAA,CAAgDmE,+BAAA;cAxL5DrE,OAAA,EAAAC,QAAA,CAwLkC,MAAGW,MAAA,SAAAA,MAAA,QAxLrCK,gBAAA,CAwLkC,KAAG,E;cAxLrCC,CAAA;cAAAC,EAAA;;YAAAD,CAAA;cA2LUhB,YAAA,CAQcoD,sBAAA;YAPZS,EAAE,EAAC,gBAAgB;YACnB,cAAY,EAAC,iBAAiB;YAC9BnE,KAAK,EAAC,iBAAiB;YACvBU,KAAK,EAAC;;YA/LlBN,OAAA,EAAAC,QAAA,CAiMY,MAA2C,CAA3CC,YAAA,CAA2CsD,4BAAA;cAjMvDxD,OAAA,EAAAC,QAAA,CAiM+B,MAAIW,MAAA,SAAAA,MAAA,QAjMnCK,gBAAA,CAiM+B,MAAI,E;cAjMnCC,CAAA;cAAAC,EAAA;gBAkMYjB,YAAA,CAAiDmE,+BAAA;cAlM7DrE,OAAA,EAAAC,QAAA,CAkMkC,MAAIW,MAAA,SAAAA,MAAA,QAlMtCK,gBAAA,CAkMkC,MAAI,E;cAlMtCC,CAAA;cAAAC,EAAA;;YAAAD,CAAA;cAqMUd,mBAAA,WAAc,EACdF,YAAA,CAGmBiE,2BAAA;YAHDvE,KAAK,EAAC;UAAsB;YAtMxDI,OAAA,EAAAC,QAAA,CAuMY,MAAiE,CAAjEC,YAAA,CAAiEc,iBAAA;cAAzDM,IAAI,EAAC,mBAAmB;cAACC,IAAI,EAAC,IAAI;cAAC3B,KAAK,EAAC;4CAvM7DqB,gBAAA,CAuM6E,QAEnE,G;YAzMVC,CAAA;YAAAC,EAAA;cA2MUjB,YAAA,CAOcoD,sBAAA;YANZS,EAAE,EAAC,QAAQ;YACX,cAAY,EAAC,8BAA8B;YAC3CnE,KAAK,EAAC,iBAAiB;YACvBU,KAAK,EAAC;;YA/MlBN,OAAA,EAAAC,QAAA,CAiNY,MAA2C,CAA3CC,YAAA,CAA2CsD,4BAAA;cAjNvDxD,OAAA,EAAAC,QAAA,CAiN+B,MAAIW,MAAA,SAAAA,MAAA,QAjNnCK,gBAAA,CAiN+B,MAAI,E;cAjNnCC,CAAA;cAAAC,EAAA;;YAAAD,CAAA;cAoNUhB,YAAA,CAQcoD,sBAAA;YAPZS,EAAE,EAAC,WAAW;YACd,cAAY,EAAC,mBAAmB;YAChCnE,KAAK,EAAC,iBAAiB;YACvBU,KAAK,EAAC;;YAxNlBN,OAAA,EAAAC,QAAA,CA0NY,MAA2C,CAA3CC,YAAA,CAA2CsD,4BAAA;cA1NvDxD,OAAA,EAAAC,QAAA,CA0N+B,MAAIW,MAAA,SAAAA,MAAA,QA1NnCK,gBAAA,CA0N+B,MAAI,E;cA1NnCC,CAAA;cAAAC,EAAA;gBA2NYjB,YAAA,CAAiDmE,+BAAA;cA3N7DrE,OAAA,EAAAC,QAAA,CA2NkC,MAAIW,MAAA,SAAAA,MAAA,QA3NtCK,gBAAA,CA2NkC,MAAI,E;cA3NtCC,CAAA;cAAAC,EAAA;;YAAAD,CAAA;cA8NUd,mBAAA,WAAc,EACdF,YAAA,CAGmBiE,2BAAA;YAHDvE,KAAK,EAAC;UAAsB;YA/NxDI,OAAA,EAAAC,QAAA,CAgOY,MAAgE,CAAhEC,YAAA,CAAgEc,iBAAA;cAAxDM,IAAI,EAAC,kBAAkB;cAACC,IAAI,EAAC,IAAI;cAAC3B,KAAK,EAAC;4CAhO5DqB,gBAAA,CAgO4E,QAElE,G;YAlOVC,CAAA;YAAAC,EAAA;cAoOUjB,YAAA,CAQcoD,sBAAA;YAPZS,EAAE,EAAC,QAAQ;YACX,cAAY,EAAC,kBAAkB;YAC/BnE,KAAK,EAAC,iBAAiB;YACvBU,KAAK,EAAC;;YAxOlBN,OAAA,EAAAC,QAAA,CA0OY,MAA2C,CAA3CC,YAAA,CAA2CsD,4BAAA;cA1OvDxD,OAAA,EAAAC,QAAA,CA0O+B,MAAIW,MAAA,SAAAA,MAAA,QA1OnCK,gBAAA,CA0O+B,MAAI,E;cA1OnCC,CAAA;cAAAC,EAAA;gBA2OYjB,YAAA,CAAiDmE,+BAAA;cA3O7DrE,OAAA,EAAAC,QAAA,CA2OkC,MAAIW,MAAA,SAAAA,MAAA,QA3OtCK,gBAAA,CA2OkC,MAAI,E;cA3OtCC,CAAA;cAAAC,EAAA;;YAAAD,CAAA;cA8OUd,mBAAA,WAAc,EACdF,YAAA,CAGmBiE,2BAAA;YAHDvE,KAAK,EAAC;UAAsB;YA/OxDI,OAAA,EAAAC,QAAA,CAgPY,MAA8D,CAA9DC,YAAA,CAA8Dc,iBAAA;cAAtDM,IAAI,EAAC,gBAAgB;cAACC,IAAI,EAAC,IAAI;cAAC3B,KAAK,EAAC;4CAhP1DqB,gBAAA,CAgP0E,QAEhE,G;YAlPVC,CAAA;YAAAC,EAAA;cAoPUjB,YAAA,CAQcoD,sBAAA;YAPZS,EAAE,EAAC,UAAU;YACb,cAAY,EAAC,eAAe;YAC5BnE,KAAK,EAAC,iBAAiB;YACvBU,KAAK,EAAC;;YAxPlBN,OAAA,EAAAC,QAAA,CA0PY,MAA2C,CAA3CC,YAAA,CAA2CsD,4BAAA;cA1PvDxD,OAAA,EAAAC,QAAA,CA0P+B,MAAIW,MAAA,SAAAA,MAAA,QA1PnCK,gBAAA,CA0P+B,MAAI,E;cA1PnCC,CAAA;cAAAC,EAAA;gBA2PYjB,YAAA,CAAiDmE,+BAAA;cA3P7DrE,OAAA,EAAAC,QAAA,CA2PkC,MAAIW,MAAA,SAAAA,MAAA,QA3PtCK,gBAAA,CA2PkC,MAAI,E;cA3PtCC,CAAA;cAAAC,EAAA;;YAAAD,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;yCAuRMd,mBAAA,sBAAyB,EACzBF,YAAA,CAISoE,iBAAA;QAJD1E,KAAK,EAAC;MAAa;QAxRjCI,OAAA,EAAAC,QAAA,CAyRQ,MAEM,CAFNmB,mBAAA,CAEM,OAFNmD,UAEM,GADJrE,YAAA,CAA2BsE,sBAAA,E;QA1RrCtD,CAAA;UA8RMd,mBAAA,mBAAsB,EACtBF,YAAA,CAiCWuE,mBAAA;QAhCTlE,GAAG,EAAH,EAAG;QACHX,KAAK,EAAC,eAAe;QACrBU,KAAK,EAAC,OAAO;QACbG,SAAS,EAAC;;QAnSlBT,OAAA,EAAAC,QAAA,CAqSQ,MA0BM,CA1BNmB,mBAAA,CA0BM,OA1BNsD,UA0BM,GAzBJtD,mBAAA,CAUM,OAVNuD,WAUM,GATJzE,YAAA,CAKUc,iBAAA;UAJRM,IAAI,EAAC,kBAAkB;UACvBhB,KAAK,EAAC,iBAAiB;UACvBiB,IAAI,EAAC,IAAI;UACT3B,KAAK,EAAC;YAERwB,mBAAA,CAEO,QAFPwD,WAEO,EAFwC,KACtC,GAAA7B,gBAAA,KAAO8B,IAAI,GAAGC,WAAW,MAAK,4CACvC,gB,GAEF1D,mBAAA,CAaM,OAbN2D,WAaM,GAZJ7E,YAAA,CAQSkE,iBAAA;UAPP7C,IAAI,EAAC,OAAO;UACZjB,KAAK,EAAC,iBAAiB;UACvBuB,OAAO,EAAC,MAAM;UACdjC,KAAK,EAAC;;UAtTpBI,OAAA,EAAAC,QAAA,CAwTc,MAA+C,CAA/CC,YAAA,CAA+Cc,iBAAA;YAAvCgE,KAAK,EAAL,EAAK;YAAC1D,IAAI,EAAC;0CAxTjCL,gBAAA,CAwT6D,QAEjD,G;UA1TZC,CAAA;UAAAC,EAAA;wCA2TYC,mBAAA,CAEO;UAFDxB,KAAK,EAAC;QAAmC,GAAC,aAEhD,qB;QA7TZsB,CAAA;;MAAAA,CAAA;QAmUId,mBAAA,qBAAwB,EACxBF,YAAA,CA0EsBwD,8BAAA;MA9Y1BhC,UAAA,EAqUeZ,KAAA,CAAAmB,iBAAiB;MArUhC,uBAAArB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAqUeC,KAAA,CAAAmB,iBAAiB,GAAApB,MAAA;MAC1BoE,QAAQ,EAAC,OAAO;MAChBC,SAAS,EAAT,EAAS;MACTvB,KAAK,EAAC,KAAK;MACX/D,KAAK,EAAC;;MAzUZI,OAAA,EAAAC,QAAA,CA2UM,MAeM,CAfNmB,mBAAA,CAeM,OAfN+D,WAeM,GAdJ/D,mBAAA,CAaM,OAbNgE,WAaM,GAZJhE,mBAAA,CAGM,OAHNiE,WAGM,GAFJnF,YAAA,CAAsEc,iBAAA;QAA9DM,IAAI,EAAC,UAAU;QAAChB,KAAK,EAAC,iBAAiB;QAACV,KAAK,EAAC;sCACtDwB,mBAAA,CAAiC;QAA3BxB,KAAK,EAAC;MAAS,GAAC,MAAI,qB,GAE5BM,YAAA,CAOQ8B,gBAAA;QANNV,IAAI,EAAJ,EAAI;QACJO,OAAO,EAAC,MAAM;QACdN,IAAI,EAAC,OAAO;QACXZ,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,KAAA,CAAAmB,iBAAiB;;QArVrCjC,OAAA,EAAAC,QAAA,CAuVY,MAA0B,CAA1BC,YAAA,CAA0Bc,iBAAA;UAvVtChB,OAAA,EAAAC,QAAA,CAuVoB,MAASW,MAAA,SAAAA,MAAA,QAvV7BK,gBAAA,CAuVoB,WAAS,E;UAvV7BC,CAAA;UAAAC,EAAA;;QAAAD,CAAA;cA4VMhB,YAAA,CAAuBkD,oBAAA,GAEvBhC,mBAAA,CA+CM,OA/CNkE,WA+CM,GA9CJpF,YAAA,CAgCSmD,iBAAA;QA/XjBrD,OAAA,EAAAC,QAAA,CAiWY,MAAqC,E,kBADvCsF,mBAAA,CA8BcC,SAAA,QA9XxBC,WAAA,CAiWmC3E,KAAA,CAAA4E,aAAa,EAA7BC,YAAY;+BADrB7F,YAAA,CA8BcwD,sBAAA;YA5BXzD,GAAG,EAAE8F,YAAY,CAACC,EAAE;YACrBhG,KAAK,EAAC,0BAA0B;YAC/Be,OAAK,EAAAE,MAAA,IAAEuB,QAAA,CAAAyD,uBAAuB,CAACF,YAAY;;YAE3BG,OAAO,EAAA7F,QAAA,CACtB,MAQW,CARXC,YAAA,CAQWwC,mBAAA;cAPRpC,KAAK,EAAE8B,QAAA,CAAA2D,oBAAoB,CAACJ,YAAY,CAACK,IAAI;cAC9CzE,IAAI,EAAC;;cAzWrBvB,OAAA,EAAAC,QAAA,CA2WgB,MAGU,CAHVC,YAAA,CAGUc,iBAAA;gBAFPM,IAAI,EAAEc,QAAA,CAAA6D,mBAAmB,CAACN,YAAY,CAACK,IAAI;gBAC5C1F,KAAK,EAAC;;cA7WxBY,CAAA;;YAyX6B0C,MAAM,EAAA3D,QAAA,CACrB,MAEM,CAFNmB,mBAAA,CAEM,OAFN8E,WAEM,EAAAnD,gBAAA,CADDX,QAAA,CAAA+D,UAAU,CAACR,YAAY,CAACS,IAAI,kB;YA3X/CpG,OAAA,EAAAC,QAAA,CAkXY,MAEoB,CAFpBC,YAAA,CAEoBsD,4BAAA;cAFD5D,KAAK,EAAC;YAAW;cAlXhDI,OAAA,EAAAC,QAAA,CAmXc,MAAwB,CAnXtCgB,gBAAA,CAAA8B,gBAAA,CAmXiB4C,YAAY,CAACU,KAAK,iB;cAnXnCnF,CAAA;0CAqXYhB,YAAA,CAEuBmE,+BAAA;cAFDzE,KAAK,EAAC;YAAW;cArXnDI,OAAA,EAAAC,QAAA,CAsXc,MAA0B,CAtXxCgB,gBAAA,CAAA8B,gBAAA,CAsXiB4C,YAAY,CAACW,OAAO,iB;cAtXrCpF,CAAA;;YAAAA,CAAA;;;QAAAA,CAAA;UAiYmBJ,KAAA,CAAA4E,aAAa,CAACa,MAAM,U,cAA/BhB,mBAAA,CAWM,OAXNiB,WAWM,GAVJtG,YAAA,CAKUc,iBAAA;QAJRM,IAAI,EAAC,cAAc;QACnBC,IAAI,EAAC,IAAI;QACTjB,KAAK,EAAC,gBAAgB;QACtBV,KAAK,EAAC;sCAERwB,mBAAA,CAAyD;QAApDxB,KAAK,EAAC;MAAmC,GAAC,MAAI,sB,4BACnDwB,mBAAA,CAEM;QAFDxB,KAAK,EAAC;MAAkC,GAAC,cAE9C,qB,KA3YVQ,mBAAA,e;MAAAc,CAAA;uCAgZId,mBAAA,WAAc,EACdF,YAAA,CAoCWuG,mBAAA;MArbf/E,UAAA,EAiZuBZ,KAAA,CAAAgD,QAAQ;MAjZ/B,uBAAAlD,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAiZuBC,KAAA,CAAAgD,QAAQ,GAAAjD,MAAA;MAAE,WAAS,EAAC;;MAjZ3Cb,OAAA,EAAAC,QAAA,CAkZM,MAkCS,CAlCTC,YAAA,CAkCSyC,iBAAA;QAlCD/C,KAAK,EAAC;MAAoB;QAlZxCI,OAAA,EAAAC,QAAA,CAmZQ,MAGe,CAHfC,YAAA,CAGewG,uBAAA;UAHD9G,KAAK,EAAC;QAAqB;UAnZjDI,OAAA,EAAAC,QAAA,CAoZU,MAA6E,CAA7EC,YAAA,CAA6Ec,iBAAA;YAArEM,IAAI,EAAC,iBAAiB;YAAChB,KAAK,EAAC,iBAAiB;YAACV,KAAK,EAAC;0CApZvEqB,gBAAA,CAoZuF,QAE/E,G;UAtZRC,CAAA;UAAAC,EAAA;YAuZQjB,YAAA,CAsBc0C,sBAAA;UA7atB5C,OAAA,EAAAC,QAAA,CAwZU,MAKM,C,4BALNmB,mBAAA,CAKM;YALDxB,KAAK,EAAC;UAAM,IACfwB,mBAAA,CAAkC;YAA9BxB,KAAK,EAAC;UAAc,GAAC,MAAI,GAC7BwB,mBAAA,CAEI;YAFDxB,KAAK,EAAC;UAAkC,GAAC,6BAE5C,E,sBAGFM,YAAA,CAaSmD,iBAAA;YAbDvB,OAAO,EAAC;UAAS;YA/ZnC9B,OAAA,EAAAC,QAAA,CAgaY,MAGc,CAHdC,YAAA,CAGcoD,sBAAA;cAHD,cAAY,EAAC;YAAuB;cAha7DtD,OAAA,EAAAC,QAAA,CAiac,MAA6C,CAA7CC,YAAA,CAA6CsD,4BAAA;gBAja3DxD,OAAA,EAAAC,QAAA,CAiaiC,MAAMW,MAAA,SAAAA,MAAA,QAjavCK,gBAAA,CAiaiC,QAAM,E;gBAjavCC,CAAA;gBAAAC,EAAA;kBAkacjB,YAAA,CAA6DmE,+BAAA;gBAla3ErE,OAAA,EAAAC,QAAA,CAkaoC,MAAgBW,MAAA,SAAAA,MAAA,QAlapDK,gBAAA,CAkaoC,kBAAgB,E;gBAlapDC,CAAA;gBAAAC,EAAA;;cAAAD,CAAA;gBAoaYhB,YAAA,CAGcoD,sBAAA;cAHD,cAAY,EAAC;YAA8B;cApapEtD,OAAA,EAAAC,QAAA,CAqac,MAA2C,CAA3CC,YAAA,CAA2CsD,4BAAA;gBArazDxD,OAAA,EAAAC,QAAA,CAqaiC,MAAIW,MAAA,SAAAA,MAAA,QArarCK,gBAAA,CAqaiC,MAAI,E;gBArarCC,CAAA;gBAAAC,EAAA;kBAsacjB,YAAA,CAAwDmE,+BAAA;gBAtatErE,OAAA,EAAAC,QAAA,CAsaoC,MAAWW,MAAA,SAAAA,MAAA,QAta/CK,gBAAA,CAsaoC,aAAW,E;gBAta/CC,CAAA;gBAAAC,EAAA;;cAAAD,CAAA;gBAwaYhB,YAAA,CAGcoD,sBAAA;cAHD,cAAY,EAAC;YAA4B;cAxalEtD,OAAA,EAAAC,QAAA,CAyac,MAA2C,CAA3CC,YAAA,CAA2CsD,4BAAA;gBAzazDxD,OAAA,EAAAC,QAAA,CAyaiC,MAAIW,MAAA,SAAAA,MAAA,QAzarCK,gBAAA,CAyaiC,MAAI,E;gBAzarCC,CAAA;gBAAAC,EAAA;kBA0acjB,YAAA,CAA2DmE,+BAAA;gBA1azErE,OAAA,EAAAC,QAAA,CA0aoC,MAAcW,MAAA,SAAAA,MAAA,QA1alDK,gBAAA,CA0aoC,gBAAc,E;gBA1alDC,CAAA;gBAAAC,EAAA;;cAAAD,CAAA;;YAAAA,CAAA;;UAAAA,CAAA;UAAAC,EAAA;YA8aQjB,YAAA,CAKiByG,yBAAA;UAnbzB3G,OAAA,EAAAC,QAAA,CA+aU,MAAqB,CAArBC,YAAA,CAAqBsB,mBAAA,GACrBtB,YAAA,CAEQ8B,gBAAA;YAFD1B,KAAK,EAAC,iBAAiB;YAAEK,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,KAAA,CAAAgD,QAAQ;;YAhbzD9D,OAAA,EAAAC,QAAA,CAgbmE,MAEzDW,MAAA,SAAAA,MAAA,QAlbVK,gBAAA,CAgbmE,OAEzD,E;YAlbVC,CAAA;YAAAC,EAAA;;UAAAD,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;uCAubId,mBAAA,UAAa,EACbF,YAAA,CAAmB0G,0BAAA,E;IAxbvB1F,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}