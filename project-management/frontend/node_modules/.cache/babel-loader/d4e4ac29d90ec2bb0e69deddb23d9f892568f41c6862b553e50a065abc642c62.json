{"ast": null, "code": "import { createApp } from 'vue';\nimport { createPinia } from 'pinia';\nimport App from './App.vue';\nimport router from './router';\nimport store from './store';\nimport vuetify from './plugins/vuetify';\nimport './assets/styles/main.css';\nimport './styles/medical-theme.css'; // 引入医疗主题样式\nimport './utils/dataInspector'; // 引入数据检查工具\n\n// 创建Vue应用实例\nconst app = createApp(App);\nconst pinia = createPinia();\n\n// 使用插件\napp.use(pinia);\napp.use(store);\napp.use(router);\napp.use(vuetify);\n\n// 初始化认证状态\nstore.dispatch('auth/initAuth');\n\n// 如果没有认证，自动登录一个演示用户\nif (!store.getters['auth/isAuthenticated']) {\n  store.dispatch('auth/login', {\n    username: 'demo'\n  });\n}\n\n// 挂载应用\napp.mount('#app');", "map": {"version": 3, "names": ["createApp", "createPinia", "App", "router", "store", "vuetify", "app", "pinia", "use", "dispatch", "getters", "username", "mount"], "sources": ["/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/技术测试/项目实施进度管理/project-management/frontend/src/main.js"], "sourcesContent": ["import { createApp } from 'vue'\nimport { createPinia } from 'pinia'\nimport App from './App.vue'\nimport router from './router'\nimport store from './store'\nimport vuetify from './plugins/vuetify'\nimport './assets/styles/main.css'\nimport './styles/medical-theme.css' // 引入医疗主题样式\nimport './utils/dataInspector' // 引入数据检查工具\n\n// 创建Vue应用实例\nconst app = createApp(App)\nconst pinia = createPinia()\n\n// 使用插件\napp.use(pinia)\napp.use(store)\napp.use(router)\napp.use(vuetify)\n\n// 初始化认证状态\nstore.dispatch('auth/initAuth')\n\n// 如果没有认证，自动登录一个演示用户\nif (!store.getters['auth/isAuthenticated']) {\n  store.dispatch('auth/login', { username: 'demo' })\n}\n\n// 挂载应用\napp.mount('#app')\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,KAAK;AAC/B,SAASC,WAAW,QAAQ,OAAO;AACnC,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,OAAO,MAAM,mBAAmB;AACvC,OAAO,0BAA0B;AACjC,OAAO,4BAA4B,EAAC;AACpC,OAAO,uBAAuB,EAAC;;AAE/B;AACA,MAAMC,GAAG,GAAGN,SAAS,CAACE,GAAG,CAAC;AAC1B,MAAMK,KAAK,GAAGN,WAAW,CAAC,CAAC;;AAE3B;AACAK,GAAG,CAACE,GAAG,CAACD,KAAK,CAAC;AACdD,GAAG,CAACE,GAAG,CAACJ,KAAK,CAAC;AACdE,GAAG,CAACE,GAAG,CAACL,MAAM,CAAC;AACfG,GAAG,CAACE,GAAG,CAACH,OAAO,CAAC;;AAEhB;AACAD,KAAK,CAACK,QAAQ,CAAC,eAAe,CAAC;;AAE/B;AACA,IAAI,CAACL,KAAK,CAACM,OAAO,CAAC,sBAAsB,CAAC,EAAE;EAC1CN,KAAK,CAACK,QAAQ,CAAC,YAAY,EAAE;IAAEE,QAAQ,EAAE;EAAO,CAAC,CAAC;AACpD;;AAEA;AACAL,GAAG,CAACM,KAAK,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}