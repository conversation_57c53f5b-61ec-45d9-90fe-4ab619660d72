{"ast": null, "code": "import axios from 'axios';\nimport store from '@/store';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: process.env.VUE_APP_API_BASE_URL || 'http://localhost:5000/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求拦截器\napi.interceptors.request.use(config => {\n  // 添加认证token\n  const token = store.getters['auth/token'];\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  if (error.response?.status === 401) {\n    // token过期，清除认证信息并跳转到登录页\n    store.dispatch('auth/logout');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\nexport default api;", "map": {"version": 3, "names": ["axios", "store", "api", "create", "baseURL", "process", "env", "VUE_APP_API_BASE_URL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "getters", "Authorization", "error", "Promise", "reject", "response", "status", "dispatch", "window", "location", "href"], "sources": ["/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/技术测试/项目实施进度管理/project-management/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios'\nimport store from '@/store'\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: process.env.VUE_APP_API_BASE_URL || 'http://localhost:5000/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n})\n\n// 请求拦截器\napi.interceptors.request.use(\n  config => {\n    // 添加认证token\n    const token = store.getters['auth/token']\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`\n    }\n    return config\n  },\n  error => {\n    return Promise.reject(error)\n  }\n)\n\n// 响应拦截器\napi.interceptors.response.use(\n  response => {\n    return response\n  },\n  error => {\n    if (error.response?.status === 401) {\n      // token过期，清除认证信息并跳转到登录页\n      store.dispatch('auth/logout')\n      window.location.href = '/login'\n    }\n    return Promise.reject(error)\n  }\n)\n\nexport default api\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,SAAS;;AAE3B;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,oBAAoB,IAAI,2BAA2B;EACxEC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAP,GAAG,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CAC1BC,MAAM,IAAI;EACR;EACA,MAAMC,KAAK,GAAGb,KAAK,CAACc,OAAO,CAAC,YAAY,CAAC;EACzC,IAAID,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACO,aAAa,GAAG,UAAUF,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACDI,KAAK,IAAI;EACP,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAf,GAAG,CAACQ,YAAY,CAACU,QAAQ,CAACR,GAAG,CAC3BQ,QAAQ,IAAI;EACV,OAAOA,QAAQ;AACjB,CAAC,EACDH,KAAK,IAAI;EACP,IAAIA,KAAK,CAACG,QAAQ,EAAEC,MAAM,KAAK,GAAG,EAAE;IAClC;IACApB,KAAK,CAACqB,QAAQ,CAAC,aAAa,CAAC;IAC7BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOP,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAef,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}