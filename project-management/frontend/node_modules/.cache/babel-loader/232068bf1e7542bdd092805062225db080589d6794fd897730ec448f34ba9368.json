{"ast": null, "code": "import NotificationBar from '@/components/common/NotificationBar.vue';\nexport default {\n  name: 'App',\n  components: {\n    NotificationBar\n  },\n  data() {\n    return {\n      drawer: true,\n      searchQuery: '',\n      showNotifications: false,\n      showHelp: false,\n      // 模拟通知数据\n      notifications: [{\n        id: 1,\n        type: 'task',\n        title: '任务即将到期',\n        message: '医院A系统集成任务将在2小时后到期',\n        time: new Date(Date.now() - 1000 * 60 * 30) // 30分钟前\n      }, {\n        id: 2,\n        type: 'project',\n        title: '项目状态更新',\n        message: '医院B数据迁移项目已完成第二阶段',\n        time: new Date(Date.now() - 1000 * 60 * 60 * 2) // 2小时前\n      }, {\n        id: 3,\n        type: 'meeting',\n        title: '会议提醒',\n        message: '项目评审会议将在明天上午10:00开始',\n        time: new Date(Date.now() - 1000 * 60 * 60 * 4) // 4小时前\n      }]\n    };\n  },\n  computed: {\n    currentUser() {\n      return this.$store.getters['auth/user'] || {};\n    },\n    notificationCount() {\n      return this.notifications.length;\n    }\n  },\n  methods: {\n    navigateTo(route) {\n      this.$router.push(route);\n    },\n    logout() {\n      // 清除认证信息\n      this.$store.dispatch('auth/logout');\n      // 重定向到登录页\n      this.$router.push('/login');\n    },\n    handleNotificationClick(notification) {\n      // 根据通知类型跳转到相应页面\n      switch (notification.type) {\n        case 'task':\n          this.$router.push('/tasks');\n          break;\n        case 'project':\n          this.$router.push('/projects');\n          break;\n        case 'meeting':\n          this.$router.push('/meetings');\n          break;\n      }\n      this.showNotifications = false;\n    },\n    getNotificationColor(type) {\n      const colors = {\n        'task': 'medical-warning',\n        'project': 'medical-primary',\n        'meeting': 'medical-info',\n        'risk': 'medical-error'\n      };\n      return colors[type] || 'grey';\n    },\n    getNotificationIcon(type) {\n      const icons = {\n        'task': 'mdi-clipboard-check',\n        'project': 'mdi-hospital-building',\n        'meeting': 'mdi-calendar',\n        'risk': 'mdi-alert-circle'\n      };\n      return icons[type] || 'mdi-information';\n    },\n    formatTime(time) {\n      const now = new Date();\n      const diff = now - time;\n      const minutes = Math.floor(diff / (1000 * 60));\n      const hours = Math.floor(diff / (1000 * 60 * 60));\n      const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n      if (minutes < 60) {\n        return `${minutes}分钟前`;\n      } else if (hours < 24) {\n        return `${hours}小时前`;\n      } else {\n        return `${days}天前`;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["NotificationBar", "name", "components", "data", "drawer", "searchQuery", "showNotifications", "showHelp", "notifications", "id", "type", "title", "message", "time", "Date", "now", "computed", "currentUser", "$store", "getters", "notificationCount", "length", "methods", "navigateTo", "route", "$router", "push", "logout", "dispatch", "handleNotificationClick", "notification", "getNotificationColor", "colors", "getNotificationIcon", "icons", "formatTime", "diff", "minutes", "Math", "floor", "hours", "days"], "sources": ["/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/技术测试/项目实施进度管理/project-management/frontend/src/App.vue"], "sourcesContent": ["<template>\n  <v-app class=\"medical-app\">\n    <v-layout class=\"google-layout\">\n      <!-- 顶部导航栏 - Google风格 -->\n      <v-app-bar\n        color=\"white\"\n        app\n        flat\n        class=\"google-header\"\n        elevation=\"1\"\n      >\n        <v-app-bar-nav-icon\n          @click=\"drawer = !drawer\"\n          class=\"google-menu-icon\"\n        >\n          <v-icon color=\"grey-darken-1\">mdi-menu</v-icon>\n        </v-app-bar-nav-icon>\n\n        <!-- 医疗项目管理系统标题 -->\n        <div class=\"d-flex align-center\">\n          <v-icon\n            icon=\"mdi-hospital-box\"\n            color=\"medical-primary\"\n            size=\"32\"\n            class=\"me-3\"\n          ></v-icon>\n          <div>\n            <div class=\"google-title\">医疗项目管理</div>\n            <div class=\"google-subtitle\">Healthcare Project Management</div>\n          </div>\n        </div>\n\n        <v-spacer></v-spacer>\n\n        <!-- 搜索框 - Google风格 -->\n        <v-text-field\n          v-model=\"searchQuery\"\n          placeholder=\"搜索项目、任务或文档...\"\n          prepend-inner-icon=\"mdi-magnify\"\n          variant=\"outlined\"\n          density=\"compact\"\n          hide-details\n          class=\"google-search me-4\"\n          style=\"max-width: 400px;\"\n        ></v-text-field>\n\n        <!-- 通知按钮 -->\n        <v-btn\n          icon\n          variant=\"text\"\n          class=\"google-icon-btn me-2\"\n          @click=\"showNotifications = true\"\n        >\n          <v-badge\n            :content=\"notificationCount\"\n            :model-value=\"notificationCount > 0\"\n            color=\"medical-accent\"\n          >\n            <v-icon color=\"grey-darken-1\">mdi-bell-outline</v-icon>\n          </v-badge>\n        </v-btn>\n\n        <!-- 用户菜单 -->\n        <v-menu offset-y>\n          <template v-slot:activator=\"{ props }\">\n            <v-btn\n              icon\n              variant=\"text\"\n              class=\"google-icon-btn\"\n              v-bind=\"props\"\n            >\n              <v-avatar size=\"32\" color=\"medical-primary\">\n                <v-icon color=\"white\">mdi-account</v-icon>\n              </v-avatar>\n            </v-btn>\n          </template>\n          <v-card class=\"google-menu\" min-width=\"280\">\n            <v-card-text class=\"pa-4\">\n              <div class=\"d-flex align-center mb-3\">\n                <v-avatar size=\"48\" color=\"medical-primary\" class=\"me-3\">\n                  <v-icon color=\"white\" size=\"24\">mdi-account</v-icon>\n                </v-avatar>\n                <div>\n                  <div class=\"text-h6\">{{ currentUser.name || '用户' }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">{{ currentUser.email || '<EMAIL>' }}</div>\n                </div>\n              </div>\n              <v-divider class=\"mb-3\"></v-divider>\n              <v-list density=\"compact\" class=\"pa-0\">\n                <v-list-item\n                  @click=\"navigateTo('/profile')\"\n                  prepend-icon=\"mdi-account-circle\"\n                  class=\"google-menu-item\"\n                >\n                  <v-list-item-title>个人资料</v-list-item-title>\n                </v-list-item>\n                <v-list-item\n                  @click=\"navigateTo('/settings')\"\n                  prepend-icon=\"mdi-cog\"\n                  class=\"google-menu-item\"\n                >\n                  <v-list-item-title>系统设置</v-list-item-title>\n                </v-list-item>\n                <v-divider class=\"my-2\"></v-divider>\n                <v-list-item\n                  @click=\"logout\"\n                  prepend-icon=\"mdi-logout\"\n                  class=\"google-menu-item\"\n                >\n                  <v-list-item-title>退出登录</v-list-item-title>\n                </v-list-item>\n              </v-list>\n            </v-card-text>\n          </v-card>\n        </v-menu>\n      </v-app-bar>\n\n      <!-- 侧边导航菜单 - Google风格 -->\n      <v-navigation-drawer\n        v-model=\"drawer\"\n        app\n        class=\"google-drawer\"\n        width=\"280\"\n      >\n        <!-- 导航头部 -->\n        <div class=\"google-drawer-header\">\n          <div class=\"d-flex align-center pa-4\">\n            <v-icon\n              icon=\"mdi-hospital-box\"\n              color=\"medical-primary\"\n              size=\"24\"\n              class=\"me-3\"\n            ></v-icon>\n            <div>\n              <div class=\"text-subtitle-1 font-weight-medium\">医疗项目管理</div>\n              <div class=\"text-caption text-medium-emphasis\">Healthcare PM</div>\n            </div>\n          </div>\n        </div>\n\n        <v-divider></v-divider>\n\n        <!-- 主要导航 -->\n        <v-list class=\"google-nav-list\" nav>\n          <!-- 概览部分 -->\n          <v-list-subheader class=\"google-nav-subheader\">\n            <v-icon icon=\"mdi-view-dashboard\" size=\"16\" class=\"me-2\"></v-icon>\n            概览\n          </v-list-subheader>\n\n          <v-list-item\n            to=\"/\"\n            prepend-icon=\"mdi-view-dashboard\"\n            class=\"google-nav-item\"\n            color=\"medical-primary\"\n          >\n            <v-list-item-title>仪表盘</v-list-item-title>\n            <template v-slot:append>\n              <v-chip size=\"x-small\" color=\"medical-accent\" variant=\"flat\">NEW</v-chip>\n            </template>\n          </v-list-item>\n\n          <!-- 项目管理部分 -->\n          <v-list-subheader class=\"google-nav-subheader\">\n            <v-icon icon=\"mdi-hospital-building\" size=\"16\" class=\"me-2\"></v-icon>\n            医疗项目\n          </v-list-subheader>\n\n          <v-list-item\n            to=\"/projects\"\n            prepend-icon=\"mdi-hospital-building\"\n            class=\"google-nav-item\"\n            color=\"medical-primary\"\n          >\n            <v-list-item-title>项目管理</v-list-item-title>\n          </v-list-item>\n\n          <v-list-item\n            to=\"/gantt\"\n            prepend-icon=\"mdi-chart-timeline-variant\"\n            class=\"google-nav-item\"\n            color=\"medical-primary\"\n          >\n            <v-list-item-title>项目进度</v-list-item-title>\n            <v-list-item-subtitle>甘特图</v-list-item-subtitle>\n          </v-list-item>\n\n          <v-list-item\n            to=\"/modern-kanban\"\n            prepend-icon=\"mdi-view-column\"\n            class=\"google-nav-item\"\n            color=\"medical-primary\"\n          >\n            <v-list-item-title>项目看板</v-list-item-title>\n            <v-list-item-subtitle>敏捷管理</v-list-item-subtitle>\n          </v-list-item>\n\n          <!-- 任务与协作 -->\n          <v-list-subheader class=\"google-nav-subheader\">\n            <v-icon icon=\"mdi-account-group\" size=\"16\" class=\"me-2\"></v-icon>\n            任务协作\n          </v-list-subheader>\n\n          <v-list-item\n            to=\"/tasks\"\n            prepend-icon=\"mdi-clipboard-check-multiple\"\n            class=\"google-nav-item\"\n            color=\"medical-primary\"\n          >\n            <v-list-item-title>任务管理</v-list-item-title>\n          </v-list-item>\n\n          <v-list-item\n            to=\"/meetings\"\n            prepend-icon=\"mdi-video-account\"\n            class=\"google-nav-item\"\n            color=\"medical-primary\"\n          >\n            <v-list-item-title>会议记录</v-list-item-title>\n            <v-list-item-subtitle>团队协作</v-list-item-subtitle>\n          </v-list-item>\n\n          <!-- 质量与风险 -->\n          <v-list-subheader class=\"google-nav-subheader\">\n            <v-icon icon=\"mdi-shield-check\" size=\"16\" class=\"me-2\"></v-icon>\n            质量控制\n          </v-list-subheader>\n\n          <v-list-item\n            to=\"/risks\"\n            prepend-icon=\"mdi-alert-circle\"\n            class=\"google-nav-item\"\n            color=\"medical-primary\"\n          >\n            <v-list-item-title>风险管理</v-list-item-title>\n            <v-list-item-subtitle>质量保障</v-list-item-subtitle>\n          </v-list-item>\n\n          <!-- 分析与报告 -->\n          <v-list-subheader class=\"google-nav-subheader\">\n            <v-icon icon=\"mdi-chart-line\" size=\"16\" class=\"me-2\"></v-icon>\n            数据分析\n          </v-list-subheader>\n\n          <v-list-item\n            to=\"/reports\"\n            prepend-icon=\"mdi-chart-bar\"\n            class=\"google-nav-item\"\n            color=\"medical-primary\"\n          >\n            <v-list-item-title>报表分析</v-list-item-title>\n            <v-list-item-subtitle>数据洞察</v-list-item-subtitle>\n          </v-list-item>\n        </v-list>\n\n        <!-- 底部快捷操作 -->\n        <template v-slot:append>\n          <div class=\"google-drawer-footer\">\n            <v-divider class=\"mb-2\"></v-divider>\n            <v-list density=\"compact\">\n              <v-list-item\n                prepend-icon=\"mdi-help-circle\"\n                class=\"google-nav-item\"\n                @click=\"showHelp = true\"\n              >\n                <v-list-item-title>帮助中心</v-list-item-title>\n              </v-list-item>\n              <v-list-item\n                prepend-icon=\"mdi-cog\"\n                class=\"google-nav-item\"\n                to=\"/settings\"\n              >\n                <v-list-item-title>系统设置</v-list-item-title>\n              </v-list-item>\n            </v-list>\n          </div>\n        </template>\n      </v-navigation-drawer>\n\n      <!-- 主内容区域 - Google风格 -->\n      <v-main class=\"google-main\">\n        <div class=\"google-content\">\n          <router-view></router-view>\n        </div>\n      </v-main>\n\n      <!-- 页脚 - Google风格 -->\n      <v-footer\n        app\n        class=\"google-footer\"\n        color=\"white\"\n        elevation=\"1\"\n      >\n        <div class=\"d-flex align-center justify-space-between w-100 px-4\">\n          <div class=\"d-flex align-center\">\n            <v-icon\n              icon=\"mdi-hospital-box\"\n              color=\"medical-primary\"\n              size=\"20\"\n              class=\"me-2\"\n            ></v-icon>\n            <span class=\"text-body-2 text-medium-emphasis\">\n              &copy; {{ new Date().getFullYear() }} 医疗项目管理系统 - Healthcare Project Management\n            </span>\n          </div>\n          <div class=\"d-flex align-center\">\n            <v-chip\n              size=\"small\"\n              color=\"medical-success\"\n              variant=\"flat\"\n              class=\"me-2\"\n            >\n              <v-icon start icon=\"mdi-check-circle\"></v-icon>\n              系统正常\n            </v-chip>\n            <span class=\"text-caption text-medium-emphasis\">\n              版本 v2.1.0\n            </span>\n          </div>\n        </div>\n      </v-footer>\n    </v-layout>\n\n    <!-- 通知面板 - Google风格 -->\n    <v-navigation-drawer\n      v-model=\"showNotifications\"\n      location=\"right\"\n      temporary\n      width=\"400\"\n      class=\"google-notification-panel\"\n    >\n      <div class=\"google-notification-header\">\n        <div class=\"d-flex align-center justify-space-between pa-4\">\n          <div class=\"d-flex align-center\">\n            <v-icon icon=\"mdi-bell\" color=\"medical-primary\" class=\"me-2\"></v-icon>\n            <span class=\"text-h6\">通知中心</span>\n          </div>\n          <v-btn\n            icon\n            variant=\"text\"\n            size=\"small\"\n            @click=\"showNotifications = false\"\n          >\n            <v-icon>mdi-close</v-icon>\n          </v-btn>\n        </div>\n      </div>\n\n      <v-divider></v-divider>\n\n      <div class=\"google-notification-content\">\n        <v-list>\n          <v-list-item\n            v-for=\"notification in notifications\"\n            :key=\"notification.id\"\n            class=\"google-notification-item\"\n            @click=\"handleNotificationClick(notification)\"\n          >\n            <template v-slot:prepend>\n              <v-avatar\n                :color=\"getNotificationColor(notification.type)\"\n                size=\"40\"\n              >\n                <v-icon\n                  :icon=\"getNotificationIcon(notification.type)\"\n                  color=\"white\"\n                ></v-icon>\n              </v-avatar>\n            </template>\n\n            <v-list-item-title class=\"text-wrap\">\n              {{ notification.title }}\n            </v-list-item-title>\n            <v-list-item-subtitle class=\"text-wrap\">\n              {{ notification.message }}\n            </v-list-item-subtitle>\n\n            <template v-slot:append>\n              <div class=\"text-caption text-medium-emphasis\">\n                {{ formatTime(notification.time) }}\n              </div>\n            </template>\n          </v-list-item>\n        </v-list>\n\n        <div v-if=\"notifications.length === 0\" class=\"text-center pa-8\">\n          <v-icon\n            icon=\"mdi-bell-off\"\n            size=\"64\"\n            color=\"grey-lighten-2\"\n            class=\"mb-4\"\n          ></v-icon>\n          <div class=\"text-h6 text-medium-emphasis mb-2\">暂无通知</div>\n          <div class=\"text-body-2 text-medium-emphasis\">\n            所有通知都已处理完毕\n          </div>\n        </div>\n      </div>\n    </v-navigation-drawer>\n\n    <!-- 帮助对话框 -->\n    <v-dialog v-model=\"showHelp\" max-width=\"600\">\n      <v-card class=\"google-help-dialog\">\n        <v-card-title class=\"d-flex align-center\">\n          <v-icon icon=\"mdi-help-circle\" color=\"medical-primary\" class=\"me-2\"></v-icon>\n          帮助中心\n        </v-card-title>\n        <v-card-text>\n          <div class=\"mb-4\">\n            <h3 class=\"text-h6 mb-2\">快速入门</h3>\n            <p class=\"text-body-2 text-medium-emphasis\">\n              欢迎使用医疗项目管理系统！这里是一些快速入门指南：\n            </p>\n          </div>\n\n          <v-list density=\"compact\">\n            <v-list-item prepend-icon=\"mdi-hospital-building\">\n              <v-list-item-title>创建医疗项目</v-list-item-title>\n              <v-list-item-subtitle>在项目管理中创建新的医疗实施项目</v-list-item-subtitle>\n            </v-list-item>\n            <v-list-item prepend-icon=\"mdi-clipboard-check-multiple\">\n              <v-list-item-title>管理任务</v-list-item-title>\n              <v-list-item-subtitle>分配和跟踪项目任务进度</v-list-item-subtitle>\n            </v-list-item>\n            <v-list-item prepend-icon=\"mdi-chart-timeline-variant\">\n              <v-list-item-title>查看进度</v-list-item-title>\n              <v-list-item-subtitle>使用甘特图和看板查看项目进度</v-list-item-subtitle>\n            </v-list-item>\n          </v-list>\n        </v-card-text>\n        <v-card-actions>\n          <v-spacer></v-spacer>\n          <v-btn color=\"medical-primary\" @click=\"showHelp = false\">\n            了解了\n          </v-btn>\n        </v-card-actions>\n      </v-card>\n    </v-dialog>\n\n    <!-- 通知组件 -->\n    <NotificationBar />\n  </v-app>\n</template>\n\n<script>\nimport NotificationBar from '@/components/common/NotificationBar.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    NotificationBar\n  },\n  data() {\n    return {\n      drawer: true,\n      searchQuery: '',\n      showNotifications: false,\n      showHelp: false,\n\n      // 模拟通知数据\n      notifications: [\n        {\n          id: 1,\n          type: 'task',\n          title: '任务即将到期',\n          message: '医院A系统集成任务将在2小时后到期',\n          time: new Date(Date.now() - 1000 * 60 * 30) // 30分钟前\n        },\n        {\n          id: 2,\n          type: 'project',\n          title: '项目状态更新',\n          message: '医院B数据迁移项目已完成第二阶段',\n          time: new Date(Date.now() - 1000 * 60 * 60 * 2) // 2小时前\n        },\n        {\n          id: 3,\n          type: 'meeting',\n          title: '会议提醒',\n          message: '项目评审会议将在明天上午10:00开始',\n          time: new Date(Date.now() - 1000 * 60 * 60 * 4) // 4小时前\n        }\n      ]\n    }\n  },\n\n  computed: {\n    currentUser() {\n      return this.$store.getters['auth/user'] || {}\n    },\n\n    notificationCount() {\n      return this.notifications.length\n    }\n  },\n\n  methods: {\n    navigateTo(route) {\n      this.$router.push(route)\n    },\n\n    logout() {\n      // 清除认证信息\n      this.$store.dispatch('auth/logout')\n      // 重定向到登录页\n      this.$router.push('/login')\n    },\n\n    handleNotificationClick(notification) {\n      // 根据通知类型跳转到相应页面\n      switch (notification.type) {\n        case 'task':\n          this.$router.push('/tasks')\n          break\n        case 'project':\n          this.$router.push('/projects')\n          break\n        case 'meeting':\n          this.$router.push('/meetings')\n          break\n      }\n      this.showNotifications = false\n    },\n\n    getNotificationColor(type) {\n      const colors = {\n        'task': 'medical-warning',\n        'project': 'medical-primary',\n        'meeting': 'medical-info',\n        'risk': 'medical-error'\n      }\n      return colors[type] || 'grey'\n    },\n\n    getNotificationIcon(type) {\n      const icons = {\n        'task': 'mdi-clipboard-check',\n        'project': 'mdi-hospital-building',\n        'meeting': 'mdi-calendar',\n        'risk': 'mdi-alert-circle'\n      }\n      return icons[type] || 'mdi-information'\n    },\n\n    formatTime(time) {\n      const now = new Date()\n      const diff = now - time\n      const minutes = Math.floor(diff / (1000 * 60))\n      const hours = Math.floor(diff / (1000 * 60 * 60))\n      const days = Math.floor(diff / (1000 * 60 * 60 * 24))\n\n      if (minutes < 60) {\n        return `${minutes}分钟前`\n      } else if (hours < 24) {\n        return `${hours}小时前`\n      } else {\n        return `${days}天前`\n      }\n    }\n  }\n}\n</script>\n\n<style>\n/* 医疗项目管理系统 - Google风格样式 */\n\n/* CSS变量定义 - 医疗主题色彩 */\n:root {\n  /* 医疗主题色彩 */\n  --medical-primary: #1565C0;        /* 医疗蓝 */\n  --medical-secondary: #2E7D32;      /* 医疗绿 */\n  --medical-accent: #E53935;         /* 医疗红 */\n  --medical-warning: #F57C00;        /* 医疗橙 */\n  --medical-info: #1976D2;           /* 信息蓝 */\n  --medical-success: #388E3C;        /* 成功绿 */\n  --medical-error: #D32F2F;          /* 错误红 */\n\n  /* Google风格色彩 */\n  --google-blue: #4285F4;\n  --google-red: #EA4335;\n  --google-yellow: #FBBC04;\n  --google-green: #34A853;\n  --google-grey-50: #FAFAFA;\n  --google-grey-100: #F5F5F5;\n  --google-grey-200: #EEEEEE;\n  --google-grey-300: #E0E0E0;\n  --google-grey-400: #BDBDBD;\n  --google-grey-500: #9E9E9E;\n  --google-grey-600: #757575;\n  --google-grey-700: #616161;\n  --google-grey-800: #424242;\n  --google-grey-900: #212121;\n\n  /* 阴影定义 */\n  --google-shadow-1: 0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15);\n  --google-shadow-2: 0 1px 2px 0 rgba(60,64,67,.3), 0 2px 6px 2px rgba(60,64,67,.15);\n  --google-shadow-3: 0 4px 8px 3px rgba(60,64,67,.15), 0 1px 3px rgba(60,64,67,.3);\n  --google-shadow-4: 0 6px 10px 4px rgba(60,64,67,.15), 0 2px 3px rgba(60,64,67,.3);\n\n  /* 圆角定义 */\n  --google-radius-small: 4px;\n  --google-radius-medium: 8px;\n  --google-radius-large: 12px;\n  --google-radius-xl: 16px;\n}\n\n/* 全局样式重置 */\n* {\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: 'Google Sans', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;\n  margin: 0;\n  padding: 0;\n  background-color: var(--google-grey-50);\n  color: var(--google-grey-800);\n  line-height: 1.5;\n}\n\n/* 医疗应用主容器 */\n.medical-app {\n  background-color: var(--google-grey-50);\n}\n\n.google-layout {\n  min-height: 100vh;\n}\n\n/* Google风格头部导航 */\n.google-header {\n  border-bottom: 1px solid var(--google-grey-200);\n  backdrop-filter: blur(10px);\n  background-color: rgba(255, 255, 255, 0.95) !important;\n}\n\n.google-menu-icon {\n  border-radius: var(--google-radius-medium);\n  transition: background-color 0.2s ease;\n}\n\n.google-menu-icon:hover {\n  background-color: var(--google-grey-100);\n}\n\n.google-title {\n  font-size: 1.375rem;\n  font-weight: 500;\n  color: var(--medical-primary);\n  line-height: 1.2;\n}\n\n.google-subtitle {\n  font-size: 0.75rem;\n  color: var(--google-grey-600);\n  font-weight: 400;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n/* Google风格搜索框 */\n.google-search .v-field {\n  border-radius: var(--google-radius-large) !important;\n  background-color: var(--google-grey-100);\n  border: none !important;\n  box-shadow: none !important;\n}\n\n.google-search .v-field:hover {\n  background-color: var(--google-grey-200);\n  box-shadow: var(--google-shadow-1) !important;\n}\n\n.google-search .v-field--focused {\n  background-color: white !important;\n  box-shadow: var(--google-shadow-2) !important;\n}\n\n/* Google风格图标按钮 */\n.google-icon-btn {\n  border-radius: var(--google-radius-medium) !important;\n  transition: all 0.2s ease !important;\n}\n\n.google-icon-btn:hover {\n  background-color: var(--google-grey-100) !important;\n}\n\n/* Google风格菜单 */\n.google-menu {\n  border-radius: var(--google-radius-medium) !important;\n  box-shadow: var(--google-shadow-3) !important;\n  border: 1px solid var(--google-grey-200);\n}\n\n.google-menu-item {\n  border-radius: var(--google-radius-small) !important;\n  margin: 2px 0 !important;\n  transition: background-color 0.2s ease !important;\n}\n\n.google-menu-item:hover {\n  background-color: var(--google-grey-100) !important;\n}\n\n/* Google风格侧边导航 */\n.google-drawer {\n  border-right: 1px solid var(--google-grey-200) !important;\n  background-color: white !important;\n}\n\n.google-drawer-header {\n  background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-info) 100%);\n  color: white;\n}\n\n.google-drawer-header .text-subtitle-1 {\n  color: white !important;\n}\n\n.google-drawer-header .text-caption {\n  color: rgba(255, 255, 255, 0.8) !important;\n}\n\n.google-nav-list {\n  padding: 8px !important;\n}\n\n.google-nav-subheader {\n  font-size: 0.75rem !important;\n  font-weight: 600 !important;\n  color: var(--google-grey-600) !important;\n  text-transform: uppercase !important;\n  letter-spacing: 0.5px !important;\n  padding: 16px 16px 8px 16px !important;\n  margin-top: 8px !important;\n}\n\n.google-nav-item {\n  border-radius: var(--google-radius-medium) !important;\n  margin: 2px 0 !important;\n  transition: all 0.2s ease !important;\n  font-weight: 500 !important;\n}\n\n.google-nav-item:hover {\n  background-color: rgba(21, 101, 192, 0.08) !important;\n  transform: translateX(2px);\n}\n\n.google-nav-item.v-list-item--active {\n  background-color: rgba(21, 101, 192, 0.12) !important;\n  color: var(--medical-primary) !important;\n}\n\n.google-nav-item .v-list-item-subtitle {\n  font-size: 0.75rem !important;\n  color: var(--google-grey-500) !important;\n}\n\n.google-drawer-footer {\n  padding: 8px;\n  background-color: var(--google-grey-50);\n}\n\n/* Google风格主内容区域 */\n.google-main {\n  background-color: var(--google-grey-50) !important;\n}\n\n.google-content {\n  padding: 24px;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n\n/* Google风格页脚 */\n.google-footer {\n  border-top: 1px solid var(--google-grey-200) !important;\n  background-color: white !important;\n  min-height: 48px !important;\n}\n\n/* Google风格通知面板 */\n.google-notification-panel {\n  background-color: white !important;\n  box-shadow: var(--google-shadow-4) !important;\n}\n\n.google-notification-header {\n  background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-info) 100%);\n  color: white;\n}\n\n.google-notification-header .text-h6 {\n  color: white !important;\n}\n\n.google-notification-content {\n  max-height: calc(100vh - 120px);\n  overflow-y: auto;\n}\n\n.google-notification-item {\n  border-radius: var(--google-radius-medium) !important;\n  margin: 4px 8px !important;\n  transition: all 0.2s ease !important;\n  cursor: pointer !important;\n}\n\n.google-notification-item:hover {\n  background-color: var(--google-grey-100) !important;\n  transform: translateX(2px);\n}\n\n/* Google风格帮助对话框 */\n.google-help-dialog {\n  border-radius: var(--google-radius-large) !important;\n  box-shadow: var(--google-shadow-4) !important;\n}\n\n/* 医疗主题状态徽章 */\n.medical-status-badge {\n  border-radius: var(--google-radius-large);\n  padding: 4px 12px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.medical-status-badge.completed {\n  background-color: var(--medical-success);\n  color: white;\n}\n\n.medical-status-badge.in-progress {\n  background-color: var(--medical-info);\n  color: white;\n}\n\n.medical-status-badge.pending {\n  background-color: var(--medical-warning);\n  color: white;\n}\n\n.medical-status-badge.delayed {\n  background-color: var(--medical-error);\n  color: white;\n}\n\n.medical-status-badge.approved {\n  background-color: var(--medical-success);\n  color: white;\n}\n\n.medical-status-badge.rejected {\n  background-color: var(--medical-error);\n  color: white;\n}\n\n/* 医疗主题卡片 */\n.medical-card {\n  border-radius: var(--google-radius-large) !important;\n  box-shadow: var(--google-shadow-1) !important;\n  border: 1px solid var(--google-grey-200) !important;\n  transition: all 0.3s ease !important;\n}\n\n.medical-card:hover {\n  box-shadow: var(--google-shadow-2) !important;\n  transform: translateY(-2px);\n}\n\n.medical-card-header {\n  background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-info) 100%);\n  color: white;\n  border-radius: var(--google-radius-large) var(--google-radius-large) 0 0 !important;\n}\n\n/* 医疗主题按钮 */\n.medical-btn-primary {\n  background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-info) 100%) !important;\n  color: white !important;\n  border-radius: var(--google-radius-medium) !important;\n  font-weight: 600 !important;\n  text-transform: none !important;\n  box-shadow: var(--google-shadow-1) !important;\n  transition: all 0.2s ease !important;\n}\n\n.medical-btn-primary:hover {\n  box-shadow: var(--google-shadow-2) !important;\n  transform: translateY(-1px);\n}\n\n.medical-btn-secondary {\n  background-color: var(--medical-secondary) !important;\n  color: white !important;\n  border-radius: var(--google-radius-medium) !important;\n  font-weight: 600 !important;\n  text-transform: none !important;\n}\n\n/* 医疗主题图标 */\n.medical-icon {\n  color: var(--medical-primary);\n  filter: drop-shadow(0 1px 2px rgba(21, 101, 192, 0.2));\n}\n\n.medical-icon-success {\n  color: var(--medical-success);\n}\n\n.medical-icon-warning {\n  color: var(--medical-warning);\n}\n\n.medical-icon-error {\n  color: var(--medical-error);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .google-content {\n    padding: 16px;\n  }\n\n  .google-drawer {\n    width: 280px !important;\n  }\n\n  .google-search {\n    display: none !important;\n  }\n\n  .google-title {\n    font-size: 1.125rem;\n  }\n\n  .google-subtitle {\n    display: none;\n  }\n}\n\n@media (max-width: 480px) {\n  .google-content {\n    padding: 12px;\n  }\n\n  .google-notification-panel {\n    width: 100vw !important;\n  }\n}\n\n/* 滚动条样式 */\n::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: var(--google-grey-100);\n  border-radius: var(--google-radius-small);\n}\n\n::-webkit-scrollbar-thumb {\n  background: var(--google-grey-400);\n  border-radius: var(--google-radius-small);\n  transition: background-color 0.2s ease;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: var(--google-grey-500);\n}\n\n/* 动画效果 */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes slideInRight {\n  from {\n    opacity: 0;\n    transform: translateX(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n.fade-in-up {\n  animation: fadeInUp 0.3s ease-out;\n}\n\n.slide-in-right {\n  animation: slideInRight 0.3s ease-out;\n}\n\n/* 医疗主题渐变背景 */\n.medical-gradient-bg {\n  background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-info) 50%, var(--medical-secondary) 100%);\n}\n\n.medical-gradient-text {\n  background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-info) 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  font-weight: 600;\n}\n</style>\n"], "mappings": "AA6bA,OAAOA,eAAc,MAAO,yCAAwC;AAEpE,eAAe;EACbC,IAAI,EAAE,KAAK;EACXC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,MAAM,EAAE,IAAI;MACZC,WAAW,EAAE,EAAE;MACfC,iBAAiB,EAAE,KAAK;MACxBC,QAAQ,EAAE,KAAK;MAEf;MACAC,aAAa,EAAE,CACb;QACEC,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,QAAQ;QACfC,OAAO,EAAE,mBAAmB;QAC5BC,IAAI,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,IAAI,IAAG,GAAI,EAAC,GAAI,EAAE,EAAE;MAC9C,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,QAAQ;QACfC,OAAO,EAAE,kBAAkB;QAC3BC,IAAI,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,IAAI,IAAG,GAAI,EAAC,GAAI,EAAC,GAAI,CAAC,EAAE;MAClD,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,OAAO,EAAE,qBAAqB;QAC9BC,IAAI,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,IAAI,IAAG,GAAI,EAAC,GAAI,EAAC,GAAI,CAAC,EAAE;MAClD;IAEJ;EACF,CAAC;EAEDC,QAAQ,EAAE;IACRC,WAAWA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,WAAW,KAAK,CAAC;IAC9C,CAAC;IAEDC,iBAAiBA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACZ,aAAa,CAACa,MAAK;IACjC;EACF,CAAC;EAEDC,OAAO,EAAE;IACPC,UAAUA,CAACC,KAAK,EAAE;MAChB,IAAI,CAACC,OAAO,CAACC,IAAI,CAACF,KAAK;IACzB,CAAC;IAEDG,MAAMA,CAAA,EAAG;MACP;MACA,IAAI,CAACT,MAAM,CAACU,QAAQ,CAAC,aAAa;MAClC;MACA,IAAI,CAACH,OAAO,CAACC,IAAI,CAAC,QAAQ;IAC5B,CAAC;IAEDG,uBAAuBA,CAACC,YAAY,EAAE;MACpC;MACA,QAAQA,YAAY,CAACpB,IAAI;QACvB,KAAK,MAAM;UACT,IAAI,CAACe,OAAO,CAACC,IAAI,CAAC,QAAQ;UAC1B;QACF,KAAK,SAAS;UACZ,IAAI,CAACD,OAAO,CAACC,IAAI,CAAC,WAAW;UAC7B;QACF,KAAK,SAAS;UACZ,IAAI,CAACD,OAAO,CAACC,IAAI,CAAC,WAAW;UAC7B;MACJ;MACA,IAAI,CAACpB,iBAAgB,GAAI,KAAI;IAC/B,CAAC;IAEDyB,oBAAoBA,CAACrB,IAAI,EAAE;MACzB,MAAMsB,MAAK,GAAI;QACb,MAAM,EAAE,iBAAiB;QACzB,SAAS,EAAE,iBAAiB;QAC5B,SAAS,EAAE,cAAc;QACzB,MAAM,EAAE;MACV;MACA,OAAOA,MAAM,CAACtB,IAAI,KAAK,MAAK;IAC9B,CAAC;IAEDuB,mBAAmBA,CAACvB,IAAI,EAAE;MACxB,MAAMwB,KAAI,GAAI;QACZ,MAAM,EAAE,qBAAqB;QAC7B,SAAS,EAAE,uBAAuB;QAClC,SAAS,EAAE,cAAc;QACzB,MAAM,EAAE;MACV;MACA,OAAOA,KAAK,CAACxB,IAAI,KAAK,iBAAgB;IACxC,CAAC;IAEDyB,UAAUA,CAACtB,IAAI,EAAE;MACf,MAAME,GAAE,GAAI,IAAID,IAAI,CAAC;MACrB,MAAMsB,IAAG,GAAIrB,GAAE,GAAIF,IAAG;MACtB,MAAMwB,OAAM,GAAIC,IAAI,CAACC,KAAK,CAACH,IAAG,IAAK,IAAG,GAAI,EAAE,CAAC;MAC7C,MAAMI,KAAI,GAAIF,IAAI,CAACC,KAAK,CAACH,IAAG,IAAK,IAAG,GAAI,EAAC,GAAI,EAAE,CAAC;MAChD,MAAMK,IAAG,GAAIH,IAAI,CAACC,KAAK,CAACH,IAAG,IAAK,IAAG,GAAI,EAAC,GAAI,EAAC,GAAI,EAAE,CAAC;MAEpD,IAAIC,OAAM,GAAI,EAAE,EAAE;QAChB,OAAO,GAAGA,OAAO,KAAI;MACvB,OAAO,IAAIG,KAAI,GAAI,EAAE,EAAE;QACrB,OAAO,GAAGA,KAAK,KAAI;MACrB,OAAO;QACL,OAAO,GAAGC,IAAI,IAAG;MACnB;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}