{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, toDisplayString as _toDisplayString, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"google-content\"\n};\nconst _hoisted_2 = {\n  class: \"medical-page-header mb-6\"\n};\nconst _hoisted_3 = {\n  class: \"d-flex align-center justify-space-between\"\n};\nconst _hoisted_4 = {\n  class: \"d-flex align-center\"\n};\nconst _hoisted_5 = {\n  class: \"text-subtitle-1 text-medium-emphasis\"\n};\nconst _hoisted_6 = {\n  class: \"d-flex align-center ga-3\"\n};\nconst _hoisted_7 = {\n  class: \"text-h5 mb-1\"\n};\nconst _hoisted_8 = {\n  class: \"text-body-1 text-medium-emphasis\"\n};\nconst _hoisted_9 = {\n  class: \"d-flex align-center mb-3\"\n};\nconst _hoisted_10 = {\n  class: \"text-body-1\"\n};\nconst _hoisted_11 = {\n  class: \"d-flex align-center mb-3\"\n};\nconst _hoisted_12 = {\n  class: \"text-body-1\"\n};\nconst _hoisted_13 = {\n  class: \"d-flex align-center mb-3\"\n};\nconst _hoisted_14 = {\n  class: \"text-body-1\"\n};\nconst _hoisted_15 = {\n  class: \"d-flex align-center\"\n};\nconst _hoisted_16 = {\n  class: \"text-body-1\"\n};\nconst _hoisted_17 = {\n  class: \"d-flex align-center justify-space-between\"\n};\nconst _hoisted_18 = {\n  class: \"text-center\"\n};\nconst _hoisted_19 = {\n  class: \"text-h4 font-weight-bold\"\n};\nconst _hoisted_20 = {\n  class: \"text-center\"\n};\nconst _hoisted_21 = {\n  class: \"text-h4 font-weight-bold\"\n};\nconst _hoisted_22 = {\n  class: \"text-center\"\n};\nconst _hoisted_23 = {\n  class: \"text-h4 font-weight-bold\"\n};\nconst _hoisted_24 = {\n  class: \"text-center\"\n};\nconst _hoisted_25 = {\n  class: \"text-h4 font-weight-bold\"\n};\nconst _hoisted_26 = {\n  class: \"mb-4\"\n};\nconst _hoisted_27 = {\n  class: \"d-flex align-center justify-space-between mb-2\"\n};\nconst _hoisted_28 = {\n  class: \"text-body-1 font-weight-bold\"\n};\nconst _hoisted_29 = {\n  class: \"mb-4\"\n};\nconst _hoisted_30 = {\n  class: \"d-flex align-center justify-space-between mb-2\"\n};\nconst _hoisted_31 = {\n  class: \"text-body-1 font-weight-bold\"\n};\nconst _hoisted_32 = {\n  class: \"mb-4\"\n};\nconst _hoisted_33 = {\n  class: \"d-flex align-center justify-space-between mb-2\"\n};\nconst _hoisted_34 = {\n  class: \"text-body-1 font-weight-bold\"\n};\nconst _hoisted_35 = {\n  key: 0\n};\nconst _hoisted_36 = {\n  key: 0,\n  class: \"text-center mt-4\"\n};\nconst _hoisted_37 = {\n  class: \"mb-4\"\n};\nconst _hoisted_38 = {\n  class: \"d-flex justify-space-between mb-2\"\n};\nconst _hoisted_39 = {\n  class: \"font-weight-bold\"\n};\nconst _hoisted_40 = {\n  class: \"mb-4\"\n};\nconst _hoisted_41 = {\n  class: \"d-flex justify-space-between mb-2\"\n};\nconst _hoisted_42 = {\n  class: \"font-weight-bold\"\n};\nconst _hoisted_43 = {\n  class: \"mb-4\"\n};\nconst _hoisted_44 = {\n  class: \"d-flex justify-space-between mb-2\"\n};\nconst _hoisted_45 = {\n  class: \"font-weight-bold\"\n};\nconst _hoisted_46 = {\n  class: \"text-h6 mb-1\"\n};\nconst _hoisted_47 = {\n  class: \"text-caption text-medium-emphasis\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_v_icon = _resolveComponent(\"v-icon\");\n  const _component_v_btn = _resolveComponent(\"v-btn\");\n  const _component_v_img = _resolveComponent(\"v-img\");\n  const _component_v_avatar = _resolveComponent(\"v-avatar\");\n  const _component_v_divider = _resolveComponent(\"v-divider\");\n  const _component_v_card_text = _resolveComponent(\"v-card-text\");\n  const _component_v_card = _resolveComponent(\"v-card\");\n  const _component_v_card_title = _resolveComponent(\"v-card-title\");\n  const _component_v_col = _resolveComponent(\"v-col\");\n  const _component_v_text_field = _resolveComponent(\"v-text-field\");\n  const _component_v_select = _resolveComponent(\"v-select\");\n  const _component_v_textarea = _resolveComponent(\"v-textarea\");\n  const _component_v_row = _resolveComponent(\"v-row\");\n  const _component_v_form = _resolveComponent(\"v-form\");\n  const _component_v_progress_linear = _resolveComponent(\"v-progress-linear\");\n  const _component_v_chip = _resolveComponent(\"v-chip\");\n  const _component_v_file_input = _resolveComponent(\"v-file-input\");\n  const _component_v_spacer = _resolveComponent(\"v-spacer\");\n  const _component_v_card_actions = _resolveComponent(\"v-card-actions\");\n  const _component_v_dialog = _resolveComponent(\"v-dialog\");\n  const _component_v_list_item = _resolveComponent(\"v-list-item\");\n  const _component_v_list = _resolveComponent(\"v-list\");\n  const _component_v_snackbar = _resolveComponent(\"v-snackbar\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 页面头部 - Google风格 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_v_icon, {\n    icon: \"mdi-account-circle\",\n    size: \"48\",\n    color: \"medical-primary\",\n    class: \"me-4\"\n  }), _createElementVNode(\"div\", null, [_cache[21] || (_cache[21] = _createElementVNode(\"h1\", {\n    class: \"medical-gradient-text text-h4 mb-1\"\n  }, \"个人资料\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_5, [_createVNode(_component_v_icon, {\n    icon: \"mdi-hospital-box\",\n    size: \"16\",\n    class: \"me-1\"\n  }), _cache[20] || (_cache[20] = _createTextVNode(\" 医疗项目管理系统 - 个人信息管理 \"))])])]), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_v_btn, {\n    color: $data.editMode ? 'medical-warning' : 'medical-primary',\n    \"prepend-icon\": $data.editMode ? 'mdi-close' : 'mdi-pencil',\n    class: \"medical-btn-primary\",\n    onClick: _cache[0] || (_cache[0] = $event => $data.editMode = !$data.editMode)\n  }, {\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($data.editMode ? '取消编辑' : '编辑资料'), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"color\", \"prepend-icon\"]), _createVNode(_component_v_btn, {\n    color: \"medical-info\",\n    \"prepend-icon\": \"mdi-chart-line\",\n    variant: \"outlined\",\n    onClick: _cache[1] || (_cache[1] = $event => $data.showWorkflowOptimization = true)\n  }, {\n    default: _withCtx(() => _cache[22] || (_cache[22] = [_createTextVNode(\" 工作流优化 \")])),\n    _: 1 /* STABLE */,\n    __: [22]\n  })])])]), _createVNode(_component_v_row, null, {\n    default: _withCtx(() => [_createVNode(_component_v_col, {\n      cols: \"12\",\n      md: \"4\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_v_card, {\n        class: \"mb-4\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_v_card_text, {\n          class: \"text-center\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_avatar, {\n            size: \"150\",\n            class: \"mb-4\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_img, {\n              src: $data.user.avatar,\n              alt: \"用户头像\"\n            }, null, 8 /* PROPS */, [\"src\"])]),\n            _: 1 /* STABLE */\n          }), _createElementVNode(\"h2\", _hoisted_7, _toDisplayString($data.user.name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_8, _toDisplayString($data.user.position), 1 /* TEXT */), _createVNode(_component_v_divider, {\n            class: \"my-4\"\n          }), $data.editMode ? (_openBlock(), _createBlock(_component_v_btn, {\n            key: 0,\n            color: \"primary\",\n            variant: \"text\",\n            block: \"\",\n            \"prepend-icon\": \"mdi-camera\",\n            onClick: _cache[2] || (_cache[2] = $event => $data.showUploadDialog = true)\n          }, {\n            default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\" 更换头像 \")])),\n            _: 1 /* STABLE */,\n            __: [23]\n          })) : _createCommentVNode(\"v-if\", true)]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_v_card, null, {\n        default: _withCtx(() => [_createVNode(_component_v_card_title, null, {\n          default: _withCtx(() => _cache[24] || (_cache[24] = [_createTextVNode(\"联系方式\")])),\n          _: 1 /* STABLE */,\n          __: [24]\n        }), _createVNode(_component_v_card_text, null, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_v_icon, {\n            icon: \"mdi-email-outline\",\n            class: \"me-3\"\n          }), _createElementVNode(\"div\", null, [_cache[25] || (_cache[25] = _createElementVNode(\"div\", {\n            class: \"text-caption text-medium-emphasis\"\n          }, \"邮箱\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_10, _toDisplayString($data.user.email), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_11, [_createVNode(_component_v_icon, {\n            icon: \"mdi-phone-outline\",\n            class: \"me-3\"\n          }), _createElementVNode(\"div\", null, [_cache[26] || (_cache[26] = _createElementVNode(\"div\", {\n            class: \"text-caption text-medium-emphasis\"\n          }, \"手机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_12, _toDisplayString($data.user.phone), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_13, [_createVNode(_component_v_icon, {\n            icon: \"mdi-office-building-outline\",\n            class: \"me-3\"\n          }), _createElementVNode(\"div\", null, [_cache[27] || (_cache[27] = _createElementVNode(\"div\", {\n            class: \"text-caption text-medium-emphasis\"\n          }, \"部门\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_14, _toDisplayString($data.user.department), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_v_icon, {\n            icon: \"mdi-map-marker-outline\",\n            class: \"me-3\"\n          }), _createElementVNode(\"div\", null, [_cache[28] || (_cache[28] = _createElementVNode(\"div\", {\n            class: \"text-caption text-medium-emphasis\"\n          }, \"地址\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_16, _toDisplayString($data.user.address), 1 /* TEXT */)])])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_v_col, {\n      cols: \"12\",\n      md: \"8\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_v_card, {\n        class: \"mb-4\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_v_card_title, null, {\n          default: _withCtx(() => _cache[29] || (_cache[29] = [_createTextVNode(\"个人资料\")])),\n          _: 1 /* STABLE */,\n          __: [29]\n        }), _createVNode(_component_v_card_text, null, {\n          default: _withCtx(() => [_createVNode(_component_v_form, {\n            ref: \"form\",\n            modelValue: $data.valid,\n            \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $data.valid = $event)\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_row, null, {\n              default: _withCtx(() => [_createVNode(_component_v_col, {\n                cols: \"12\",\n                md: \"6\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_text_field, {\n                  modelValue: $data.user.name,\n                  \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $data.user.name = $event),\n                  label: \"姓名\",\n                  rules: $data.nameRules,\n                  variant: \"outlined\",\n                  readonly: !$data.editMode,\n                  required: \"\"\n                }, null, 8 /* PROPS */, [\"modelValue\", \"rules\", \"readonly\"])]),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_v_col, {\n                cols: \"12\",\n                md: \"6\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_text_field, {\n                  modelValue: $data.user.email,\n                  \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $data.user.email = $event),\n                  label: \"邮箱\",\n                  rules: $data.emailRules,\n                  variant: \"outlined\",\n                  readonly: \"\",\n                  disabled: \"\"\n                }, null, 8 /* PROPS */, [\"modelValue\", \"rules\"])]),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_v_col, {\n                cols: \"12\",\n                md: \"6\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_text_field, {\n                  modelValue: $data.user.phone,\n                  \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.user.phone = $event),\n                  label: \"手机号码\",\n                  rules: $data.phoneRules,\n                  variant: \"outlined\",\n                  readonly: !$data.editMode\n                }, null, 8 /* PROPS */, [\"modelValue\", \"rules\", \"readonly\"])]),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_v_col, {\n                cols: \"12\",\n                md: \"6\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_select, {\n                  modelValue: $data.user.department,\n                  \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.user.department = $event),\n                  items: $data.departments,\n                  label: \"部门\",\n                  variant: \"outlined\",\n                  readonly: !$data.editMode\n                }, null, 8 /* PROPS */, [\"modelValue\", \"items\", \"readonly\"])]),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_v_col, {\n                cols: \"12\",\n                md: \"6\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_text_field, {\n                  modelValue: $data.user.position,\n                  \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.user.position = $event),\n                  label: \"职位\",\n                  variant: \"outlined\",\n                  readonly: !$data.editMode\n                }, null, 8 /* PROPS */, [\"modelValue\", \"readonly\"])]),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_v_col, {\n                cols: \"12\",\n                md: \"6\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_text_field, {\n                  modelValue: $data.user.joinDate,\n                  \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.user.joinDate = $event),\n                  label: \"入职日期\",\n                  variant: \"outlined\",\n                  readonly: \"\",\n                  disabled: \"\"\n                }, null, 8 /* PROPS */, [\"modelValue\"])]),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_v_col, {\n                cols: \"12\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_text_field, {\n                  modelValue: $data.user.address,\n                  \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.user.address = $event),\n                  label: \"地址\",\n                  variant: \"outlined\",\n                  readonly: !$data.editMode\n                }, null, 8 /* PROPS */, [\"modelValue\", \"readonly\"])]),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_v_col, {\n                cols: \"12\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_textarea, {\n                  modelValue: $data.user.bio,\n                  \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $data.user.bio = $event),\n                  label: \"个人简介\",\n                  variant: \"outlined\",\n                  readonly: !$data.editMode,\n                  \"auto-grow\": \"\",\n                  rows: \"3\"\n                }, null, 8 /* PROPS */, [\"modelValue\", \"readonly\"])]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            }), $data.editMode ? (_openBlock(), _createBlock(_component_v_row, {\n              key: 0\n            }, {\n              default: _withCtx(() => [_createVNode(_component_v_col, {\n                cols: \"12\",\n                class: \"d-flex justify-end\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_btn, {\n                  color: \"primary\",\n                  disabled: !$data.valid,\n                  onClick: $options.saveProfile\n                }, {\n                  default: _withCtx(() => _cache[30] || (_cache[30] = [_createTextVNode(\" 保存资料 \")])),\n                  _: 1 /* STABLE */,\n                  __: [30]\n                }, 8 /* PROPS */, [\"disabled\", \"onClick\"])]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            })) : _createCommentVNode(\"v-if\", true)]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_v_card, null, {\n        default: _withCtx(() => [_createVNode(_component_v_card_title, null, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_17, [_cache[32] || (_cache[32] = _createElementVNode(\"span\", null, \"工作统计\", -1 /* HOISTED */)), _createVNode(_component_v_btn, {\n            color: \"primary\",\n            variant: \"text\",\n            size: \"small\",\n            \"prepend-icon\": \"mdi-chart-line\",\n            onClick: _cache[12] || (_cache[12] = $event => $data.showWorkflowOptimization = true)\n          }, {\n            default: _withCtx(() => _cache[31] || (_cache[31] = [_createTextVNode(\" 工作流优化 \")])),\n            _: 1 /* STABLE */,\n            __: [31]\n          })])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_v_card_text, null, {\n          default: _withCtx(() => [_createVNode(_component_v_row, null, {\n            default: _withCtx(() => [_createVNode(_component_v_col, {\n              cols: \"6\",\n              md: \"3\"\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, _toDisplayString($data.stats.projects), 1 /* TEXT */), _cache[33] || (_cache[33] = _createElementVNode(\"div\", {\n                class: \"text-caption text-medium-emphasis\"\n              }, \"参与项目\", -1 /* HOISTED */))])]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_v_col, {\n              cols: \"6\",\n              md: \"3\"\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, _toDisplayString($data.stats.tasks), 1 /* TEXT */), _cache[34] || (_cache[34] = _createElementVNode(\"div\", {\n                class: \"text-caption text-medium-emphasis\"\n              }, \"完成任务\", -1 /* HOISTED */))])]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_v_col, {\n              cols: \"6\",\n              md: \"3\"\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, _toDisplayString($data.stats.meetings), 1 /* TEXT */), _cache[35] || (_cache[35] = _createElementVNode(\"div\", {\n                class: \"text-caption text-medium-emphasis\"\n              }, \"参加会议\", -1 /* HOISTED */))])]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_v_col, {\n              cols: \"6\",\n              md: \"3\"\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"div\", _hoisted_25, _toDisplayString($data.stats.documents), 1 /* TEXT */), _cache[36] || (_cache[36] = _createElementVNode(\"div\", {\n                class: \"text-caption text-medium-emphasis\"\n              }, \"提交文档\", -1 /* HOISTED */))])]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_v_divider, {\n            class: \"my-4\"\n          }), _createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, [_cache[37] || (_cache[37] = _createElementVNode(\"div\", {\n            class: \"text-body-1\"\n          }, \"任务完成率\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_28, _toDisplayString($data.stats.taskCompletionRate) + \"%\", 1 /* TEXT */)]), _createVNode(_component_v_progress_linear, {\n            \"model-value\": $data.stats.taskCompletionRate,\n            color: \"success\",\n            height: \"8\",\n            rounded: \"\"\n          }, null, 8 /* PROPS */, [\"model-value\"])]), _createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"div\", _hoisted_30, [_cache[38] || (_cache[38] = _createElementVNode(\"div\", {\n            class: \"text-body-1\"\n          }, \"按时完成率\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_31, _toDisplayString($data.stats.onTimeRate) + \"%\", 1 /* TEXT */)]), _createVNode(_component_v_progress_linear, {\n            \"model-value\": $data.stats.onTimeRate,\n            color: \"info\",\n            height: \"8\",\n            rounded: \"\"\n          }, null, 8 /* PROPS */, [\"model-value\"])]), _createCommentVNode(\" 工作流效率指标 \"), _createElementVNode(\"div\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, [_cache[39] || (_cache[39] = _createElementVNode(\"div\", {\n            class: \"text-body-1\"\n          }, \"工作流效率\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_34, _toDisplayString($options.workflowEfficiency) + \"%\", 1 /* TEXT */)]), _createVNode(_component_v_progress_linear, {\n            \"model-value\": $options.workflowEfficiency,\n            color: $options.getEfficiencyColor($options.workflowEfficiency),\n            height: \"8\",\n            rounded: \"\"\n          }, null, 8 /* PROPS */, [\"model-value\", \"color\"])]), _createCommentVNode(\" 待处理事项 \"), $data.pendingItems.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_35, [_createVNode(_component_v_divider, {\n            class: \"my-4\"\n          }), _cache[40] || (_cache[40] = _createElementVNode(\"div\", {\n            class: \"text-subtitle-2 mb-2\"\n          }, \"待处理事项\", -1 /* HOISTED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.pendingItems, item => {\n            return _openBlock(), _createBlock(_component_v_chip, {\n              key: item.id,\n              color: item.priority === 'high' ? 'error' : item.priority === 'medium' ? 'warning' : 'info',\n              size: \"small\",\n              class: \"me-2 mb-2\",\n              onClick: $event => $options.handlePendingItem(item)\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString(item.title), 1 /* TEXT */)]),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"color\", \"onClick\"]);\n          }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true)]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 上传头像对话框 \"), _createVNode(_component_v_dialog, {\n    modelValue: $data.showUploadDialog,\n    \"onUpdate:modelValue\": _cache[15] || (_cache[15] = $event => $data.showUploadDialog = $event),\n    \"max-width\": \"500\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_v_card, null, {\n      default: _withCtx(() => [_createVNode(_component_v_card_title, null, {\n        default: _withCtx(() => _cache[41] || (_cache[41] = [_createTextVNode(\"上传头像\")])),\n        _: 1 /* STABLE */,\n        __: [41]\n      }), _createVNode(_component_v_card_text, null, {\n        default: _withCtx(() => [_createVNode(_component_v_file_input, {\n          modelValue: $data.avatarFile,\n          \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $data.avatarFile = $event),\n          label: \"选择图片\",\n          accept: \"image/*\",\n          \"show-size\": \"\",\n          \"truncate-length\": \"15\",\n          variant: \"outlined\"\n        }, null, 8 /* PROPS */, [\"modelValue\"]), $data.avatarPreview ? (_openBlock(), _createElementBlock(\"div\", _hoisted_36, [_createVNode(_component_v_avatar, {\n          size: \"150\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_img, {\n            src: $data.avatarPreview,\n            alt: \"Avatar Preview\"\n          }, null, 8 /* PROPS */, [\"src\"])]),\n          _: 1 /* STABLE */\n        })])) : _createCommentVNode(\"v-if\", true)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_v_card_actions, null, {\n        default: _withCtx(() => [_createVNode(_component_v_spacer), _createVNode(_component_v_btn, {\n          color: \"grey-darken-1\",\n          variant: \"text\",\n          onClick: _cache[14] || (_cache[14] = $event => $data.showUploadDialog = false)\n        }, {\n          default: _withCtx(() => _cache[42] || (_cache[42] = [_createTextVNode(\" 取消 \")])),\n          _: 1 /* STABLE */,\n          __: [42]\n        }), _createVNode(_component_v_btn, {\n          color: \"primary\",\n          variant: \"text\",\n          disabled: !$data.avatarFile,\n          onClick: $options.uploadAvatar\n        }, {\n          default: _withCtx(() => _cache[43] || (_cache[43] = [_createTextVNode(\" 上传 \")])),\n          _: 1 /* STABLE */,\n          __: [43]\n        }, 8 /* PROPS */, [\"disabled\", \"onClick\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 工作流优化对话框 \"), _createVNode(_component_v_dialog, {\n    modelValue: $data.showWorkflowOptimization,\n    \"onUpdate:modelValue\": _cache[17] || (_cache[17] = $event => $data.showWorkflowOptimization = $event),\n    \"max-width\": \"1200\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_v_card, null, {\n      default: _withCtx(() => [_createVNode(_component_v_card_title, {\n        class: \"d-flex align-center\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_v_icon, {\n          icon: \"mdi-chart-line\",\n          class: \"me-2\"\n        }), _cache[44] || (_cache[44] = _createTextVNode(\" 工作流优化分析 \"))]),\n        _: 1 /* STABLE */,\n        __: [44]\n      }), _createVNode(_component_v_card_text, null, {\n        default: _withCtx(() => [_createVNode(_component_v_row, null, {\n          default: _withCtx(() => [_createCommentVNode(\" 工作流效率分析 \"), _createVNode(_component_v_col, {\n            cols: \"12\",\n            md: \"6\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_card, {\n              variant: \"outlined\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_v_card_title, {\n                class: \"text-h6\"\n              }, {\n                default: _withCtx(() => _cache[45] || (_cache[45] = [_createTextVNode(\"效率分析\")])),\n                _: 1 /* STABLE */,\n                __: [45]\n              }), _createVNode(_component_v_card_text, null, {\n                default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_cache[46] || (_cache[46] = _createElementVNode(\"span\", null, \"任务处理速度\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_39, _toDisplayString($data.workflowMetrics.taskProcessingSpeed) + \"个/天\", 1 /* TEXT */)]), _createVNode(_component_v_progress_linear, {\n                  \"model-value\": $data.workflowMetrics.taskProcessingSpeed * 10,\n                  color: \"primary\",\n                  height: \"6\"\n                }, null, 8 /* PROPS */, [\"model-value\"])]), _createElementVNode(\"div\", _hoisted_40, [_createElementVNode(\"div\", _hoisted_41, [_cache[47] || (_cache[47] = _createElementVNode(\"span\", null, \"平均响应时间\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_42, _toDisplayString($data.workflowMetrics.avgResponseTime) + \"小时\", 1 /* TEXT */)]), _createVNode(_component_v_progress_linear, {\n                  \"model-value\": Math.max(0, 100 - $data.workflowMetrics.avgResponseTime * 5),\n                  color: \"info\",\n                  height: \"6\"\n                }, null, 8 /* PROPS */, [\"model-value\"])]), _createElementVNode(\"div\", _hoisted_43, [_createElementVNode(\"div\", _hoisted_44, [_cache[48] || (_cache[48] = _createElementVNode(\"span\", null, \"工作流自动化率\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_45, _toDisplayString($data.workflowMetrics.automationRate) + \"%\", 1 /* TEXT */)]), _createVNode(_component_v_progress_linear, {\n                  \"model-value\": $data.workflowMetrics.automationRate,\n                  color: \"success\",\n                  height: \"6\"\n                }, null, 8 /* PROPS */, [\"model-value\"])])]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          }), _createCommentVNode(\" 优化建议 \"), _createVNode(_component_v_col, {\n            cols: \"12\",\n            md: \"6\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_card, {\n              variant: \"outlined\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_v_card_title, {\n                class: \"text-h6\"\n              }, {\n                default: _withCtx(() => _cache[49] || (_cache[49] = [_createTextVNode(\"优化建议\")])),\n                _: 1 /* STABLE */,\n                __: [49]\n              }), _createVNode(_component_v_card_text, null, {\n                default: _withCtx(() => [_createVNode(_component_v_list, {\n                  density: \"compact\"\n                }, {\n                  default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.optimizationSuggestions, suggestion => {\n                    return _openBlock(), _createBlock(_component_v_list_item, {\n                      key: suggestion.id,\n                      \"prepend-icon\": suggestion.icon,\n                      title: suggestion.title,\n                      subtitle: suggestion.description,\n                      onClick: $event => $options.applySuggestion(suggestion)\n                    }, {\n                      append: _withCtx(() => [_createVNode(_component_v_chip, {\n                        color: suggestion.impact === 'high' ? 'success' : suggestion.impact === 'medium' ? 'warning' : 'info',\n                        size: \"small\"\n                      }, {\n                        default: _withCtx(() => [_createTextVNode(_toDisplayString(suggestion.impact === 'high' ? '高效果' : suggestion.impact === 'medium' ? '中效果' : '低效果'), 1 /* TEXT */)]),\n                        _: 2 /* DYNAMIC */\n                      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"color\"])]),\n                      _: 2 /* DYNAMIC */\n                    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"prepend-icon\", \"title\", \"subtitle\", \"onClick\"]);\n                  }), 128 /* KEYED_FRAGMENT */))]),\n                  _: 1 /* STABLE */\n                })]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          }), _createCommentVNode(\" 工作流模板 \"), _createVNode(_component_v_col, {\n            cols: \"12\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_card, {\n              variant: \"outlined\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_v_card_title, {\n                class: \"text-h6\"\n              }, {\n                default: _withCtx(() => _cache[50] || (_cache[50] = [_createTextVNode(\"工作流模板\")])),\n                _: 1 /* STABLE */,\n                __: [50]\n              }), _createVNode(_component_v_card_text, null, {\n                default: _withCtx(() => [_createVNode(_component_v_row, null, {\n                  default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.workflowTemplates, template => {\n                    return _openBlock(), _createBlock(_component_v_col, {\n                      key: template.id,\n                      cols: \"12\",\n                      md: \"4\"\n                    }, {\n                      default: _withCtx(() => [_createVNode(_component_v_card, {\n                        variant: \"outlined\",\n                        class: \"workflow-template-card\",\n                        onClick: $event => $options.applyWorkflowTemplate(template)\n                      }, {\n                        default: _withCtx(() => [_createVNode(_component_v_card_text, {\n                          class: \"text-center\"\n                        }, {\n                          default: _withCtx(() => [_createVNode(_component_v_icon, {\n                            icon: template.icon,\n                            size: \"48\",\n                            color: template.color,\n                            class: \"mb-2\"\n                          }, null, 8 /* PROPS */, [\"icon\", \"color\"]), _createElementVNode(\"div\", _hoisted_46, _toDisplayString(template.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_47, _toDisplayString(template.description), 1 /* TEXT */)]),\n                          _: 2 /* DYNAMIC */\n                        }, 1024 /* DYNAMIC_SLOTS */)]),\n                        _: 2 /* DYNAMIC */\n                      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n                      _: 2 /* DYNAMIC */\n                    }, 1024 /* DYNAMIC_SLOTS */);\n                  }), 128 /* KEYED_FRAGMENT */))]),\n                  _: 1 /* STABLE */\n                })]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_v_card_actions, null, {\n        default: _withCtx(() => [_createVNode(_component_v_spacer), _createVNode(_component_v_btn, {\n          color: \"grey-darken-1\",\n          variant: \"text\",\n          onClick: _cache[16] || (_cache[16] = $event => $data.showWorkflowOptimization = false)\n        }, {\n          default: _withCtx(() => _cache[51] || (_cache[51] = [_createTextVNode(\" 关闭 \")])),\n          _: 1 /* STABLE */,\n          __: [51]\n        }), _createVNode(_component_v_btn, {\n          color: \"primary\",\n          variant: \"text\",\n          onClick: $options.exportWorkflowReport\n        }, {\n          default: _withCtx(() => _cache[52] || (_cache[52] = [_createTextVNode(\" 导出报告 \")])),\n          _: 1 /* STABLE */,\n          __: [52]\n        }, 8 /* PROPS */, [\"onClick\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 操作成功提示 \"), _createVNode(_component_v_snackbar, {\n    modelValue: $data.showSnackbar,\n    \"onUpdate:modelValue\": _cache[19] || (_cache[19] = $event => $data.showSnackbar = $event),\n    color: \"success\"\n  }, {\n    actions: _withCtx(() => [_createVNode(_component_v_btn, {\n      variant: \"text\",\n      onClick: _cache[18] || (_cache[18] = $event => $data.showSnackbar = false)\n    }, {\n      default: _withCtx(() => _cache[53] || (_cache[53] = [_createTextVNode(\" 关闭 \")])),\n      _: 1 /* STABLE */,\n      __: [53]\n    })]),\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($data.snackbarText) + \" \", 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_v_icon", "icon", "size", "color", "_hoisted_5", "_createTextVNode", "_hoisted_6", "_component_v_btn", "$data", "editMode", "onClick", "_cache", "$event", "default", "_withCtx", "_toDisplayString", "_", "variant", "showWorkflowOptimization", "__", "_component_v_row", "_component_v_col", "cols", "md", "_component_v_card", "_component_v_card_text", "_component_v_avatar", "_component_v_img", "src", "user", "avatar", "alt", "_hoisted_7", "name", "_hoisted_8", "position", "_component_v_divider", "_createBlock", "block", "showUploadDialog", "_component_v_card_title", "_hoisted_9", "_hoisted_10", "email", "_hoisted_11", "_hoisted_12", "phone", "_hoisted_13", "_hoisted_14", "department", "_hoisted_15", "_hoisted_16", "address", "_component_v_form", "ref", "modelValue", "valid", "_component_v_text_field", "label", "rules", "nameRules", "readonly", "required", "emailRules", "disabled", "phoneRules", "_component_v_select", "items", "departments", "joinDate", "_component_v_textarea", "bio", "rows", "$options", "saveProfile", "_hoisted_17", "_hoisted_18", "_hoisted_19", "stats", "projects", "_hoisted_20", "_hoisted_21", "tasks", "_hoisted_22", "_hoisted_23", "meetings", "_hoisted_24", "_hoisted_25", "documents", "_hoisted_26", "_hoisted_27", "_hoisted_28", "taskCompletionRate", "_component_v_progress_linear", "height", "rounded", "_hoisted_29", "_hoisted_30", "_hoisted_31", "onTimeRate", "_hoisted_32", "_hoisted_33", "_hoisted_34", "workflowEfficiency", "getEfficiencyColor", "pendingItems", "length", "_hoisted_35", "_Fragment", "_renderList", "item", "_component_v_chip", "id", "priority", "handlePendingItem", "title", "_component_v_dialog", "_component_v_file_input", "avatar<PERSON>ile", "accept", "avatarPreview", "_hoisted_36", "_component_v_card_actions", "_component_v_spacer", "uploadAvatar", "_hoisted_37", "_hoisted_38", "_hoisted_39", "workflowMetrics", "taskProcessingSpeed", "_hoisted_40", "_hoisted_41", "_hoisted_42", "avgResponseTime", "Math", "max", "_hoisted_43", "_hoisted_44", "_hoisted_45", "automationRate", "_component_v_list", "density", "optimizationSuggestions", "suggestion", "_component_v_list_item", "subtitle", "description", "applySuggestion", "append", "impact", "workflowTemplates", "template", "applyWorkflowTemplate", "_hoisted_46", "_hoisted_47", "exportWorkflowReport", "_component_v_snackbar", "showSnackbar", "actions", "snackbarText"], "sources": ["/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/技术测试/项目实施进度管理/project-management/frontend/src/views/Profile.vue"], "sourcesContent": ["<template>\n  <div class=\"google-content\">\n    <!-- 页面头部 - Google风格 -->\n    <div class=\"medical-page-header mb-6\">\n      <div class=\"d-flex align-center justify-space-between\">\n        <div class=\"d-flex align-center\">\n          <v-icon\n            icon=\"mdi-account-circle\"\n            size=\"48\"\n            color=\"medical-primary\"\n            class=\"me-4\"\n          ></v-icon>\n          <div>\n            <h1 class=\"medical-gradient-text text-h4 mb-1\">个人资料</h1>\n            <p class=\"text-subtitle-1 text-medium-emphasis\">\n              <v-icon icon=\"mdi-hospital-box\" size=\"16\" class=\"me-1\"></v-icon>\n              医疗项目管理系统 - 个人信息管理\n            </p>\n          </div>\n        </div>\n        <div class=\"d-flex align-center ga-3\">\n          <v-btn\n            :color=\"editMode ? 'medical-warning' : 'medical-primary'\"\n            :prepend-icon=\"editMode ? 'mdi-close' : 'mdi-pencil'\"\n            class=\"medical-btn-primary\"\n            @click=\"editMode = !editMode\"\n          >\n            {{ editMode ? '取消编辑' : '编辑资料' }}\n          </v-btn>\n          <v-btn\n            color=\"medical-info\"\n            prepend-icon=\"mdi-chart-line\"\n            variant=\"outlined\"\n            @click=\"showWorkflowOptimization = true\"\n          >\n            工作流优化\n          </v-btn>\n        </div>\n      </div>\n    </div>\n\n    <v-row>\n      <v-col cols=\"12\" md=\"4\">\n        <v-card class=\"mb-4\">\n          <v-card-text class=\"text-center\">\n            <v-avatar size=\"150\" class=\"mb-4\">\n              <v-img :src=\"user.avatar\" alt=\"用户头像\"></v-img>\n            </v-avatar>\n\n            <h2 class=\"text-h5 mb-1\">{{ user.name }}</h2>\n            <p class=\"text-body-1 text-medium-emphasis\">{{ user.position }}</p>\n\n            <v-divider class=\"my-4\"></v-divider>\n\n            <v-btn\n              v-if=\"editMode\"\n              color=\"primary\"\n              variant=\"text\"\n              block\n              prepend-icon=\"mdi-camera\"\n              @click=\"showUploadDialog = true\"\n            >\n              更换头像\n            </v-btn>\n          </v-card-text>\n        </v-card>\n\n        <v-card>\n          <v-card-title>联系方式</v-card-title>\n          <v-card-text>\n            <div class=\"d-flex align-center mb-3\">\n              <v-icon icon=\"mdi-email-outline\" class=\"me-3\"></v-icon>\n              <div>\n                <div class=\"text-caption text-medium-emphasis\">邮箱</div>\n                <div class=\"text-body-1\">{{ user.email }}</div>\n              </div>\n            </div>\n\n            <div class=\"d-flex align-center mb-3\">\n              <v-icon icon=\"mdi-phone-outline\" class=\"me-3\"></v-icon>\n              <div>\n                <div class=\"text-caption text-medium-emphasis\">手机</div>\n                <div class=\"text-body-1\">{{ user.phone }}</div>\n              </div>\n            </div>\n\n            <div class=\"d-flex align-center mb-3\">\n              <v-icon icon=\"mdi-office-building-outline\" class=\"me-3\"></v-icon>\n              <div>\n                <div class=\"text-caption text-medium-emphasis\">部门</div>\n                <div class=\"text-body-1\">{{ user.department }}</div>\n              </div>\n            </div>\n\n            <div class=\"d-flex align-center\">\n              <v-icon icon=\"mdi-map-marker-outline\" class=\"me-3\"></v-icon>\n              <div>\n                <div class=\"text-caption text-medium-emphasis\">地址</div>\n                <div class=\"text-body-1\">{{ user.address }}</div>\n              </div>\n            </div>\n          </v-card-text>\n        </v-card>\n      </v-col>\n\n      <v-col cols=\"12\" md=\"8\">\n        <v-card class=\"mb-4\">\n          <v-card-title>个人资料</v-card-title>\n          <v-card-text>\n            <v-form ref=\"form\" v-model=\"valid\">\n              <v-row>\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.name\"\n                    label=\"姓名\"\n                    :rules=\"nameRules\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                    required\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.email\"\n                    label=\"邮箱\"\n                    :rules=\"emailRules\"\n                    variant=\"outlined\"\n                    readonly\n                    disabled\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.phone\"\n                    label=\"手机号码\"\n                    :rules=\"phoneRules\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-select\n                    v-model=\"user.department\"\n                    :items=\"departments\"\n                    label=\"部门\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                  ></v-select>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.position\"\n                    label=\"职位\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.joinDate\"\n                    label=\"入职日期\"\n                    variant=\"outlined\"\n                    readonly\n                    disabled\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\">\n                  <v-text-field\n                    v-model=\"user.address\"\n                    label=\"地址\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\">\n                  <v-textarea\n                    v-model=\"user.bio\"\n                    label=\"个人简介\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                    auto-grow\n                    rows=\"3\"\n                  ></v-textarea>\n                </v-col>\n              </v-row>\n\n              <v-row v-if=\"editMode\">\n                <v-col cols=\"12\" class=\"d-flex justify-end\">\n                  <v-btn\n                    color=\"primary\"\n                    :disabled=\"!valid\"\n                    @click=\"saveProfile\"\n                  >\n                    保存资料\n                  </v-btn>\n                </v-col>\n              </v-row>\n            </v-form>\n          </v-card-text>\n        </v-card>\n\n        <v-card>\n          <v-card-title>\n            <div class=\"d-flex align-center justify-space-between\">\n              <span>工作统计</span>\n              <v-btn\n                color=\"primary\"\n                variant=\"text\"\n                size=\"small\"\n                prepend-icon=\"mdi-chart-line\"\n                @click=\"showWorkflowOptimization = true\"\n              >\n                工作流优化\n              </v-btn>\n            </div>\n          </v-card-title>\n          <v-card-text>\n            <v-row>\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.projects }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">参与项目</div>\n                </div>\n              </v-col>\n\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.tasks }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">完成任务</div>\n                </div>\n              </v-col>\n\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.meetings }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">参加会议</div>\n                </div>\n              </v-col>\n\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.documents }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">提交文档</div>\n                </div>\n              </v-col>\n            </v-row>\n\n            <v-divider class=\"my-4\"></v-divider>\n\n            <div class=\"mb-4\">\n              <div class=\"d-flex align-center justify-space-between mb-2\">\n                <div class=\"text-body-1\">任务完成率</div>\n                <div class=\"text-body-1 font-weight-bold\">{{ stats.taskCompletionRate }}%</div>\n              </div>\n              <v-progress-linear\n                :model-value=\"stats.taskCompletionRate\"\n                color=\"success\"\n                height=\"8\"\n                rounded\n              ></v-progress-linear>\n            </div>\n\n            <div class=\"mb-4\">\n              <div class=\"d-flex align-center justify-space-between mb-2\">\n                <div class=\"text-body-1\">按时完成率</div>\n                <div class=\"text-body-1 font-weight-bold\">{{ stats.onTimeRate }}%</div>\n              </div>\n              <v-progress-linear\n                :model-value=\"stats.onTimeRate\"\n                color=\"info\"\n                height=\"8\"\n                rounded\n              ></v-progress-linear>\n            </div>\n\n            <!-- 工作流效率指标 -->\n            <div class=\"mb-4\">\n              <div class=\"d-flex align-center justify-space-between mb-2\">\n                <div class=\"text-body-1\">工作流效率</div>\n                <div class=\"text-body-1 font-weight-bold\">{{ workflowEfficiency }}%</div>\n              </div>\n              <v-progress-linear\n                :model-value=\"workflowEfficiency\"\n                :color=\"getEfficiencyColor(workflowEfficiency)\"\n                height=\"8\"\n                rounded\n              ></v-progress-linear>\n            </div>\n\n            <!-- 待处理事项 -->\n            <div v-if=\"pendingItems.length > 0\">\n              <v-divider class=\"my-4\"></v-divider>\n              <div class=\"text-subtitle-2 mb-2\">待处理事项</div>\n              <v-chip\n                v-for=\"item in pendingItems\"\n                :key=\"item.id\"\n                :color=\"item.priority === 'high' ? 'error' : item.priority === 'medium' ? 'warning' : 'info'\"\n                size=\"small\"\n                class=\"me-2 mb-2\"\n                @click=\"handlePendingItem(item)\"\n              >\n                {{ item.title }}\n              </v-chip>\n            </div>\n          </v-card-text>\n        </v-card>\n      </v-col>\n    </v-row>\n\n    <!-- 上传头像对话框 -->\n    <v-dialog v-model=\"showUploadDialog\" max-width=\"500\">\n      <v-card>\n        <v-card-title>上传头像</v-card-title>\n        <v-card-text>\n          <v-file-input\n            v-model=\"avatarFile\"\n            label=\"选择图片\"\n            accept=\"image/*\"\n            show-size\n            truncate-length=\"15\"\n            variant=\"outlined\"\n          ></v-file-input>\n\n          <div v-if=\"avatarPreview\" class=\"text-center mt-4\">\n            <v-avatar size=\"150\">\n              <v-img :src=\"avatarPreview\" alt=\"Avatar Preview\"></v-img>\n            </v-avatar>\n          </div>\n        </v-card-text>\n        <v-card-actions>\n          <v-spacer></v-spacer>\n          <v-btn\n            color=\"grey-darken-1\"\n            variant=\"text\"\n            @click=\"showUploadDialog = false\"\n          >\n            取消\n          </v-btn>\n          <v-btn\n            color=\"primary\"\n            variant=\"text\"\n            :disabled=\"!avatarFile\"\n            @click=\"uploadAvatar\"\n          >\n            上传\n          </v-btn>\n        </v-card-actions>\n      </v-card>\n    </v-dialog>\n\n    <!-- 工作流优化对话框 -->\n    <v-dialog v-model=\"showWorkflowOptimization\" max-width=\"1200\">\n      <v-card>\n        <v-card-title class=\"d-flex align-center\">\n          <v-icon icon=\"mdi-chart-line\" class=\"me-2\"></v-icon>\n          工作流优化分析\n        </v-card-title>\n\n        <v-card-text>\n          <v-row>\n            <!-- 工作流效率分析 -->\n            <v-col cols=\"12\" md=\"6\">\n              <v-card variant=\"outlined\">\n                <v-card-title class=\"text-h6\">效率分析</v-card-title>\n                <v-card-text>\n                  <div class=\"mb-4\">\n                    <div class=\"d-flex justify-space-between mb-2\">\n                      <span>任务处理速度</span>\n                      <span class=\"font-weight-bold\">{{ workflowMetrics.taskProcessingSpeed }}个/天</span>\n                    </div>\n                    <v-progress-linear\n                      :model-value=\"workflowMetrics.taskProcessingSpeed * 10\"\n                      color=\"primary\"\n                      height=\"6\"\n                    ></v-progress-linear>\n                  </div>\n\n                  <div class=\"mb-4\">\n                    <div class=\"d-flex justify-space-between mb-2\">\n                      <span>平均响应时间</span>\n                      <span class=\"font-weight-bold\">{{ workflowMetrics.avgResponseTime }}小时</span>\n                    </div>\n                    <v-progress-linear\n                      :model-value=\"Math.max(0, 100 - workflowMetrics.avgResponseTime * 5)\"\n                      color=\"info\"\n                      height=\"6\"\n                    ></v-progress-linear>\n                  </div>\n\n                  <div class=\"mb-4\">\n                    <div class=\"d-flex justify-space-between mb-2\">\n                      <span>工作流自动化率</span>\n                      <span class=\"font-weight-bold\">{{ workflowMetrics.automationRate }}%</span>\n                    </div>\n                    <v-progress-linear\n                      :model-value=\"workflowMetrics.automationRate\"\n                      color=\"success\"\n                      height=\"6\"\n                    ></v-progress-linear>\n                  </div>\n                </v-card-text>\n              </v-card>\n            </v-col>\n\n            <!-- 优化建议 -->\n            <v-col cols=\"12\" md=\"6\">\n              <v-card variant=\"outlined\">\n                <v-card-title class=\"text-h6\">优化建议</v-card-title>\n                <v-card-text>\n                  <v-list density=\"compact\">\n                    <v-list-item\n                      v-for=\"suggestion in optimizationSuggestions\"\n                      :key=\"suggestion.id\"\n                      :prepend-icon=\"suggestion.icon\"\n                      :title=\"suggestion.title\"\n                      :subtitle=\"suggestion.description\"\n                      @click=\"applySuggestion(suggestion)\"\n                    >\n                      <template v-slot:append>\n                        <v-chip\n                          :color=\"suggestion.impact === 'high' ? 'success' : suggestion.impact === 'medium' ? 'warning' : 'info'\"\n                          size=\"small\"\n                        >\n                          {{ suggestion.impact === 'high' ? '高效果' : suggestion.impact === 'medium' ? '中效果' : '低效果' }}\n                        </v-chip>\n                      </template>\n                    </v-list-item>\n                  </v-list>\n                </v-card-text>\n              </v-card>\n            </v-col>\n\n            <!-- 工作流模板 -->\n            <v-col cols=\"12\">\n              <v-card variant=\"outlined\">\n                <v-card-title class=\"text-h6\">工作流模板</v-card-title>\n                <v-card-text>\n                  <v-row>\n                    <v-col\n                      v-for=\"template in workflowTemplates\"\n                      :key=\"template.id\"\n                      cols=\"12\"\n                      md=\"4\"\n                    >\n                      <v-card\n                        variant=\"outlined\"\n                        class=\"workflow-template-card\"\n                        @click=\"applyWorkflowTemplate(template)\"\n                      >\n                        <v-card-text class=\"text-center\">\n                          <v-icon\n                            :icon=\"template.icon\"\n                            size=\"48\"\n                            :color=\"template.color\"\n                            class=\"mb-2\"\n                          ></v-icon>\n                          <div class=\"text-h6 mb-1\">{{ template.name }}</div>\n                          <div class=\"text-caption text-medium-emphasis\">{{ template.description }}</div>\n                        </v-card-text>\n                      </v-card>\n                    </v-col>\n                  </v-row>\n                </v-card-text>\n              </v-card>\n            </v-col>\n          </v-row>\n        </v-card-text>\n\n        <v-card-actions>\n          <v-spacer></v-spacer>\n          <v-btn\n            color=\"grey-darken-1\"\n            variant=\"text\"\n            @click=\"showWorkflowOptimization = false\"\n          >\n            关闭\n          </v-btn>\n          <v-btn\n            color=\"primary\"\n            variant=\"text\"\n            @click=\"exportWorkflowReport\"\n          >\n            导出报告\n          </v-btn>\n        </v-card-actions>\n      </v-card>\n    </v-dialog>\n\n    <!-- 操作成功提示 -->\n    <v-snackbar\n      v-model=\"showSnackbar\"\n      color=\"success\"\n    >\n      {{ snackbarText }}\n\n      <template v-slot:actions>\n        <v-btn\n          variant=\"text\"\n          @click=\"showSnackbar = false\"\n        >\n          关闭\n        </v-btn>\n      </template>\n    </v-snackbar>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ProfileView',\n  data() {\n    return {\n      editMode: false,\n      valid: true,\n      showUploadDialog: false,\n      avatarFile: null,\n      avatarPreview: null,\n      showSnackbar: false,\n      snackbarText: '',\n      showWorkflowOptimization: false,\n\n      nameRules: [\n        v => !!v || '姓名不能为空',\n        v => v.length <= 20 || '姓名不能超过20个字符'\n      ],\n      emailRules: [\n        v => !!v || '邮箱不能为空',\n        v => /.+@.+\\..+/.test(v) || '邮箱格式不正确'\n      ],\n      phoneRules: [\n        v => !!v || '手机号码不能为空',\n        v => /^1[3-9]\\d{9}$/.test(v) || '手机号码格式不正确'\n      ],\n\n      departments: ['技术部', '市场部', '销售部', '人力资源部', '财务部'],\n\n      user: {\n        name: '张三',\n        email: '<EMAIL>',\n        phone: '13800138000',\n        department: '技术部',\n        position: '项目经理',\n        joinDate: '2023-01-15',\n        address: '上海市浦东新区张江高科技园区',\n        bio: '拥有8年项目管理经验，专注于软件开发领域。擅长敏捷开发方法，曾成功带领团队完成多个大型项目。',\n        avatar: 'https://ui-avatars.com/api/?name=张三&background=random'\n      },\n\n      stats: {\n        projects: 12,\n        tasks: 156,\n        meetings: 48,\n        documents: 32,\n        taskCompletionRate: 92,\n        onTimeRate: 88\n      },\n\n      // 工作流优化相关数据\n      workflowMetrics: {\n        taskProcessingSpeed: 8.5,\n        avgResponseTime: 2.3,\n        automationRate: 75\n      },\n\n      pendingItems: [\n        { id: 1, title: '项目A审批', priority: 'high', type: 'approval' },\n        { id: 2, title: '任务B验收', priority: 'medium', type: 'review' },\n        { id: 3, title: '会议记录确认', priority: 'low', type: 'confirmation' }\n      ],\n\n      optimizationSuggestions: [\n        {\n          id: 1,\n          title: '启用任务自动分配',\n          description: '根据团队成员工作负载自动分配新任务',\n          icon: 'mdi-account-multiple-plus',\n          impact: 'high',\n          action: 'enable_auto_assignment'\n        },\n        {\n          id: 2,\n          title: '设置状态自动流转',\n          description: '任务完成后自动触发下一阶段',\n          icon: 'mdi-arrow-right-circle',\n          impact: 'high',\n          action: 'enable_auto_transition'\n        },\n        {\n          id: 3,\n          title: '优化通知频率',\n          description: '减少非关键通知，提高工作专注度',\n          icon: 'mdi-bell-outline',\n          impact: 'medium',\n          action: 'optimize_notifications'\n        },\n        {\n          id: 4,\n          title: '启用智能提醒',\n          description: '基于历史数据预测任务延期风险',\n          icon: 'mdi-brain',\n          impact: 'high',\n          action: 'enable_smart_reminders'\n        }\n      ],\n\n      workflowTemplates: [\n        {\n          id: 1,\n          name: '医疗项目标准流程',\n          description: '适用于医疗项目的标准化工作流',\n          icon: 'mdi-hospital-box',\n          color: 'primary'\n        },\n        {\n          id: 2,\n          name: '敏捷开发流程',\n          description: '快速迭代的敏捷开发工作流',\n          icon: 'mdi-rocket-launch',\n          color: 'success'\n        },\n        {\n          id: 3,\n          name: '审批密集型流程',\n          description: '需要多层审批的严格工作流',\n          icon: 'mdi-shield-check',\n          color: 'warning'\n        }\n      ]\n    }\n  },\n  computed: {\n    workflowEfficiency() {\n      // 基于任务完成率、按时完成率和自动化率计算工作流效率\n      const taskWeight = 0.4\n      const timeWeight = 0.3\n      const autoWeight = 0.3\n\n      return Math.round(\n        this.stats.taskCompletionRate * taskWeight +\n        this.stats.onTimeRate * timeWeight +\n        this.workflowMetrics.automationRate * autoWeight\n      )\n    }\n  },\n  watch: {\n    avatarFile(file) {\n      if (file) {\n        this.createImagePreview(file)\n      } else {\n        this.avatarPreview = null\n      }\n    }\n  },\n  methods: {\n    saveProfile() {\n      // 在实际应用中，这里会发送请求到后端保存个人资料\n      this.editMode = false\n      this.showSuccessMessage('个人资料保存成功')\n    },\n    createImagePreview(file) {\n      const reader = new FileReader()\n      reader.readAsDataURL(file)\n      reader.onload = e => {\n        this.avatarPreview = e.target.result\n      }\n    },\n    uploadAvatar() {\n      // 在实际应用中，这里会发送请求到后端上传头像\n      if (this.avatarPreview) {\n        this.user.avatar = this.avatarPreview\n      }\n      this.showUploadDialog = false\n      this.avatarFile = null\n      this.showSuccessMessage('头像上传成功')\n    },\n    showSuccessMessage(text) {\n      this.snackbarText = text\n      this.showSnackbar = true\n    },\n\n    // 工作流优化相关方法\n    getEfficiencyColor(efficiency) {\n      if (efficiency >= 85) return 'success'\n      if (efficiency >= 70) return 'warning'\n      return 'error'\n    },\n\n    handlePendingItem(item) {\n      // 处理待办事项\n      switch (item.type) {\n        case 'approval':\n          this.$router.push('/projects')\n          break\n        case 'review':\n          this.$router.push('/kanban')\n          break\n        case 'confirmation':\n          this.$router.push('/meetings')\n          break\n      }\n      this.showSuccessMessage(`正在处理：${item.title}`)\n    },\n\n    applySuggestion(suggestion) {\n      // 应用优化建议\n      switch (suggestion.action) {\n        case 'enable_auto_assignment':\n          this.enableAutoAssignment()\n          break\n        case 'enable_auto_transition':\n          this.enableAutoTransition()\n          break\n        case 'optimize_notifications':\n          this.optimizeNotifications()\n          break\n        case 'enable_smart_reminders':\n          this.enableSmartReminders()\n          break\n      }\n      this.showSuccessMessage(`已应用优化建议：${suggestion.title}`)\n    },\n\n    applyWorkflowTemplate(template) {\n      // 应用工作流模板\n      this.showSuccessMessage(`正在应用工作流模板：${template.name}`)\n      // 这里可以调用API来应用模板\n    },\n\n    exportWorkflowReport() {\n      // 导出工作流报告\n      const reportData = {\n        user: this.user.name,\n        date: new Date().toLocaleDateString(),\n        efficiency: this.workflowEfficiency,\n        metrics: this.workflowMetrics,\n        suggestions: this.optimizationSuggestions.length\n      }\n\n      // 模拟导出功能\n      console.log('导出工作流报告:', reportData)\n      this.showSuccessMessage('工作流报告已导出')\n    },\n\n    // 优化功能实现\n    enableAutoAssignment() {\n      // 启用自动分配功能\n      this.workflowMetrics.automationRate = Math.min(100, this.workflowMetrics.automationRate + 10)\n    },\n\n    enableAutoTransition() {\n      // 启用自动流转功能\n      this.workflowMetrics.automationRate = Math.min(100, this.workflowMetrics.automationRate + 15)\n      this.workflowMetrics.avgResponseTime = Math.max(0.5, this.workflowMetrics.avgResponseTime - 0.5)\n    },\n\n    optimizeNotifications() {\n      // 优化通知设置\n      this.workflowMetrics.avgResponseTime = Math.max(0.5, this.workflowMetrics.avgResponseTime - 0.3)\n    },\n\n    enableSmartReminders() {\n      // 启用智能提醒\n      this.workflowMetrics.taskProcessingSpeed = Math.min(15, this.workflowMetrics.taskProcessingSpeed + 1.5)\n      this.stats.onTimeRate = Math.min(100, this.stats.onTimeRate + 5)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.workflow-template-card {\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.workflow-template-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.workflow-template-card .v-card-text {\n  padding: 24px;\n}\n\n/* 工作流效率指标样式 */\n.v-progress-linear {\n  border-radius: 4px;\n}\n\n/* 待处理事项样式 */\n.v-chip {\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.v-chip:hover {\n  transform: scale(1.05);\n}\n\n/* 优化建议列表样式 */\n.v-list-item {\n  border-radius: 8px;\n  margin-bottom: 8px;\n  transition: background-color 0.2s ease;\n}\n\n.v-list-item:hover {\n  background-color: rgba(0, 0, 0, 0.04);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .workflow-template-card .v-card-text {\n    padding: 16px;\n  }\n\n  .v-icon {\n    font-size: 36px !important;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAA2C;;EAC/CA,KAAK,EAAC;AAAqB;;EASzBA,KAAK,EAAC;AAAsC;;EAM9CA,KAAK,EAAC;AAA0B;;EA6B7BA,KAAK,EAAC;AAAc;;EACrBA,KAAK,EAAC;AAAkC;;EAoBtCA,KAAK,EAAC;AAA0B;;EAI5BA,KAAK,EAAC;AAAa;;EAIvBA,KAAK,EAAC;AAA0B;;EAI5BA,KAAK,EAAC;AAAa;;EAIvBA,KAAK,EAAC;AAA0B;;EAI5BA,KAAK,EAAC;AAAa;;EAIvBA,KAAK,EAAC;AAAqB;;EAIvBA,KAAK,EAAC;AAAa;;EAgHvBA,KAAK,EAAC;AAA2C;;EAgB7CA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAA0B;;EAMlCA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAA0B;;EAMlCA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAA0B;;EAMlCA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAA0B;;EAQtCA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAgD;;EAEpDA,KAAK,EAAC;AAA8B;;EAUxCA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAgD;;EAEpDA,KAAK,EAAC;AAA8B;;EAWxCA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAgD;;EAEpDA,KAAK,EAAC;AAA8B;;EA9RzDC,GAAA;AAAA;;EAAAA,GAAA;EA0UoCD,KAAK,EAAC;;;EA0CnBA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAmC;;EAEtCA,KAAK,EAAC;AAAkB;;EAS7BA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAmC;;EAEtCA,KAAK,EAAC;AAAkB;;EAS7BA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAmC;;EAEtCA,KAAK,EAAC;AAAkB;;EAgErBA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAmC;;;;;;;;;;;;;;;;;;;;;;;;;uBA/ctEE,mBAAA,CA8fM,OA9fNC,UA8fM,GA7fJC,mBAAA,qBAAwB,EACxBC,mBAAA,CAoCM,OApCNC,UAoCM,GAnCJD,mBAAA,CAkCM,OAlCNE,UAkCM,GAjCJF,mBAAA,CAcM,OAdNG,UAcM,GAbJC,YAAA,CAKUC,iBAAA;IAJRC,IAAI,EAAC,oBAAoB;IACzBC,IAAI,EAAC,IAAI;IACTC,KAAK,EAAC,iBAAiB;IACvBb,KAAK,EAAC;MAERK,mBAAA,CAMM,c,4BALJA,mBAAA,CAAwD;IAApDL,KAAK,EAAC;EAAoC,GAAC,MAAI,sBACnDK,mBAAA,CAGI,KAHJS,UAGI,GAFFL,YAAA,CAAgEC,iBAAA;IAAxDC,IAAI,EAAC,kBAAkB;IAACC,IAAI,EAAC,IAAI;IAACZ,KAAK,EAAC;kCAf9De,gBAAA,CAe8E,qBAElE,G,OAGJV,mBAAA,CAiBM,OAjBNW,UAiBM,GAhBJP,YAAA,CAOQQ,gBAAA;IANLJ,KAAK,EAAEK,KAAA,CAAAC,QAAQ;IACf,cAAY,EAAED,KAAA,CAAAC,QAAQ;IACvBnB,KAAK,EAAC,qBAAqB;IAC1BoB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEJ,KAAA,CAAAC,QAAQ,IAAID,KAAA,CAAAC,QAAQ;;IAzBxCI,OAAA,EAAAC,QAAA,CA2BY,MAAgC,CA3B5CT,gBAAA,CAAAU,gBAAA,CA2BeP,KAAA,CAAAC,QAAQ,mC;IA3BvBO,CAAA;gDA6BUjB,YAAA,CAOQQ,gBAAA;IANNJ,KAAK,EAAC,cAAc;IACpB,cAAY,EAAC,gBAAgB;IAC7Bc,OAAO,EAAC,UAAU;IACjBP,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEJ,KAAA,CAAAU,wBAAwB;;IAjC5CL,OAAA,EAAAC,QAAA,CAkCW,MAEDH,MAAA,SAAAA,MAAA,QApCVN,gBAAA,CAkCW,SAED,E;IApCVW,CAAA;IAAAG,EAAA;YAyCIpB,YAAA,CAiRQqB,gBAAA;IA1TZP,OAAA,EAAAC,QAAA,CA0CM,MA6DQ,CA7DRf,YAAA,CA6DQsB,gBAAA;MA7DDC,IAAI,EAAC,IAAI;MAACC,EAAE,EAAC;;MA1C1BV,OAAA,EAAAC,QAAA,CA2CQ,MAsBS,CAtBTf,YAAA,CAsBSyB,iBAAA;QAtBDlC,KAAK,EAAC;MAAM;QA3C5BuB,OAAA,EAAAC,QAAA,CA4CU,MAoBc,CApBdf,YAAA,CAoBc0B,sBAAA;UApBDnC,KAAK,EAAC;QAAa;UA5C1CuB,OAAA,EAAAC,QAAA,CA6CY,MAEW,CAFXf,YAAA,CAEW2B,mBAAA;YAFDxB,IAAI,EAAC,KAAK;YAACZ,KAAK,EAAC;;YA7CvCuB,OAAA,EAAAC,QAAA,CA8Cc,MAA6C,CAA7Cf,YAAA,CAA6C4B,gBAAA;cAArCC,GAAG,EAAEpB,KAAA,CAAAqB,IAAI,CAACC,MAAM;cAAEC,GAAG,EAAC;;YA9C5Cf,CAAA;cAiDYrB,mBAAA,CAA6C,MAA7CqC,UAA6C,EAAAjB,gBAAA,CAAjBP,KAAA,CAAAqB,IAAI,CAACI,IAAI,kBACrCtC,mBAAA,CAAmE,KAAnEuC,UAAmE,EAAAnB,gBAAA,CAApBP,KAAA,CAAAqB,IAAI,CAACM,QAAQ,kBAE5DpC,YAAA,CAAoCqC,oBAAA;YAAzB9C,KAAK,EAAC;UAAM,IAGfkB,KAAA,CAAAC,QAAQ,I,cADhB4B,YAAA,CASQ9B,gBAAA;YA/DpBhB,GAAA;YAwDcY,KAAK,EAAC,SAAS;YACfc,OAAO,EAAC,MAAM;YACdqB,KAAK,EAAL,EAAK;YACL,cAAY,EAAC,YAAY;YACxB5B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEJ,KAAA,CAAA+B,gBAAgB;;YA5DtC1B,OAAA,EAAAC,QAAA,CA6Da,MAEDH,MAAA,SAAAA,MAAA,QA/DZN,gBAAA,CA6Da,QAED,E;YA/DZW,CAAA;YAAAG,EAAA;gBAAAzB,mBAAA,e;UAAAsB,CAAA;;QAAAA,CAAA;UAmEQjB,YAAA,CAmCSyB,iBAAA;QAtGjBX,OAAA,EAAAC,QAAA,CAoEU,MAAiC,CAAjCf,YAAA,CAAiCyC,uBAAA;UApE3C3B,OAAA,EAAAC,QAAA,CAoEwB,MAAIH,MAAA,SAAAA,MAAA,QApE5BN,gBAAA,CAoEwB,MAAI,E;UApE5BW,CAAA;UAAAG,EAAA;YAqEUpB,YAAA,CAgCc0B,sBAAA;UArGxBZ,OAAA,EAAAC,QAAA,CAsEY,MAMM,CANNnB,mBAAA,CAMM,OANN8C,UAMM,GALJ1C,YAAA,CAAuDC,iBAAA;YAA/CC,IAAI,EAAC,mBAAmB;YAACX,KAAK,EAAC;cACvCK,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAuD;YAAlDL,KAAK,EAAC;UAAmC,GAAC,IAAE,sBACjDK,mBAAA,CAA+C,OAA/C+C,WAA+C,EAAA3B,gBAAA,CAAnBP,KAAA,CAAAqB,IAAI,CAACc,KAAK,iB,KAI1ChD,mBAAA,CAMM,OANNiD,WAMM,GALJ7C,YAAA,CAAuDC,iBAAA;YAA/CC,IAAI,EAAC,mBAAmB;YAACX,KAAK,EAAC;cACvCK,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAuD;YAAlDL,KAAK,EAAC;UAAmC,GAAC,IAAE,sBACjDK,mBAAA,CAA+C,OAA/CkD,WAA+C,EAAA9B,gBAAA,CAAnBP,KAAA,CAAAqB,IAAI,CAACiB,KAAK,iB,KAI1CnD,mBAAA,CAMM,OANNoD,WAMM,GALJhD,YAAA,CAAiEC,iBAAA;YAAzDC,IAAI,EAAC,6BAA6B;YAACX,KAAK,EAAC;cACjDK,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAuD;YAAlDL,KAAK,EAAC;UAAmC,GAAC,IAAE,sBACjDK,mBAAA,CAAoD,OAApDqD,WAAoD,EAAAjC,gBAAA,CAAxBP,KAAA,CAAAqB,IAAI,CAACoB,UAAU,iB,KAI/CtD,mBAAA,CAMM,OANNuD,WAMM,GALJnD,YAAA,CAA4DC,iBAAA;YAApDC,IAAI,EAAC,wBAAwB;YAACX,KAAK,EAAC;cAC5CK,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAuD;YAAlDL,KAAK,EAAC;UAAmC,GAAC,IAAE,sBACjDK,mBAAA,CAAiD,OAAjDwD,WAAiD,EAAApC,gBAAA,CAArBP,KAAA,CAAAqB,IAAI,CAACuB,OAAO,iB;UAlGxDpC,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;QAyGMjB,YAAA,CAgNQsB,gBAAA;MAhNDC,IAAI,EAAC,IAAI;MAACC,EAAE,EAAC;;MAzG1BV,OAAA,EAAAC,QAAA,CA0GQ,MAoGS,CApGTf,YAAA,CAoGSyB,iBAAA;QApGDlC,KAAK,EAAC;MAAM;QA1G5BuB,OAAA,EAAAC,QAAA,CA2GU,MAAiC,CAAjCf,YAAA,CAAiCyC,uBAAA;UA3G3C3B,OAAA,EAAAC,QAAA,CA2GwB,MAAIH,MAAA,SAAAA,MAAA,QA3G5BN,gBAAA,CA2GwB,MAAI,E;UA3G5BW,CAAA;UAAAG,EAAA;YA4GUpB,YAAA,CAiGc0B,sBAAA;UA7MxBZ,OAAA,EAAAC,QAAA,CA6GY,MA+FS,CA/FTf,YAAA,CA+FSsD,iBAAA;YA/FDC,GAAG,EAAC,MAAM;YA7G9BC,UAAA,EA6GwC/C,KAAA,CAAAgD,KAAK;YA7G7C,uBAAA7C,MAAA,SAAAA,MAAA,OAAAC,MAAA,IA6GwCJ,KAAA,CAAAgD,KAAK,GAAA5C,MAAA;;YA7G7CC,OAAA,EAAAC,QAAA,CA8Gc,MAiFQ,CAjFRf,YAAA,CAiFQqB,gBAAA;cA/LtBP,OAAA,EAAAC,QAAA,CA+GgB,MASQ,CATRf,YAAA,CASQsB,gBAAA;gBATDC,IAAI,EAAC,IAAI;gBAACC,EAAE,EAAC;;gBA/GpCV,OAAA,EAAAC,QAAA,CAgHkB,MAOgB,CAPhBf,YAAA,CAOgB0D,uBAAA;kBAvHlCF,UAAA,EAiH6B/C,KAAA,CAAAqB,IAAI,CAACI,IAAI;kBAjHtC,uBAAAtB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAiH6BJ,KAAA,CAAAqB,IAAI,CAACI,IAAI,GAAArB,MAAA;kBAClB8C,KAAK,EAAC,IAAI;kBACTC,KAAK,EAAEnD,KAAA,CAAAoD,SAAS;kBACjB3C,OAAO,EAAC,UAAU;kBACjB4C,QAAQ,GAAGrD,KAAA,CAAAC,QAAQ;kBACpBqD,QAAQ,EAAR;;gBAtHpB9C,CAAA;kBA0HgBjB,YAAA,CASQsB,gBAAA;gBATDC,IAAI,EAAC,IAAI;gBAACC,EAAE,EAAC;;gBA1HpCV,OAAA,EAAAC,QAAA,CA2HkB,MAOgB,CAPhBf,YAAA,CAOgB0D,uBAAA;kBAlIlCF,UAAA,EA4H6B/C,KAAA,CAAAqB,IAAI,CAACc,KAAK;kBA5HvC,uBAAAhC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA4H6BJ,KAAA,CAAAqB,IAAI,CAACc,KAAK,GAAA/B,MAAA;kBACnB8C,KAAK,EAAC,IAAI;kBACTC,KAAK,EAAEnD,KAAA,CAAAuD,UAAU;kBAClB9C,OAAO,EAAC,UAAU;kBAClB4C,QAAQ,EAAR,EAAQ;kBACRG,QAAQ,EAAR;;gBAjIpBhD,CAAA;kBAqIgBjB,YAAA,CAQQsB,gBAAA;gBARDC,IAAI,EAAC,IAAI;gBAACC,EAAE,EAAC;;gBArIpCV,OAAA,EAAAC,QAAA,CAsIkB,MAMgB,CANhBf,YAAA,CAMgB0D,uBAAA;kBA5IlCF,UAAA,EAuI6B/C,KAAA,CAAAqB,IAAI,CAACiB,KAAK;kBAvIvC,uBAAAnC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAuI6BJ,KAAA,CAAAqB,IAAI,CAACiB,KAAK,GAAAlC,MAAA;kBACnB8C,KAAK,EAAC,MAAM;kBACXC,KAAK,EAAEnD,KAAA,CAAAyD,UAAU;kBAClBhD,OAAO,EAAC,UAAU;kBACjB4C,QAAQ,GAAGrD,KAAA,CAAAC;;gBA3IhCO,CAAA;kBA+IgBjB,YAAA,CAQQsB,gBAAA;gBARDC,IAAI,EAAC,IAAI;gBAACC,EAAE,EAAC;;gBA/IpCV,OAAA,EAAAC,QAAA,CAgJkB,MAMY,CANZf,YAAA,CAMYmE,mBAAA;kBAtJ9BX,UAAA,EAiJ6B/C,KAAA,CAAAqB,IAAI,CAACoB,UAAU;kBAjJ5C,uBAAAtC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAiJ6BJ,KAAA,CAAAqB,IAAI,CAACoB,UAAU,GAAArC,MAAA;kBACvBuD,KAAK,EAAE3D,KAAA,CAAA4D,WAAW;kBACnBV,KAAK,EAAC,IAAI;kBACVzC,OAAO,EAAC,UAAU;kBACjB4C,QAAQ,GAAGrD,KAAA,CAAAC;;gBArJhCO,CAAA;kBAyJgBjB,YAAA,CAOQsB,gBAAA;gBAPDC,IAAI,EAAC,IAAI;gBAACC,EAAE,EAAC;;gBAzJpCV,OAAA,EAAAC,QAAA,CA0JkB,MAKgB,CALhBf,YAAA,CAKgB0D,uBAAA;kBA/JlCF,UAAA,EA2J6B/C,KAAA,CAAAqB,IAAI,CAACM,QAAQ;kBA3J1C,uBAAAxB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA2J6BJ,KAAA,CAAAqB,IAAI,CAACM,QAAQ,GAAAvB,MAAA;kBACtB8C,KAAK,EAAC,IAAI;kBACVzC,OAAO,EAAC,UAAU;kBACjB4C,QAAQ,GAAGrD,KAAA,CAAAC;;gBA9JhCO,CAAA;kBAkKgBjB,YAAA,CAQQsB,gBAAA;gBARDC,IAAI,EAAC,IAAI;gBAACC,EAAE,EAAC;;gBAlKpCV,OAAA,EAAAC,QAAA,CAmKkB,MAMgB,CANhBf,YAAA,CAMgB0D,uBAAA;kBAzKlCF,UAAA,EAoK6B/C,KAAA,CAAAqB,IAAI,CAACwC,QAAQ;kBApK1C,uBAAA1D,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAoK6BJ,KAAA,CAAAqB,IAAI,CAACwC,QAAQ,GAAAzD,MAAA;kBACtB8C,KAAK,EAAC,MAAM;kBACZzC,OAAO,EAAC,UAAU;kBAClB4C,QAAQ,EAAR,EAAQ;kBACRG,QAAQ,EAAR;;gBAxKpBhD,CAAA;kBA4KgBjB,YAAA,CAOQsB,gBAAA;gBAPDC,IAAI,EAAC;cAAI;gBA5KhCT,OAAA,EAAAC,QAAA,CA6KkB,MAKgB,CALhBf,YAAA,CAKgB0D,uBAAA;kBAlLlCF,UAAA,EA8K6B/C,KAAA,CAAAqB,IAAI,CAACuB,OAAO;kBA9KzC,uBAAAzC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA8K6BJ,KAAA,CAAAqB,IAAI,CAACuB,OAAO,GAAAxC,MAAA;kBACrB8C,KAAK,EAAC,IAAI;kBACVzC,OAAO,EAAC,UAAU;kBACjB4C,QAAQ,GAAGrD,KAAA,CAAAC;;gBAjLhCO,CAAA;kBAqLgBjB,YAAA,CASQsB,gBAAA;gBATDC,IAAI,EAAC;cAAI;gBArLhCT,OAAA,EAAAC,QAAA,CAsLkB,MAOc,CAPdf,YAAA,CAOcuE,qBAAA;kBA7LhCf,UAAA,EAuL6B/C,KAAA,CAAAqB,IAAI,CAAC0C,GAAG;kBAvLrC,uBAAA5D,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAuL6BJ,KAAA,CAAAqB,IAAI,CAAC0C,GAAG,GAAA3D,MAAA;kBACjB8C,KAAK,EAAC,MAAM;kBACZzC,OAAO,EAAC,UAAU;kBACjB4C,QAAQ,GAAGrD,KAAA,CAAAC,QAAQ;kBACpB,WAAS,EAAT,EAAS;kBACT+D,IAAI,EAAC;;gBA5LzBxD,CAAA;;cAAAA,CAAA;gBAiM2BR,KAAA,CAAAC,QAAQ,I,cAArB4B,YAAA,CAUQjB,gBAAA;cA3MtB7B,GAAA;YAAA;cAAAsB,OAAA,EAAAC,QAAA,CAkMgB,MAQQ,CARRf,YAAA,CAQQsB,gBAAA;gBARDC,IAAI,EAAC,IAAI;gBAAChC,KAAK,EAAC;;gBAlMvCuB,OAAA,EAAAC,QAAA,CAmMkB,MAMQ,CANRf,YAAA,CAMQQ,gBAAA;kBALNJ,KAAK,EAAC,SAAS;kBACd6D,QAAQ,GAAGxD,KAAA,CAAAgD,KAAK;kBAChB9C,OAAK,EAAE+D,QAAA,CAAAC;;kBAtM5B7D,OAAA,EAAAC,QAAA,CAuMmB,MAEDH,MAAA,SAAAA,MAAA,QAzMlBN,gBAAA,CAuMmB,QAED,E;kBAzMlBW,CAAA;kBAAAG,EAAA;;gBAAAH,CAAA;;cAAAA,CAAA;kBAAAtB,mBAAA,e;YAAAsB,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;UAgNQjB,YAAA,CAwGSyB,iBAAA;QAxTjBX,OAAA,EAAAC,QAAA,CAiNU,MAae,CAbff,YAAA,CAaeyC,uBAAA;UA9NzB3B,OAAA,EAAAC,QAAA,CAkNY,MAWM,CAXNnB,mBAAA,CAWM,OAXNgF,WAWM,G,4BAVJhF,mBAAA,CAAiB,cAAX,MAAI,sBACVI,YAAA,CAQQQ,gBAAA;YAPNJ,KAAK,EAAC,SAAS;YACfc,OAAO,EAAC,MAAM;YACdf,IAAI,EAAC,OAAO;YACZ,cAAY,EAAC,gBAAgB;YAC5BQ,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEJ,KAAA,CAAAU,wBAAwB;;YAzNhDL,OAAA,EAAAC,QAAA,CA0Ne,MAEDH,MAAA,SAAAA,MAAA,QA5NdN,gBAAA,CA0Ne,SAED,E;YA5NdW,CAAA;YAAAG,EAAA;;UAAAH,CAAA;YA+NUjB,YAAA,CAwFc0B,sBAAA;UAvTxBZ,OAAA,EAAAC,QAAA,CAgOY,MA4BQ,CA5BRf,YAAA,CA4BQqB,gBAAA;YA5PpBP,OAAA,EAAAC,QAAA,CAiOc,MAKQ,CALRf,YAAA,CAKQsB,gBAAA;cALDC,IAAI,EAAC,GAAG;cAACC,EAAE,EAAC;;cAjOjCV,OAAA,EAAAC,QAAA,CAkOgB,MAGM,CAHNnB,mBAAA,CAGM,OAHNiF,WAGM,GAFJjF,mBAAA,CAAgE,OAAhEkF,WAAgE,EAAA9D,gBAAA,CAAvBP,KAAA,CAAAsE,KAAK,CAACC,QAAQ,kB,4BACvDpF,mBAAA,CAAyD;gBAApDL,KAAK,EAAC;cAAmC,GAAC,MAAI,qB;cApOrE0B,CAAA;gBAwOcjB,YAAA,CAKQsB,gBAAA;cALDC,IAAI,EAAC,GAAG;cAACC,EAAE,EAAC;;cAxOjCV,OAAA,EAAAC,QAAA,CAyOgB,MAGM,CAHNnB,mBAAA,CAGM,OAHNqF,WAGM,GAFJrF,mBAAA,CAA6D,OAA7DsF,WAA6D,EAAAlE,gBAAA,CAApBP,KAAA,CAAAsE,KAAK,CAACI,KAAK,kB,4BACpDvF,mBAAA,CAAyD;gBAApDL,KAAK,EAAC;cAAmC,GAAC,MAAI,qB;cA3OrE0B,CAAA;gBA+OcjB,YAAA,CAKQsB,gBAAA;cALDC,IAAI,EAAC,GAAG;cAACC,EAAE,EAAC;;cA/OjCV,OAAA,EAAAC,QAAA,CAgPgB,MAGM,CAHNnB,mBAAA,CAGM,OAHNwF,WAGM,GAFJxF,mBAAA,CAAgE,OAAhEyF,WAAgE,EAAArE,gBAAA,CAAvBP,KAAA,CAAAsE,KAAK,CAACO,QAAQ,kB,4BACvD1F,mBAAA,CAAyD;gBAApDL,KAAK,EAAC;cAAmC,GAAC,MAAI,qB;cAlPrE0B,CAAA;gBAsPcjB,YAAA,CAKQsB,gBAAA;cALDC,IAAI,EAAC,GAAG;cAACC,EAAE,EAAC;;cAtPjCV,OAAA,EAAAC,QAAA,CAuPgB,MAGM,CAHNnB,mBAAA,CAGM,OAHN2F,WAGM,GAFJ3F,mBAAA,CAAiE,OAAjE4F,WAAiE,EAAAxE,gBAAA,CAAxBP,KAAA,CAAAsE,KAAK,CAACU,SAAS,kB,4BACxD7F,mBAAA,CAAyD;gBAApDL,KAAK,EAAC;cAAmC,GAAC,MAAI,qB;cAzPrE0B,CAAA;;YAAAA,CAAA;cA8PYjB,YAAA,CAAoCqC,oBAAA;YAAzB9C,KAAK,EAAC;UAAM,IAEvBK,mBAAA,CAWM,OAXN8F,WAWM,GAVJ9F,mBAAA,CAGM,OAHN+F,WAGM,G,4BAFJ/F,mBAAA,CAAoC;YAA/BL,KAAK,EAAC;UAAa,GAAC,OAAK,sBAC9BK,mBAAA,CAA+E,OAA/EgG,WAA+E,EAAA5E,gBAAA,CAAlCP,KAAA,CAAAsE,KAAK,CAACc,kBAAkB,IAAG,GAAC,gB,GAE3E7F,YAAA,CAKqB8F,4BAAA;YAJlB,aAAW,EAAErF,KAAA,CAAAsE,KAAK,CAACc,kBAAkB;YACtCzF,KAAK,EAAC,SAAS;YACf2F,MAAM,EAAC,GAAG;YACVC,OAAO,EAAP;sDAIJpG,mBAAA,CAWM,OAXNqG,WAWM,GAVJrG,mBAAA,CAGM,OAHNsG,WAGM,G,4BAFJtG,mBAAA,CAAoC;YAA/BL,KAAK,EAAC;UAAa,GAAC,OAAK,sBAC9BK,mBAAA,CAAuE,OAAvEuG,WAAuE,EAAAnF,gBAAA,CAA1BP,KAAA,CAAAsE,KAAK,CAACqB,UAAU,IAAG,GAAC,gB,GAEnEpG,YAAA,CAKqB8F,4BAAA;YAJlB,aAAW,EAAErF,KAAA,CAAAsE,KAAK,CAACqB,UAAU;YAC9BhG,KAAK,EAAC,MAAM;YACZ2F,MAAM,EAAC,GAAG;YACVC,OAAO,EAAP;sDAIJrG,mBAAA,aAAgB,EAChBC,mBAAA,CAWM,OAXNyG,WAWM,GAVJzG,mBAAA,CAGM,OAHN0G,WAGM,G,4BAFJ1G,mBAAA,CAAoC;YAA/BL,KAAK,EAAC;UAAa,GAAC,OAAK,sBAC9BK,mBAAA,CAAyE,OAAzE2G,WAAyE,EAAAvF,gBAAA,CAA5B0D,QAAA,CAAA8B,kBAAkB,IAAG,GAAC,gB,GAErExG,YAAA,CAKqB8F,4BAAA;YAJlB,aAAW,EAAEpB,QAAA,CAAA8B,kBAAkB;YAC/BpG,KAAK,EAAEsE,QAAA,CAAA+B,kBAAkB,CAAC/B,QAAA,CAAA8B,kBAAkB;YAC7CT,MAAM,EAAC,GAAG;YACVC,OAAO,EAAP;+DAIJrG,mBAAA,WAAc,EACHc,KAAA,CAAAiG,YAAY,CAACC,MAAM,Q,cAA9BlH,mBAAA,CAaM,OAtTlBmH,WAAA,GA0Sc5G,YAAA,CAAoCqC,oBAAA;YAAzB9C,KAAK,EAAC;UAAM,I,4BACvBK,mBAAA,CAA6C;YAAxCL,KAAK,EAAC;UAAsB,GAAC,OAAK,uB,kBACvCE,mBAAA,CASSoH,SAAA,QArTvBC,WAAA,CA6S+BrG,KAAA,CAAAiG,YAAY,EAApBK,IAAI;iCADbzE,YAAA,CASS0E,iBAAA;cAPNxH,GAAG,EAAEuH,IAAI,CAACE,EAAE;cACZ7G,KAAK,EAAE2G,IAAI,CAACG,QAAQ,wBAAwBH,IAAI,CAACG,QAAQ;cAC1D/G,IAAI,EAAC,OAAO;cACZZ,KAAK,EAAC,WAAW;cAChBoB,OAAK,EAAAE,MAAA,IAAE6D,QAAA,CAAAyC,iBAAiB,CAACJ,IAAI;;cAlT9CjG,OAAA,EAAAC,QAAA,CAoTgB,MAAgB,CApThCT,gBAAA,CAAAU,gBAAA,CAoTmB+F,IAAI,CAACK,KAAK,iB;cApT7BnG,CAAA;;8CAAAtB,mBAAA,e;UAAAsB,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;MA4TItB,mBAAA,aAAgB,EAChBK,YAAA,CAsCWqH,mBAAA;IAnWf7D,UAAA,EA6TuB/C,KAAA,CAAA+B,gBAAgB;IA7TvC,uBAAA5B,MAAA,SAAAA,MAAA,OAAAC,MAAA,IA6TuBJ,KAAA,CAAA+B,gBAAgB,GAAA3B,MAAA;IAAE,WAAS,EAAC;;IA7TnDC,OAAA,EAAAC,QAAA,CA8TM,MAoCS,CApCTf,YAAA,CAoCSyB,iBAAA;MAlWfX,OAAA,EAAAC,QAAA,CA+TQ,MAAiC,CAAjCf,YAAA,CAAiCyC,uBAAA;QA/TzC3B,OAAA,EAAAC,QAAA,CA+TsB,MAAIH,MAAA,SAAAA,MAAA,QA/T1BN,gBAAA,CA+TsB,MAAI,E;QA/T1BW,CAAA;QAAAG,EAAA;UAgUQpB,YAAA,CAec0B,sBAAA;QA/UtBZ,OAAA,EAAAC,QAAA,CAiUU,MAOgB,CAPhBf,YAAA,CAOgBsH,uBAAA;UAxU1B9D,UAAA,EAkUqB/C,KAAA,CAAA8G,UAAU;UAlU/B,uBAAA3G,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAkUqBJ,KAAA,CAAA8G,UAAU,GAAA1G,MAAA;UACnB8C,KAAK,EAAC,MAAM;UACZ6D,MAAM,EAAC,SAAS;UAChB,WAAS,EAAT,EAAS;UACT,iBAAe,EAAC,IAAI;UACpBtG,OAAO,EAAC;iDAGCT,KAAA,CAAAgH,aAAa,I,cAAxBhI,mBAAA,CAIM,OAJNiI,WAIM,GAHJ1H,YAAA,CAEW2B,mBAAA;UAFDxB,IAAI,EAAC;QAAK;UA3UhCW,OAAA,EAAAC,QAAA,CA4Uc,MAAyD,CAAzDf,YAAA,CAAyD4B,gBAAA;YAAjDC,GAAG,EAAEpB,KAAA,CAAAgH,aAAa;YAAEzF,GAAG,EAAC;;UA5U9Cf,CAAA;gBAAAtB,mBAAA,e;QAAAsB,CAAA;UAgVQjB,YAAA,CAiBiB2H,yBAAA;QAjWzB7G,OAAA,EAAAC,QAAA,CAiVU,MAAqB,CAArBf,YAAA,CAAqB4H,mBAAA,GACrB5H,YAAA,CAMQQ,gBAAA;UALNJ,KAAK,EAAC,eAAe;UACrBc,OAAO,EAAC,MAAM;UACbP,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEJ,KAAA,CAAA+B,gBAAgB;;UArVpC1B,OAAA,EAAAC,QAAA,CAsVW,MAEDH,MAAA,SAAAA,MAAA,QAxVVN,gBAAA,CAsVW,MAED,E;UAxVVW,CAAA;UAAAG,EAAA;YAyVUpB,YAAA,CAOQQ,gBAAA;UANNJ,KAAK,EAAC,SAAS;UACfc,OAAO,EAAC,MAAM;UACb+C,QAAQ,GAAGxD,KAAA,CAAA8G,UAAU;UACrB5G,OAAK,EAAE+D,QAAA,CAAAmD;;UA7VpB/G,OAAA,EAAAC,QAAA,CA8VW,MAEDH,MAAA,SAAAA,MAAA,QAhWVN,gBAAA,CA8VW,MAED,E;UAhWVW,CAAA;UAAAG,EAAA;;QAAAH,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;qCAqWItB,mBAAA,cAAiB,EACjBK,YAAA,CAuIWqH,mBAAA;IA7ef7D,UAAA,EAsWuB/C,KAAA,CAAAU,wBAAwB;IAtW/C,uBAAAP,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAsWuBJ,KAAA,CAAAU,wBAAwB,GAAAN,MAAA;IAAE,WAAS,EAAC;;IAtW3DC,OAAA,EAAAC,QAAA,CAuWM,MAqIS,CArITf,YAAA,CAqISyB,iBAAA;MA5efX,OAAA,EAAAC,QAAA,CAwWQ,MAGe,CAHff,YAAA,CAGeyC,uBAAA;QAHDlD,KAAK,EAAC;MAAqB;QAxWjDuB,OAAA,EAAAC,QAAA,CAyWU,MAAoD,CAApDf,YAAA,CAAoDC,iBAAA;UAA5CC,IAAI,EAAC,gBAAgB;UAACX,KAAK,EAAC;wCAzW9Ce,gBAAA,CAyW8D,WAEtD,G;QA3WRW,CAAA;QAAAG,EAAA;UA6WQpB,YAAA,CA4Gc0B,sBAAA;QAzdtBZ,OAAA,EAAAC,QAAA,CA8WU,MA0GQ,CA1GRf,YAAA,CA0GQqB,gBAAA;UAxdlBP,OAAA,EAAAC,QAAA,CA+WY,MAAgB,CAAhBpB,mBAAA,aAAgB,EAChBK,YAAA,CAyCQsB,gBAAA;YAzCDC,IAAI,EAAC,IAAI;YAACC,EAAE,EAAC;;YAhXhCV,OAAA,EAAAC,QAAA,CAiXc,MAuCS,CAvCTf,YAAA,CAuCSyB,iBAAA;cAvCDP,OAAO,EAAC;YAAU;cAjXxCJ,OAAA,EAAAC,QAAA,CAkXgB,MAAiD,CAAjDf,YAAA,CAAiDyC,uBAAA;gBAAnClD,KAAK,EAAC;cAAS;gBAlX7CuB,OAAA,EAAAC,QAAA,CAkX8C,MAAIH,MAAA,SAAAA,MAAA,QAlXlDN,gBAAA,CAkX8C,MAAI,E;gBAlXlDW,CAAA;gBAAAG,EAAA;kBAmXgBpB,YAAA,CAoCc0B,sBAAA;gBAvZ9BZ,OAAA,EAAAC,QAAA,CAoXkB,MAUM,CAVNnB,mBAAA,CAUM,OAVNkI,WAUM,GATJlI,mBAAA,CAGM,OAHNmI,WAGM,G,4BAFJnI,mBAAA,CAAmB,cAAb,QAAM,sBACZA,mBAAA,CAAkF,QAAlFoI,WAAkF,EAAAhH,gBAAA,CAAhDP,KAAA,CAAAwH,eAAe,CAACC,mBAAmB,IAAG,KAAG,gB,GAE7ElI,YAAA,CAIqB8F,4BAAA;kBAHlB,aAAW,EAAErF,KAAA,CAAAwH,eAAe,CAACC,mBAAmB;kBACjD9H,KAAK,EAAC,SAAS;kBACf2F,MAAM,EAAC;4DAIXnG,mBAAA,CAUM,OAVNuI,WAUM,GATJvI,mBAAA,CAGM,OAHNwI,WAGM,G,4BAFJxI,mBAAA,CAAmB,cAAb,QAAM,sBACZA,mBAAA,CAA6E,QAA7EyI,WAA6E,EAAArH,gBAAA,CAA3CP,KAAA,CAAAwH,eAAe,CAACK,eAAe,IAAG,IAAE,gB,GAExEtI,YAAA,CAIqB8F,4BAAA;kBAHlB,aAAW,EAAEyC,IAAI,CAACC,GAAG,UAAU/H,KAAA,CAAAwH,eAAe,CAACK,eAAe;kBAC/DlI,KAAK,EAAC,MAAM;kBACZ2F,MAAM,EAAC;4DAIXnG,mBAAA,CAUM,OAVN6I,WAUM,GATJ7I,mBAAA,CAGM,OAHN8I,WAGM,G,4BAFJ9I,mBAAA,CAAoB,cAAd,SAAO,sBACbA,mBAAA,CAA2E,QAA3E+I,WAA2E,EAAA3H,gBAAA,CAAzCP,KAAA,CAAAwH,eAAe,CAACW,cAAc,IAAG,GAAC,gB,GAEtE5I,YAAA,CAIqB8F,4BAAA;kBAHlB,aAAW,EAAErF,KAAA,CAAAwH,eAAe,CAACW,cAAc;kBAC5CxI,KAAK,EAAC,SAAS;kBACf2F,MAAM,EAAC;;gBApZ7B9E,CAAA;;cAAAA,CAAA;;YAAAA,CAAA;cA2ZYtB,mBAAA,UAAa,EACbK,YAAA,CAyBQsB,gBAAA;YAzBDC,IAAI,EAAC,IAAI;YAACC,EAAE,EAAC;;YA5ZhCV,OAAA,EAAAC,QAAA,CA6Zc,MAuBS,CAvBTf,YAAA,CAuBSyB,iBAAA;cAvBDP,OAAO,EAAC;YAAU;cA7ZxCJ,OAAA,EAAAC,QAAA,CA8ZgB,MAAiD,CAAjDf,YAAA,CAAiDyC,uBAAA;gBAAnClD,KAAK,EAAC;cAAS;gBA9Z7CuB,OAAA,EAAAC,QAAA,CA8Z8C,MAAIH,MAAA,SAAAA,MAAA,QA9ZlDN,gBAAA,CA8Z8C,MAAI,E;gBA9ZlDW,CAAA;gBAAAG,EAAA;kBA+ZgBpB,YAAA,CAoBc0B,sBAAA;gBAnb9BZ,OAAA,EAAAC,QAAA,CAgakB,MAkBS,CAlBTf,YAAA,CAkBS6I,iBAAA;kBAlBDC,OAAO,EAAC;gBAAS;kBAha3ChI,OAAA,EAAAC,QAAA,CAkasB,MAA6C,E,kBAD/CtB,mBAAA,CAgBcoH,SAAA,QAjblCC,WAAA,CAka2CrG,KAAA,CAAAsI,uBAAuB,EAArCC,UAAU;yCADnB1G,YAAA,CAgBc2G,sBAAA;sBAdXzJ,GAAG,EAAEwJ,UAAU,CAAC/B,EAAE;sBAClB,cAAY,EAAE+B,UAAU,CAAC9I,IAAI;sBAC7BkH,KAAK,EAAE4B,UAAU,CAAC5B,KAAK;sBACvB8B,QAAQ,EAAEF,UAAU,CAACG,WAAW;sBAChCxI,OAAK,EAAAE,MAAA,IAAE6D,QAAA,CAAA0E,eAAe,CAACJ,UAAU;;sBAEjBK,MAAM,EAAAtI,QAAA,CACrB,MAKS,CALTf,YAAA,CAKSgH,iBAAA;wBAJN5G,KAAK,EAAE4I,UAAU,CAACM,MAAM,0BAA0BN,UAAU,CAACM,MAAM;wBACpEnJ,IAAI,EAAC;;wBA5a/BW,OAAA,EAAAC,QAAA,CA8a0B,MAA2F,CA9arHT,gBAAA,CAAAU,gBAAA,CA8a6BgI,UAAU,CAACM,MAAM,sBAAsBN,UAAU,CAACM,MAAM,8C;wBA9arFrI,CAAA;;sBAAAA,CAAA;;;kBAAAA,CAAA;;gBAAAA,CAAA;;cAAAA,CAAA;;YAAAA,CAAA;cAubYtB,mBAAA,WAAc,EACdK,YAAA,CA+BQsB,gBAAA;YA/BDC,IAAI,EAAC;UAAI;YAxb5BT,OAAA,EAAAC,QAAA,CAybc,MA6BS,CA7BTf,YAAA,CA6BSyB,iBAAA;cA7BDP,OAAO,EAAC;YAAU;cAzbxCJ,OAAA,EAAAC,QAAA,CA0bgB,MAAkD,CAAlDf,YAAA,CAAkDyC,uBAAA;gBAApClD,KAAK,EAAC;cAAS;gBA1b7CuB,OAAA,EAAAC,QAAA,CA0b8C,MAAKH,MAAA,SAAAA,MAAA,QA1bnDN,gBAAA,CA0b8C,OAAK,E;gBA1bnDW,CAAA;gBAAAG,EAAA;kBA2bgBpB,YAAA,CA0Bc0B,sBAAA;gBArd9BZ,OAAA,EAAAC,QAAA,CA4bkB,MAwBQ,CAxBRf,YAAA,CAwBQqB,gBAAA;kBApd1BP,OAAA,EAAAC,QAAA,CA8bsB,MAAqC,E,kBADvCtB,mBAAA,CAsBQoH,SAAA,QAnd5BC,WAAA,CA8byCrG,KAAA,CAAA8I,iBAAiB,EAA7BC,QAAQ;yCADjBlH,YAAA,CAsBQhB,gBAAA;sBApBL9B,GAAG,EAAEgK,QAAQ,CAACvC,EAAE;sBACjB1F,IAAI,EAAC,IAAI;sBACTC,EAAE,EAAC;;sBAjczBV,OAAA,EAAAC,QAAA,CAmcsB,MAeS,CAfTf,YAAA,CAeSyB,iBAAA;wBAdPP,OAAO,EAAC,UAAU;wBAClB3B,KAAK,EAAC,wBAAwB;wBAC7BoB,OAAK,EAAAE,MAAA,IAAE6D,QAAA,CAAA+E,qBAAqB,CAACD,QAAQ;;wBAtc9D1I,OAAA,EAAAC,QAAA,CAwcwB,MASc,CATdf,YAAA,CASc0B,sBAAA;0BATDnC,KAAK,EAAC;wBAAa;0BAxcxDuB,OAAA,EAAAC,QAAA,CAyc0B,MAKU,CALVf,YAAA,CAKUC,iBAAA;4BAJPC,IAAI,EAAEsJ,QAAQ,CAACtJ,IAAI;4BACpBC,IAAI,EAAC,IAAI;4BACRC,KAAK,EAAEoJ,QAAQ,CAACpJ,KAAK;4BACtBb,KAAK,EAAC;sEAERK,mBAAA,CAAmD,OAAnD8J,WAAmD,EAAA1I,gBAAA,CAAtBwI,QAAQ,CAACtH,IAAI,kBAC1CtC,mBAAA,CAA+E,OAA/E+J,WAA+E,EAAA3I,gBAAA,CAA7BwI,QAAQ,CAACL,WAAW,iB;0BAhdhGlI,CAAA;;wBAAAA,CAAA;;sBAAAA,CAAA;;;kBAAAA,CAAA;;gBAAAA,CAAA;;cAAAA,CAAA;;YAAAA,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;UA2dQjB,YAAA,CAgBiB2H,yBAAA;QA3ezB7G,OAAA,EAAAC,QAAA,CA4dU,MAAqB,CAArBf,YAAA,CAAqB4H,mBAAA,GACrB5H,YAAA,CAMQQ,gBAAA;UALNJ,KAAK,EAAC,eAAe;UACrBc,OAAO,EAAC,MAAM;UACbP,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEJ,KAAA,CAAAU,wBAAwB;;UAhe5CL,OAAA,EAAAC,QAAA,CAieW,MAEDH,MAAA,SAAAA,MAAA,QAneVN,gBAAA,CAieW,MAED,E;UAneVW,CAAA;UAAAG,EAAA;YAoeUpB,YAAA,CAMQQ,gBAAA;UALNJ,KAAK,EAAC,SAAS;UACfc,OAAO,EAAC,MAAM;UACbP,OAAK,EAAE+D,QAAA,CAAAkF;;UAvepB9I,OAAA,EAAAC,QAAA,CAweW,MAEDH,MAAA,SAAAA,MAAA,QA1eVN,gBAAA,CAweW,QAED,E;UA1eVW,CAAA;UAAAG,EAAA;;QAAAH,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;qCA+eItB,mBAAA,YAAe,EACfK,YAAA,CAca6J,qBAAA;IA9fjBrG,UAAA,EAife/C,KAAA,CAAAqJ,YAAY;IAjf3B,uBAAAlJ,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAifeJ,KAAA,CAAAqJ,YAAY,GAAAjJ,MAAA;IACrBT,KAAK,EAAC;;IAIW2J,OAAO,EAAAhJ,QAAA,CACtB,MAKQ,CALRf,YAAA,CAKQQ,gBAAA;MAJNU,OAAO,EAAC,MAAM;MACbP,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEJ,KAAA,CAAAqJ,YAAY;;MAzf9BhJ,OAAA,EAAAC,QAAA,CA0fS,MAEDH,MAAA,SAAAA,MAAA,QA5fRN,gBAAA,CA0fS,MAED,E;MA5fRW,CAAA;MAAAG,EAAA;;IAAAN,OAAA,EAAAC,QAAA,CAofM,MAAkB,CApfxBT,gBAAA,CAAAU,gBAAA,CAofSP,KAAA,CAAAuJ,YAAY,IAAG,GAElB,gB;IAtfN/I,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}