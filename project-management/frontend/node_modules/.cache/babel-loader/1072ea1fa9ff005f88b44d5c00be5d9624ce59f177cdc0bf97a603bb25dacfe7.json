{"ast": null, "code": "export default {\n  name: 'ProfileView',\n  data() {\n    return {\n      editMode: false,\n      valid: true,\n      showUploadDialog: false,\n      avatarFile: null,\n      avatarPreview: null,\n      showSnackbar: false,\n      snackbarText: '',\n      showWorkflowOptimization: false,\n      nameRules: [v => !!v || '姓名不能为空', v => v.length <= 20 || '姓名不能超过20个字符'],\n      emailRules: [v => !!v || '邮箱不能为空', v => /.+@.+\\..+/.test(v) || '邮箱格式不正确'],\n      phoneRules: [v => !!v || '手机号码不能为空', v => /^1[3-9]\\d{9}$/.test(v) || '手机号码格式不正确'],\n      // 医疗部门选项\n      medicalDepartments: [{\n        title: '信息技术部',\n        value: '信息技术部'\n      }, {\n        title: '医疗信息部',\n        value: '医疗信息部'\n      }, {\n        title: '项目管理部',\n        value: '项目管理部'\n      }, {\n        title: '系统集成部',\n        value: '系统集成部'\n      }, {\n        title: '数据分析部',\n        value: '数据分析部'\n      }, {\n        title: '质量管理部',\n        value: '质量管理部'\n      }, {\n        title: '运维支持部',\n        value: '运维支持部'\n      }, {\n        title: '培训服务部',\n        value: '培训服务部'\n      }],\n      user: {\n        name: '李医生',\n        email: '<EMAIL>',\n        phone: '13800138000',\n        department: '医疗信息部',\n        position: '医疗项目经理',\n        joinDate: '2022-03-15',\n        address: '北京市朝阳区医疗科技园区A座',\n        bio: '拥有10年医疗信息化项目管理经验，专注于医院信息系统集成与数据迁移。擅长医疗流程优化，曾成功带领团队完成多个三甲医院的信息化建设项目。持有PMP项目管理认证和医疗信息化专业认证。',\n        avatar: 'https://ui-avatars.com/api/?name=李医生&background=1565C0&color=fff'\n      },\n      stats: {\n        projects: 15,\n        // 参与的医疗项目数量\n        tasks: 189,\n        // 完成的任务数量\n        meetings: 67,\n        // 参加的项目会议\n        documents: 45,\n        // 提交的项目文档\n        taskCompletionRate: 94,\n        // 任务完成率\n        onTimeRate: 91 // 按时完成率\n      },\n      // 工作流优化相关数据\n      workflowMetrics: {\n        taskProcessingSpeed: 8.5,\n        avgResponseTime: 2.3,\n        automationRate: 75\n      },\n      pendingItems: [{\n        id: 1,\n        title: '北京协和医院HIS系统审批',\n        priority: 'high',\n        type: 'approval'\n      }, {\n        id: 2,\n        title: '上海瑞金医院数据迁移验收',\n        priority: 'medium',\n        type: 'review'\n      }, {\n        id: 3,\n        title: '医疗设备接口测试确认',\n        priority: 'high',\n        type: 'confirmation'\n      }, {\n        id: 4,\n        title: '医院培训计划审核',\n        priority: 'low',\n        type: 'approval'\n      }],\n      optimizationSuggestions: [{\n        id: 1,\n        title: '启用任务自动分配',\n        description: '根据团队成员工作负载自动分配新任务',\n        icon: 'mdi-account-multiple-plus',\n        impact: 'high',\n        action: 'enable_auto_assignment'\n      }, {\n        id: 2,\n        title: '设置状态自动流转',\n        description: '任务完成后自动触发下一阶段',\n        icon: 'mdi-arrow-right-circle',\n        impact: 'high',\n        action: 'enable_auto_transition'\n      }, {\n        id: 3,\n        title: '优化通知频率',\n        description: '减少非关键通知，提高工作专注度',\n        icon: 'mdi-bell-outline',\n        impact: 'medium',\n        action: 'optimize_notifications'\n      }, {\n        id: 4,\n        title: '启用智能提醒',\n        description: '基于历史数据预测任务延期风险',\n        icon: 'mdi-brain',\n        impact: 'high',\n        action: 'enable_smart_reminders'\n      }],\n      workflowTemplates: [{\n        id: 1,\n        name: '医疗项目标准流程',\n        description: '适用于医疗项目的标准化工作流',\n        icon: 'mdi-hospital-box',\n        color: 'primary'\n      }, {\n        id: 2,\n        name: '敏捷开发流程',\n        description: '快速迭代的敏捷开发工作流',\n        icon: 'mdi-rocket-launch',\n        color: 'success'\n      }, {\n        id: 3,\n        name: '审批密集型流程',\n        description: '需要多层审批的严格工作流',\n        icon: 'mdi-shield-check',\n        color: 'warning'\n      }]\n    };\n  },\n  computed: {\n    workflowEfficiency() {\n      // 基于任务完成率、按时完成率和自动化率计算工作流效率\n      const taskWeight = 0.4;\n      const timeWeight = 0.3;\n      const autoWeight = 0.3;\n      return Math.round(this.stats.taskCompletionRate * taskWeight + this.stats.onTimeRate * timeWeight + this.workflowMetrics.automationRate * autoWeight);\n    }\n  },\n  watch: {\n    avatarFile(file) {\n      if (file) {\n        this.createImagePreview(file);\n      } else {\n        this.avatarPreview = null;\n      }\n    }\n  },\n  methods: {\n    saveProfile() {\n      // 在实际应用中，这里会发送请求到后端保存个人资料\n      this.editMode = false;\n      this.showSuccessMessage('个人资料保存成功');\n    },\n    createImagePreview(file) {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = e => {\n        this.avatarPreview = e.target.result;\n      };\n    },\n    uploadAvatar() {\n      // 在实际应用中，这里会发送请求到后端上传头像\n      if (this.avatarPreview) {\n        this.user.avatar = this.avatarPreview;\n      }\n      this.showUploadDialog = false;\n      this.avatarFile = null;\n      this.showSuccessMessage('头像上传成功');\n    },\n    showSuccessMessage(text) {\n      this.snackbarText = text;\n      this.showSnackbar = true;\n    },\n    // 工作流优化相关方法\n    getEfficiencyColor(efficiency) {\n      if (efficiency >= 85) return 'success';\n      if (efficiency >= 70) return 'warning';\n      return 'error';\n    },\n    handlePendingItem(item) {\n      // 处理待办事项\n      switch (item.type) {\n        case 'approval':\n          this.$router.push('/projects');\n          break;\n        case 'review':\n          this.$router.push('/kanban');\n          break;\n        case 'confirmation':\n          this.$router.push('/meetings');\n          break;\n      }\n      this.showSuccessMessage(`正在处理：${item.title}`);\n    },\n    applySuggestion(suggestion) {\n      // 应用优化建议\n      switch (suggestion.action) {\n        case 'enable_auto_assignment':\n          this.enableAutoAssignment();\n          break;\n        case 'enable_auto_transition':\n          this.enableAutoTransition();\n          break;\n        case 'optimize_notifications':\n          this.optimizeNotifications();\n          break;\n        case 'enable_smart_reminders':\n          this.enableSmartReminders();\n          break;\n      }\n      this.showSuccessMessage(`已应用优化建议：${suggestion.title}`);\n    },\n    applyWorkflowTemplate(template) {\n      // 应用工作流模板\n      this.showSuccessMessage(`正在应用工作流模板：${template.name}`);\n      // 这里可以调用API来应用模板\n    },\n    exportWorkflowReport() {\n      // 导出工作流报告\n      const reportData = {\n        user: this.user.name,\n        date: new Date().toLocaleDateString(),\n        efficiency: this.workflowEfficiency,\n        metrics: this.workflowMetrics,\n        suggestions: this.optimizationSuggestions.length\n      };\n\n      // 模拟导出功能\n      console.log('导出工作流报告:', reportData);\n      this.showSuccessMessage('工作流报告已导出');\n    },\n    // 优化功能实现\n    enableAutoAssignment() {\n      // 启用自动分配功能\n      this.workflowMetrics.automationRate = Math.min(100, this.workflowMetrics.automationRate + 10);\n    },\n    enableAutoTransition() {\n      // 启用自动流转功能\n      this.workflowMetrics.automationRate = Math.min(100, this.workflowMetrics.automationRate + 15);\n      this.workflowMetrics.avgResponseTime = Math.max(0.5, this.workflowMetrics.avgResponseTime - 0.5);\n    },\n    optimizeNotifications() {\n      // 优化通知设置\n      this.workflowMetrics.avgResponseTime = Math.max(0.5, this.workflowMetrics.avgResponseTime - 0.3);\n    },\n    enableSmartReminders() {\n      // 启用智能提醒\n      this.workflowMetrics.taskProcessingSpeed = Math.min(15, this.workflowMetrics.taskProcessingSpeed + 1.5);\n      this.stats.onTimeRate = Math.min(100, this.stats.onTimeRate + 5);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "editMode", "valid", "showUploadDialog", "avatar<PERSON>ile", "avatarPreview", "showSnackbar", "snackbarText", "showWorkflowOptimization", "nameRules", "v", "length", "emailRules", "test", "phoneRules", "medicalDepartments", "title", "value", "user", "email", "phone", "department", "position", "joinDate", "address", "bio", "avatar", "stats", "projects", "tasks", "meetings", "documents", "taskCompletionRate", "onTimeRate", "workflowMetrics", "taskProcessingSpeed", "avgResponseTime", "automationRate", "pendingItems", "id", "priority", "type", "optimizationSuggestions", "description", "icon", "impact", "action", "workflowTemplates", "color", "computed", "workflowEfficiency", "taskWeight", "timeWeight", "autoWeight", "Math", "round", "watch", "file", "createImagePreview", "methods", "saveProfile", "showSuccessMessage", "reader", "FileReader", "readAsDataURL", "onload", "e", "target", "result", "uploadAvatar", "text", "getEfficiencyColor", "efficiency", "handlePendingItem", "item", "$router", "push", "applySuggestion", "suggestion", "enableAutoAssignment", "enableAutoTransition", "optimizeNotifications", "enableSmartReminders", "applyWorkflowTemplate", "template", "exportWorkflowReport", "reportData", "date", "Date", "toLocaleDateString", "metrics", "suggestions", "console", "log", "min", "max"], "sources": ["/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/技术测试/项目实施进度管理/project-management/frontend/src/views/Profile.vue"], "sourcesContent": ["<template>\n  <div class=\"google-content\">\n    <!-- 页面头部 - Google风格 -->\n    <div class=\"medical-page-header mb-6\">\n      <div class=\"d-flex align-center justify-space-between\">\n        <div class=\"d-flex align-center\">\n          <v-icon\n            icon=\"mdi-account-circle\"\n            size=\"48\"\n            color=\"medical-primary\"\n            class=\"me-4\"\n          ></v-icon>\n          <div>\n            <h1 class=\"medical-gradient-text text-h4 mb-1\">个人资料</h1>\n            <p class=\"text-subtitle-1 text-medium-emphasis\">\n              <v-icon icon=\"mdi-hospital-box\" size=\"16\" class=\"me-1\"></v-icon>\n              医疗项目管理系统 - 个人信息管理\n            </p>\n          </div>\n        </div>\n        <div class=\"d-flex align-center ga-3\">\n          <v-btn\n            :color=\"editMode ? 'medical-warning' : 'medical-primary'\"\n            :prepend-icon=\"editMode ? 'mdi-close' : 'mdi-pencil'\"\n            class=\"medical-btn-primary\"\n            @click=\"editMode = !editMode\"\n          >\n            {{ editMode ? '取消编辑' : '编辑资料' }}\n          </v-btn>\n          <v-btn\n            color=\"medical-info\"\n            prepend-icon=\"mdi-chart-line\"\n            variant=\"outlined\"\n            @click=\"showWorkflowOptimization = true\"\n          >\n            工作流优化\n          </v-btn>\n        </div>\n      </div>\n    </div>\n\n    <v-row>\n      <v-col cols=\"12\" md=\"4\">\n        <!-- 用户信息卡片 - 医疗主题 -->\n        <v-card class=\"medical-card mb-4\">\n          <div class=\"medical-card-header pa-4\">\n            <div class=\"text-center\">\n              <v-avatar size=\"120\" class=\"mb-3\" color=\"white\">\n                <v-img :src=\"user.avatar\" alt=\"用户头像\">\n                  <template v-slot:placeholder>\n                    <v-icon icon=\"mdi-account\" size=\"60\" color=\"medical-primary\"></v-icon>\n                  </template>\n                </v-img>\n              </v-avatar>\n              <h2 class=\"text-h5 mb-1 text-white\">{{ user.name }}</h2>\n              <p class=\"text-body-1 text-white opacity-90\">{{ user.position }}</p>\n              <v-chip\n                color=\"white\"\n                text-color=\"medical-primary\"\n                size=\"small\"\n                class=\"mt-2\"\n              >\n                <v-icon start icon=\"mdi-hospital-box\"></v-icon>\n                医疗项目专家\n              </v-chip>\n            </div>\n          </div>\n\n          <v-card-text class=\"pa-4\">\n            <v-btn\n              v-if=\"editMode\"\n              color=\"medical-primary\"\n              variant=\"outlined\"\n              block\n              prepend-icon=\"mdi-camera\"\n              class=\"mb-3\"\n              @click=\"showUploadDialog = true\"\n            >\n              更换头像\n            </v-btn>\n\n            <!-- 快速统计 -->\n            <div class=\"text-center\">\n              <v-row dense>\n                <v-col cols=\"6\">\n                  <div class=\"text-h6 medical-gradient-text\">{{ stats.projects }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">参与项目</div>\n                </v-col>\n                <v-col cols=\"6\">\n                  <div class=\"text-h6 medical-gradient-text\">{{ workflowEfficiency }}%</div>\n                  <div class=\"text-caption text-medium-emphasis\">工作效率</div>\n                </v-col>\n              </v-row>\n            </div>\n          </v-card-text>\n        </v-card>\n\n        <!-- 联系方式卡片 - 医疗主题 -->\n        <v-card class=\"medical-card\">\n          <v-card-title class=\"d-flex align-center\">\n            <v-icon icon=\"mdi-card-account-details\" color=\"medical-primary\" class=\"me-2\"></v-icon>\n            联系方式\n          </v-card-title>\n          <v-card-text>\n            <v-list class=\"pa-0\">\n              <v-list-item class=\"px-0\">\n                <template v-slot:prepend>\n                  <v-avatar size=\"40\" color=\"medical-info\" class=\"me-3\">\n                    <v-icon icon=\"mdi-email\" color=\"white\"></v-icon>\n                  </v-avatar>\n                </template>\n                <v-list-item-title class=\"text-caption text-medium-emphasis\">邮箱地址</v-list-item-title>\n                <v-list-item-subtitle class=\"text-body-1 font-weight-medium\">{{ user.email }}</v-list-item-subtitle>\n              </v-list-item>\n\n              <v-list-item class=\"px-0\">\n                <template v-slot:prepend>\n                  <v-avatar size=\"40\" color=\"medical-success\" class=\"me-3\">\n                    <v-icon icon=\"mdi-phone\" color=\"white\"></v-icon>\n                  </v-avatar>\n                </template>\n                <v-list-item-title class=\"text-caption text-medium-emphasis\">手机号码</v-list-item-title>\n                <v-list-item-subtitle class=\"text-body-1 font-weight-medium\">{{ user.phone }}</v-list-item-subtitle>\n              </v-list-item>\n\n              <v-list-item class=\"px-0\">\n                <template v-slot:prepend>\n                  <v-avatar size=\"40\" color=\"medical-warning\" class=\"me-3\">\n                    <v-icon icon=\"mdi-hospital-building\" color=\"white\"></v-icon>\n                  </v-avatar>\n                </template>\n                <v-list-item-title class=\"text-caption text-medium-emphasis\">所属部门</v-list-item-title>\n                <v-list-item-subtitle class=\"text-body-1 font-weight-medium\">{{ user.department }}</v-list-item-subtitle>\n              </v-list-item>\n\n              <v-list-item class=\"px-0\">\n                <template v-slot:prepend>\n                  <v-avatar size=\"40\" color=\"medical-accent\" class=\"me-3\">\n                    <v-icon icon=\"mdi-map-marker\" color=\"white\"></v-icon>\n                  </v-avatar>\n                </template>\n                <v-list-item-title class=\"text-caption text-medium-emphasis\">工作地址</v-list-item-title>\n                <v-list-item-subtitle class=\"text-body-1 font-weight-medium\">{{ user.address }}</v-list-item-subtitle>\n              </v-list-item>\n            </v-list>\n          </v-card-text>\n        </v-card>\n      </v-col>\n\n      <v-col cols=\"12\" md=\"8\">\n        <!-- 个人资料表单 - 医疗主题 -->\n        <v-card class=\"medical-card mb-4\">\n          <v-card-title class=\"d-flex align-center\">\n            <v-icon icon=\"mdi-account-edit\" color=\"medical-primary\" class=\"me-2\"></v-icon>\n            个人资料\n            <v-spacer></v-spacer>\n            <v-chip\n              :color=\"editMode ? 'medical-warning' : 'medical-success'\"\n              size=\"small\"\n              variant=\"flat\"\n            >\n              <v-icon\n                :icon=\"editMode ? 'mdi-pencil' : 'mdi-lock'\"\n                start\n                size=\"16\"\n              ></v-icon>\n              {{ editMode ? '编辑模式' : '查看模式' }}\n            </v-chip>\n          </v-card-title>\n          <v-card-text>\n            <v-form ref=\"form\" v-model=\"valid\">\n              <v-row>\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.name\"\n                    label=\"姓名\"\n                    :rules=\"nameRules\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                    :color=\"editMode ? 'medical-primary' : 'grey'\"\n                    prepend-inner-icon=\"mdi-account\"\n                    required\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.email\"\n                    label=\"邮箱地址\"\n                    :rules=\"emailRules\"\n                    variant=\"outlined\"\n                    readonly\n                    disabled\n                    prepend-inner-icon=\"mdi-email\"\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.phone\"\n                    label=\"手机号码\"\n                    :rules=\"phoneRules\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                    :color=\"editMode ? 'medical-primary' : 'grey'\"\n                    prepend-inner-icon=\"mdi-phone\"\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-select\n                    v-model=\"user.department\"\n                    :items=\"medicalDepartments\"\n                    label=\"医疗部门\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                    :color=\"editMode ? 'medical-primary' : 'grey'\"\n                    prepend-inner-icon=\"mdi-hospital-building\"\n                  ></v-select>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.position\"\n                    label=\"职位\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                    :color=\"editMode ? 'medical-primary' : 'grey'\"\n                    prepend-inner-icon=\"mdi-badge-account\"\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.joinDate\"\n                    label=\"入职日期\"\n                    variant=\"outlined\"\n                    readonly\n                    disabled\n                    prepend-inner-icon=\"mdi-calendar\"\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\">\n                  <v-text-field\n                    v-model=\"user.address\"\n                    label=\"工作地址\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                    :color=\"editMode ? 'medical-primary' : 'grey'\"\n                    prepend-inner-icon=\"mdi-map-marker\"\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\">\n                  <v-textarea\n                    v-model=\"user.bio\"\n                    label=\"个人简介\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                    :color=\"editMode ? 'medical-primary' : 'grey'\"\n                    prepend-inner-icon=\"mdi-text-account\"\n                    auto-grow\n                    rows=\"3\"\n                  ></v-textarea>\n                </v-col>\n              </v-row>\n\n              <v-row v-if=\"editMode\">\n                <v-col cols=\"12\" class=\"d-flex justify-end ga-3\">\n                  <v-btn\n                    color=\"grey\"\n                    variant=\"outlined\"\n                    @click=\"editMode = false\"\n                  >\n                    取消\n                  </v-btn>\n                  <v-btn\n                    color=\"medical-primary\"\n                    class=\"medical-btn-primary\"\n                    :disabled=\"!valid\"\n                    @click=\"saveProfile\"\n                  >\n                    <v-icon start icon=\"mdi-content-save\"></v-icon>\n                    保存资料\n                  </v-btn>\n                </v-col>\n              </v-row>\n            </v-form>\n          </v-card-text>\n        </v-card>\n\n        <v-card>\n          <v-card-title>\n            <div class=\"d-flex align-center justify-space-between\">\n              <span>工作统计</span>\n              <v-btn\n                color=\"primary\"\n                variant=\"text\"\n                size=\"small\"\n                prepend-icon=\"mdi-chart-line\"\n                @click=\"showWorkflowOptimization = true\"\n              >\n                工作流优化\n              </v-btn>\n            </div>\n          </v-card-title>\n          <v-card-text>\n            <v-row>\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.projects }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">参与项目</div>\n                </div>\n              </v-col>\n\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.tasks }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">完成任务</div>\n                </div>\n              </v-col>\n\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.meetings }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">参加会议</div>\n                </div>\n              </v-col>\n\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.documents }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">提交文档</div>\n                </div>\n              </v-col>\n            </v-row>\n\n            <v-divider class=\"my-4\"></v-divider>\n\n            <div class=\"mb-4\">\n              <div class=\"d-flex align-center justify-space-between mb-2\">\n                <div class=\"text-body-1\">任务完成率</div>\n                <div class=\"text-body-1 font-weight-bold\">{{ stats.taskCompletionRate }}%</div>\n              </div>\n              <v-progress-linear\n                :model-value=\"stats.taskCompletionRate\"\n                color=\"success\"\n                height=\"8\"\n                rounded\n              ></v-progress-linear>\n            </div>\n\n            <div class=\"mb-4\">\n              <div class=\"d-flex align-center justify-space-between mb-2\">\n                <div class=\"text-body-1\">按时完成率</div>\n                <div class=\"text-body-1 font-weight-bold\">{{ stats.onTimeRate }}%</div>\n              </div>\n              <v-progress-linear\n                :model-value=\"stats.onTimeRate\"\n                color=\"info\"\n                height=\"8\"\n                rounded\n              ></v-progress-linear>\n            </div>\n\n            <!-- 工作流效率指标 -->\n            <div class=\"mb-4\">\n              <div class=\"d-flex align-center justify-space-between mb-2\">\n                <div class=\"text-body-1\">工作流效率</div>\n                <div class=\"text-body-1 font-weight-bold\">{{ workflowEfficiency }}%</div>\n              </div>\n              <v-progress-linear\n                :model-value=\"workflowEfficiency\"\n                :color=\"getEfficiencyColor(workflowEfficiency)\"\n                height=\"8\"\n                rounded\n              ></v-progress-linear>\n            </div>\n\n            <!-- 待处理事项 -->\n            <div v-if=\"pendingItems.length > 0\">\n              <v-divider class=\"my-4\"></v-divider>\n              <div class=\"text-subtitle-2 mb-2\">待处理事项</div>\n              <v-chip\n                v-for=\"item in pendingItems\"\n                :key=\"item.id\"\n                :color=\"item.priority === 'high' ? 'error' : item.priority === 'medium' ? 'warning' : 'info'\"\n                size=\"small\"\n                class=\"me-2 mb-2\"\n                @click=\"handlePendingItem(item)\"\n              >\n                {{ item.title }}\n              </v-chip>\n            </div>\n          </v-card-text>\n        </v-card>\n      </v-col>\n    </v-row>\n\n    <!-- 上传头像对话框 -->\n    <v-dialog v-model=\"showUploadDialog\" max-width=\"500\">\n      <v-card>\n        <v-card-title>上传头像</v-card-title>\n        <v-card-text>\n          <v-file-input\n            v-model=\"avatarFile\"\n            label=\"选择图片\"\n            accept=\"image/*\"\n            show-size\n            truncate-length=\"15\"\n            variant=\"outlined\"\n          ></v-file-input>\n\n          <div v-if=\"avatarPreview\" class=\"text-center mt-4\">\n            <v-avatar size=\"150\">\n              <v-img :src=\"avatarPreview\" alt=\"Avatar Preview\"></v-img>\n            </v-avatar>\n          </div>\n        </v-card-text>\n        <v-card-actions>\n          <v-spacer></v-spacer>\n          <v-btn\n            color=\"grey-darken-1\"\n            variant=\"text\"\n            @click=\"showUploadDialog = false\"\n          >\n            取消\n          </v-btn>\n          <v-btn\n            color=\"primary\"\n            variant=\"text\"\n            :disabled=\"!avatarFile\"\n            @click=\"uploadAvatar\"\n          >\n            上传\n          </v-btn>\n        </v-card-actions>\n      </v-card>\n    </v-dialog>\n\n    <!-- 工作流优化对话框 -->\n    <v-dialog v-model=\"showWorkflowOptimization\" max-width=\"1200\">\n      <v-card>\n        <v-card-title class=\"d-flex align-center\">\n          <v-icon icon=\"mdi-chart-line\" class=\"me-2\"></v-icon>\n          工作流优化分析\n        </v-card-title>\n\n        <v-card-text>\n          <v-row>\n            <!-- 工作流效率分析 -->\n            <v-col cols=\"12\" md=\"6\">\n              <v-card variant=\"outlined\">\n                <v-card-title class=\"text-h6\">效率分析</v-card-title>\n                <v-card-text>\n                  <div class=\"mb-4\">\n                    <div class=\"d-flex justify-space-between mb-2\">\n                      <span>任务处理速度</span>\n                      <span class=\"font-weight-bold\">{{ workflowMetrics.taskProcessingSpeed }}个/天</span>\n                    </div>\n                    <v-progress-linear\n                      :model-value=\"workflowMetrics.taskProcessingSpeed * 10\"\n                      color=\"primary\"\n                      height=\"6\"\n                    ></v-progress-linear>\n                  </div>\n\n                  <div class=\"mb-4\">\n                    <div class=\"d-flex justify-space-between mb-2\">\n                      <span>平均响应时间</span>\n                      <span class=\"font-weight-bold\">{{ workflowMetrics.avgResponseTime }}小时</span>\n                    </div>\n                    <v-progress-linear\n                      :model-value=\"Math.max(0, 100 - workflowMetrics.avgResponseTime * 5)\"\n                      color=\"info\"\n                      height=\"6\"\n                    ></v-progress-linear>\n                  </div>\n\n                  <div class=\"mb-4\">\n                    <div class=\"d-flex justify-space-between mb-2\">\n                      <span>工作流自动化率</span>\n                      <span class=\"font-weight-bold\">{{ workflowMetrics.automationRate }}%</span>\n                    </div>\n                    <v-progress-linear\n                      :model-value=\"workflowMetrics.automationRate\"\n                      color=\"success\"\n                      height=\"6\"\n                    ></v-progress-linear>\n                  </div>\n                </v-card-text>\n              </v-card>\n            </v-col>\n\n            <!-- 优化建议 -->\n            <v-col cols=\"12\" md=\"6\">\n              <v-card variant=\"outlined\">\n                <v-card-title class=\"text-h6\">优化建议</v-card-title>\n                <v-card-text>\n                  <v-list density=\"compact\">\n                    <v-list-item\n                      v-for=\"suggestion in optimizationSuggestions\"\n                      :key=\"suggestion.id\"\n                      :prepend-icon=\"suggestion.icon\"\n                      :title=\"suggestion.title\"\n                      :subtitle=\"suggestion.description\"\n                      @click=\"applySuggestion(suggestion)\"\n                    >\n                      <template v-slot:append>\n                        <v-chip\n                          :color=\"suggestion.impact === 'high' ? 'success' : suggestion.impact === 'medium' ? 'warning' : 'info'\"\n                          size=\"small\"\n                        >\n                          {{ suggestion.impact === 'high' ? '高效果' : suggestion.impact === 'medium' ? '中效果' : '低效果' }}\n                        </v-chip>\n                      </template>\n                    </v-list-item>\n                  </v-list>\n                </v-card-text>\n              </v-card>\n            </v-col>\n\n            <!-- 工作流模板 -->\n            <v-col cols=\"12\">\n              <v-card variant=\"outlined\">\n                <v-card-title class=\"text-h6\">工作流模板</v-card-title>\n                <v-card-text>\n                  <v-row>\n                    <v-col\n                      v-for=\"template in workflowTemplates\"\n                      :key=\"template.id\"\n                      cols=\"12\"\n                      md=\"4\"\n                    >\n                      <v-card\n                        variant=\"outlined\"\n                        class=\"workflow-template-card\"\n                        @click=\"applyWorkflowTemplate(template)\"\n                      >\n                        <v-card-text class=\"text-center\">\n                          <v-icon\n                            :icon=\"template.icon\"\n                            size=\"48\"\n                            :color=\"template.color\"\n                            class=\"mb-2\"\n                          ></v-icon>\n                          <div class=\"text-h6 mb-1\">{{ template.name }}</div>\n                          <div class=\"text-caption text-medium-emphasis\">{{ template.description }}</div>\n                        </v-card-text>\n                      </v-card>\n                    </v-col>\n                  </v-row>\n                </v-card-text>\n              </v-card>\n            </v-col>\n          </v-row>\n        </v-card-text>\n\n        <v-card-actions>\n          <v-spacer></v-spacer>\n          <v-btn\n            color=\"grey-darken-1\"\n            variant=\"text\"\n            @click=\"showWorkflowOptimization = false\"\n          >\n            关闭\n          </v-btn>\n          <v-btn\n            color=\"primary\"\n            variant=\"text\"\n            @click=\"exportWorkflowReport\"\n          >\n            导出报告\n          </v-btn>\n        </v-card-actions>\n      </v-card>\n    </v-dialog>\n\n    <!-- 操作成功提示 -->\n    <v-snackbar\n      v-model=\"showSnackbar\"\n      color=\"success\"\n    >\n      {{ snackbarText }}\n\n      <template v-slot:actions>\n        <v-btn\n          variant=\"text\"\n          @click=\"showSnackbar = false\"\n        >\n          关闭\n        </v-btn>\n      </template>\n    </v-snackbar>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ProfileView',\n  data() {\n    return {\n      editMode: false,\n      valid: true,\n      showUploadDialog: false,\n      avatarFile: null,\n      avatarPreview: null,\n      showSnackbar: false,\n      snackbarText: '',\n      showWorkflowOptimization: false,\n\n      nameRules: [\n        v => !!v || '姓名不能为空',\n        v => v.length <= 20 || '姓名不能超过20个字符'\n      ],\n      emailRules: [\n        v => !!v || '邮箱不能为空',\n        v => /.+@.+\\..+/.test(v) || '邮箱格式不正确'\n      ],\n      phoneRules: [\n        v => !!v || '手机号码不能为空',\n        v => /^1[3-9]\\d{9}$/.test(v) || '手机号码格式不正确'\n      ],\n\n      // 医疗部门选项\n      medicalDepartments: [\n        { title: '信息技术部', value: '信息技术部' },\n        { title: '医疗信息部', value: '医疗信息部' },\n        { title: '项目管理部', value: '项目管理部' },\n        { title: '系统集成部', value: '系统集成部' },\n        { title: '数据分析部', value: '数据分析部' },\n        { title: '质量管理部', value: '质量管理部' },\n        { title: '运维支持部', value: '运维支持部' },\n        { title: '培训服务部', value: '培训服务部' }\n      ],\n\n      user: {\n        name: '李医生',\n        email: '<EMAIL>',\n        phone: '13800138000',\n        department: '医疗信息部',\n        position: '医疗项目经理',\n        joinDate: '2022-03-15',\n        address: '北京市朝阳区医疗科技园区A座',\n        bio: '拥有10年医疗信息化项目管理经验，专注于医院信息系统集成与数据迁移。擅长医疗流程优化，曾成功带领团队完成多个三甲医院的信息化建设项目。持有PMP项目管理认证和医疗信息化专业认证。',\n        avatar: 'https://ui-avatars.com/api/?name=李医生&background=1565C0&color=fff'\n      },\n\n      stats: {\n        projects: 15,        // 参与的医疗项目数量\n        tasks: 189,          // 完成的任务数量\n        meetings: 67,        // 参加的项目会议\n        documents: 45,       // 提交的项目文档\n        taskCompletionRate: 94,  // 任务完成率\n        onTimeRate: 91       // 按时完成率\n      },\n\n      // 工作流优化相关数据\n      workflowMetrics: {\n        taskProcessingSpeed: 8.5,\n        avgResponseTime: 2.3,\n        automationRate: 75\n      },\n\n      pendingItems: [\n        { id: 1, title: '北京协和医院HIS系统审批', priority: 'high', type: 'approval' },\n        { id: 2, title: '上海瑞金医院数据迁移验收', priority: 'medium', type: 'review' },\n        { id: 3, title: '医疗设备接口测试确认', priority: 'high', type: 'confirmation' },\n        { id: 4, title: '医院培训计划审核', priority: 'low', type: 'approval' }\n      ],\n\n      optimizationSuggestions: [\n        {\n          id: 1,\n          title: '启用任务自动分配',\n          description: '根据团队成员工作负载自动分配新任务',\n          icon: 'mdi-account-multiple-plus',\n          impact: 'high',\n          action: 'enable_auto_assignment'\n        },\n        {\n          id: 2,\n          title: '设置状态自动流转',\n          description: '任务完成后自动触发下一阶段',\n          icon: 'mdi-arrow-right-circle',\n          impact: 'high',\n          action: 'enable_auto_transition'\n        },\n        {\n          id: 3,\n          title: '优化通知频率',\n          description: '减少非关键通知，提高工作专注度',\n          icon: 'mdi-bell-outline',\n          impact: 'medium',\n          action: 'optimize_notifications'\n        },\n        {\n          id: 4,\n          title: '启用智能提醒',\n          description: '基于历史数据预测任务延期风险',\n          icon: 'mdi-brain',\n          impact: 'high',\n          action: 'enable_smart_reminders'\n        }\n      ],\n\n      workflowTemplates: [\n        {\n          id: 1,\n          name: '医疗项目标准流程',\n          description: '适用于医疗项目的标准化工作流',\n          icon: 'mdi-hospital-box',\n          color: 'primary'\n        },\n        {\n          id: 2,\n          name: '敏捷开发流程',\n          description: '快速迭代的敏捷开发工作流',\n          icon: 'mdi-rocket-launch',\n          color: 'success'\n        },\n        {\n          id: 3,\n          name: '审批密集型流程',\n          description: '需要多层审批的严格工作流',\n          icon: 'mdi-shield-check',\n          color: 'warning'\n        }\n      ]\n    }\n  },\n  computed: {\n    workflowEfficiency() {\n      // 基于任务完成率、按时完成率和自动化率计算工作流效率\n      const taskWeight = 0.4\n      const timeWeight = 0.3\n      const autoWeight = 0.3\n\n      return Math.round(\n        this.stats.taskCompletionRate * taskWeight +\n        this.stats.onTimeRate * timeWeight +\n        this.workflowMetrics.automationRate * autoWeight\n      )\n    }\n  },\n  watch: {\n    avatarFile(file) {\n      if (file) {\n        this.createImagePreview(file)\n      } else {\n        this.avatarPreview = null\n      }\n    }\n  },\n  methods: {\n    saveProfile() {\n      // 在实际应用中，这里会发送请求到后端保存个人资料\n      this.editMode = false\n      this.showSuccessMessage('个人资料保存成功')\n    },\n    createImagePreview(file) {\n      const reader = new FileReader()\n      reader.readAsDataURL(file)\n      reader.onload = e => {\n        this.avatarPreview = e.target.result\n      }\n    },\n    uploadAvatar() {\n      // 在实际应用中，这里会发送请求到后端上传头像\n      if (this.avatarPreview) {\n        this.user.avatar = this.avatarPreview\n      }\n      this.showUploadDialog = false\n      this.avatarFile = null\n      this.showSuccessMessage('头像上传成功')\n    },\n    showSuccessMessage(text) {\n      this.snackbarText = text\n      this.showSnackbar = true\n    },\n\n    // 工作流优化相关方法\n    getEfficiencyColor(efficiency) {\n      if (efficiency >= 85) return 'success'\n      if (efficiency >= 70) return 'warning'\n      return 'error'\n    },\n\n    handlePendingItem(item) {\n      // 处理待办事项\n      switch (item.type) {\n        case 'approval':\n          this.$router.push('/projects')\n          break\n        case 'review':\n          this.$router.push('/kanban')\n          break\n        case 'confirmation':\n          this.$router.push('/meetings')\n          break\n      }\n      this.showSuccessMessage(`正在处理：${item.title}`)\n    },\n\n    applySuggestion(suggestion) {\n      // 应用优化建议\n      switch (suggestion.action) {\n        case 'enable_auto_assignment':\n          this.enableAutoAssignment()\n          break\n        case 'enable_auto_transition':\n          this.enableAutoTransition()\n          break\n        case 'optimize_notifications':\n          this.optimizeNotifications()\n          break\n        case 'enable_smart_reminders':\n          this.enableSmartReminders()\n          break\n      }\n      this.showSuccessMessage(`已应用优化建议：${suggestion.title}`)\n    },\n\n    applyWorkflowTemplate(template) {\n      // 应用工作流模板\n      this.showSuccessMessage(`正在应用工作流模板：${template.name}`)\n      // 这里可以调用API来应用模板\n    },\n\n    exportWorkflowReport() {\n      // 导出工作流报告\n      const reportData = {\n        user: this.user.name,\n        date: new Date().toLocaleDateString(),\n        efficiency: this.workflowEfficiency,\n        metrics: this.workflowMetrics,\n        suggestions: this.optimizationSuggestions.length\n      }\n\n      // 模拟导出功能\n      console.log('导出工作流报告:', reportData)\n      this.showSuccessMessage('工作流报告已导出')\n    },\n\n    // 优化功能实现\n    enableAutoAssignment() {\n      // 启用自动分配功能\n      this.workflowMetrics.automationRate = Math.min(100, this.workflowMetrics.automationRate + 10)\n    },\n\n    enableAutoTransition() {\n      // 启用自动流转功能\n      this.workflowMetrics.automationRate = Math.min(100, this.workflowMetrics.automationRate + 15)\n      this.workflowMetrics.avgResponseTime = Math.max(0.5, this.workflowMetrics.avgResponseTime - 0.5)\n    },\n\n    optimizeNotifications() {\n      // 优化通知设置\n      this.workflowMetrics.avgResponseTime = Math.max(0.5, this.workflowMetrics.avgResponseTime - 0.3)\n    },\n\n    enableSmartReminders() {\n      // 启用智能提醒\n      this.workflowMetrics.taskProcessingSpeed = Math.min(15, this.workflowMetrics.taskProcessingSpeed + 1.5)\n      this.stats.onTimeRate = Math.min(100, this.stats.onTimeRate + 5)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.workflow-template-card {\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.workflow-template-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.workflow-template-card .v-card-text {\n  padding: 24px;\n}\n\n/* 工作流效率指标样式 */\n.v-progress-linear {\n  border-radius: 4px;\n}\n\n/* 待处理事项样式 */\n.v-chip {\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.v-chip:hover {\n  transform: scale(1.05);\n}\n\n/* 优化建议列表样式 */\n.v-list-item {\n  border-radius: 8px;\n  margin-bottom: 8px;\n  transition: background-color 0.2s ease;\n}\n\n.v-list-item:hover {\n  background-color: rgba(0, 0, 0, 0.04);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .workflow-template-card .v-card-text {\n    padding: 16px;\n  }\n\n  .v-icon {\n    font-size: 36px !important;\n  }\n}\n</style>\n"], "mappings": "AAulBA,eAAe;EACbA,IAAI,EAAE,aAAa;EACnBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,KAAK;MACfC,KAAK,EAAE,IAAI;MACXC,gBAAgB,EAAE,KAAK;MACvBC,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE,EAAE;MAChBC,wBAAwB,EAAE,KAAK;MAE/BC,SAAS,EAAE,CACTC,CAAA,IAAK,CAAC,CAACA,CAAA,IAAK,QAAQ,EACpBA,CAAA,IAAKA,CAAC,CAACC,MAAK,IAAK,EAAC,IAAK,aAAY,CACpC;MACDC,UAAU,EAAE,CACVF,CAAA,IAAK,CAAC,CAACA,CAAA,IAAK,QAAQ,EACpBA,CAAA,IAAK,WAAW,CAACG,IAAI,CAACH,CAAC,KAAK,SAAQ,CACrC;MACDI,UAAU,EAAE,CACVJ,CAAA,IAAK,CAAC,CAACA,CAAA,IAAK,UAAU,EACtBA,CAAA,IAAK,eAAe,CAACG,IAAI,CAACH,CAAC,KAAK,WAAU,CAC3C;MAED;MACAK,kBAAkB,EAAE,CAClB;QAAEC,KAAK,EAAE,OAAO;QAAEC,KAAK,EAAE;MAAQ,CAAC,EAClC;QAAED,KAAK,EAAE,OAAO;QAAEC,KAAK,EAAE;MAAQ,CAAC,EAClC;QAAED,KAAK,EAAE,OAAO;QAAEC,KAAK,EAAE;MAAQ,CAAC,EAClC;QAAED,KAAK,EAAE,OAAO;QAAEC,KAAK,EAAE;MAAQ,CAAC,EAClC;QAAED,KAAK,EAAE,OAAO;QAAEC,KAAK,EAAE;MAAQ,CAAC,EAClC;QAAED,KAAK,EAAE,OAAO;QAAEC,KAAK,EAAE;MAAQ,CAAC,EAClC;QAAED,KAAK,EAAE,OAAO;QAAEC,KAAK,EAAE;MAAQ,CAAC,EAClC;QAAED,KAAK,EAAE,OAAO;QAAEC,KAAK,EAAE;MAAQ,EAClC;MAEDC,IAAI,EAAE;QACJnB,IAAI,EAAE,KAAK;QACXoB,KAAK,EAAE,kBAAkB;QACzBC,KAAK,EAAE,aAAa;QACpBC,UAAU,EAAE,OAAO;QACnBC,QAAQ,EAAE,QAAQ;QAClBC,QAAQ,EAAE,YAAY;QACtBC,OAAO,EAAE,gBAAgB;QACzBC,GAAG,EAAE,2FAA2F;QAChGC,MAAM,EAAE;MACV,CAAC;MAEDC,KAAK,EAAE;QACLC,QAAQ,EAAE,EAAE;QAAS;QACrBC,KAAK,EAAE,GAAG;QAAW;QACrBC,QAAQ,EAAE,EAAE;QAAS;QACrBC,SAAS,EAAE,EAAE;QAAQ;QACrBC,kBAAkB,EAAE,EAAE;QAAG;QACzBC,UAAU,EAAE,EAAC,CAAQ;MACvB,CAAC;MAED;MACAC,eAAe,EAAE;QACfC,mBAAmB,EAAE,GAAG;QACxBC,eAAe,EAAE,GAAG;QACpBC,cAAc,EAAE;MAClB,CAAC;MAEDC,YAAY,EAAE,CACZ;QAAEC,EAAE,EAAE,CAAC;QAAEvB,KAAK,EAAE,eAAe;QAAEwB,QAAQ,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAW,CAAC,EACrE;QAAEF,EAAE,EAAE,CAAC;QAAEvB,KAAK,EAAE,cAAc;QAAEwB,QAAQ,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAS,CAAC,EACpE;QAAEF,EAAE,EAAE,CAAC;QAAEvB,KAAK,EAAE,YAAY;QAAEwB,QAAQ,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAe,CAAC,EACtE;QAAEF,EAAE,EAAE,CAAC;QAAEvB,KAAK,EAAE,UAAU;QAAEwB,QAAQ,EAAE,KAAK;QAAEC,IAAI,EAAE;MAAW,EAC/D;MAEDC,uBAAuB,EAAE,CACvB;QACEH,EAAE,EAAE,CAAC;QACLvB,KAAK,EAAE,UAAU;QACjB2B,WAAW,EAAE,mBAAmB;QAChCC,IAAI,EAAE,2BAA2B;QACjCC,MAAM,EAAE,MAAM;QACdC,MAAM,EAAE;MACV,CAAC,EACD;QACEP,EAAE,EAAE,CAAC;QACLvB,KAAK,EAAE,UAAU;QACjB2B,WAAW,EAAE,eAAe;QAC5BC,IAAI,EAAE,wBAAwB;QAC9BC,MAAM,EAAE,MAAM;QACdC,MAAM,EAAE;MACV,CAAC,EACD;QACEP,EAAE,EAAE,CAAC;QACLvB,KAAK,EAAE,QAAQ;QACf2B,WAAW,EAAE,iBAAiB;QAC9BC,IAAI,EAAE,kBAAkB;QACxBC,MAAM,EAAE,QAAQ;QAChBC,MAAM,EAAE;MACV,CAAC,EACD;QACEP,EAAE,EAAE,CAAC;QACLvB,KAAK,EAAE,QAAQ;QACf2B,WAAW,EAAE,gBAAgB;QAC7BC,IAAI,EAAE,WAAW;QACjBC,MAAM,EAAE,MAAM;QACdC,MAAM,EAAE;MACV,EACD;MAEDC,iBAAiB,EAAE,CACjB;QACER,EAAE,EAAE,CAAC;QACLxC,IAAI,EAAE,UAAU;QAChB4C,WAAW,EAAE,gBAAgB;QAC7BC,IAAI,EAAE,kBAAkB;QACxBI,KAAK,EAAE;MACT,CAAC,EACD;QACET,EAAE,EAAE,CAAC;QACLxC,IAAI,EAAE,QAAQ;QACd4C,WAAW,EAAE,cAAc;QAC3BC,IAAI,EAAE,mBAAmB;QACzBI,KAAK,EAAE;MACT,CAAC,EACD;QACET,EAAE,EAAE,CAAC;QACLxC,IAAI,EAAE,SAAS;QACf4C,WAAW,EAAE,cAAc;QAC3BC,IAAI,EAAE,kBAAkB;QACxBI,KAAK,EAAE;MACT;IAEJ;EACF,CAAC;EACDC,QAAQ,EAAE;IACRC,kBAAkBA,CAAA,EAAG;MACnB;MACA,MAAMC,UAAS,GAAI,GAAE;MACrB,MAAMC,UAAS,GAAI,GAAE;MACrB,MAAMC,UAAS,GAAI,GAAE;MAErB,OAAOC,IAAI,CAACC,KAAK,CACf,IAAI,CAAC5B,KAAK,CAACK,kBAAiB,GAAImB,UAAS,GACzC,IAAI,CAACxB,KAAK,CAACM,UAAS,GAAImB,UAAS,GACjC,IAAI,CAAClB,eAAe,CAACG,cAAa,GAAIgB,UACxC;IACF;EACF,CAAC;EACDG,KAAK,EAAE;IACLpD,UAAUA,CAACqD,IAAI,EAAE;MACf,IAAIA,IAAI,EAAE;QACR,IAAI,CAACC,kBAAkB,CAACD,IAAI;MAC9B,OAAO;QACL,IAAI,CAACpD,aAAY,GAAI,IAAG;MAC1B;IACF;EACF,CAAC;EACDsD,OAAO,EAAE;IACPC,WAAWA,CAAA,EAAG;MACZ;MACA,IAAI,CAAC3D,QAAO,GAAI,KAAI;MACpB,IAAI,CAAC4D,kBAAkB,CAAC,UAAU;IACpC,CAAC;IACDH,kBAAkBA,CAACD,IAAI,EAAE;MACvB,MAAMK,MAAK,GAAI,IAAIC,UAAU,CAAC;MAC9BD,MAAM,CAACE,aAAa,CAACP,IAAI;MACzBK,MAAM,CAACG,MAAK,GAAIC,CAAA,IAAK;QACnB,IAAI,CAAC7D,aAAY,GAAI6D,CAAC,CAACC,MAAM,CAACC,MAAK;MACrC;IACF,CAAC;IACDC,YAAYA,CAAA,EAAG;MACb;MACA,IAAI,IAAI,CAAChE,aAAa,EAAE;QACtB,IAAI,CAACa,IAAI,CAACQ,MAAK,GAAI,IAAI,CAACrB,aAAY;MACtC;MACA,IAAI,CAACF,gBAAe,GAAI,KAAI;MAC5B,IAAI,CAACC,UAAS,GAAI,IAAG;MACrB,IAAI,CAACyD,kBAAkB,CAAC,QAAQ;IAClC,CAAC;IACDA,kBAAkBA,CAACS,IAAI,EAAE;MACvB,IAAI,CAAC/D,YAAW,GAAI+D,IAAG;MACvB,IAAI,CAAChE,YAAW,GAAI,IAAG;IACzB,CAAC;IAED;IACAiE,kBAAkBA,CAACC,UAAU,EAAE;MAC7B,IAAIA,UAAS,IAAK,EAAE,EAAE,OAAO,SAAQ;MACrC,IAAIA,UAAS,IAAK,EAAE,EAAE,OAAO,SAAQ;MACrC,OAAO,OAAM;IACf,CAAC;IAEDC,iBAAiBA,CAACC,IAAI,EAAE;MACtB;MACA,QAAQA,IAAI,CAACjC,IAAI;QACf,KAAK,UAAU;UACb,IAAI,CAACkC,OAAO,CAACC,IAAI,CAAC,WAAW;UAC7B;QACF,KAAK,QAAQ;UACX,IAAI,CAACD,OAAO,CAACC,IAAI,CAAC,SAAS;UAC3B;QACF,KAAK,cAAc;UACjB,IAAI,CAACD,OAAO,CAACC,IAAI,CAAC,WAAW;UAC7B;MACJ;MACA,IAAI,CAACf,kBAAkB,CAAC,QAAQa,IAAI,CAAC1D,KAAK,EAAE;IAC9C,CAAC;IAED6D,eAAeA,CAACC,UAAU,EAAE;MAC1B;MACA,QAAQA,UAAU,CAAChC,MAAM;QACvB,KAAK,wBAAwB;UAC3B,IAAI,CAACiC,oBAAoB,CAAC;UAC1B;QACF,KAAK,wBAAwB;UAC3B,IAAI,CAACC,oBAAoB,CAAC;UAC1B;QACF,KAAK,wBAAwB;UAC3B,IAAI,CAACC,qBAAqB,CAAC;UAC3B;QACF,KAAK,wBAAwB;UAC3B,IAAI,CAACC,oBAAoB,CAAC;UAC1B;MACJ;MACA,IAAI,CAACrB,kBAAkB,CAAC,WAAWiB,UAAU,CAAC9D,KAAK,EAAE;IACvD,CAAC;IAEDmE,qBAAqBA,CAACC,QAAQ,EAAE;MAC9B;MACA,IAAI,CAACvB,kBAAkB,CAAC,aAAauB,QAAQ,CAACrF,IAAI,EAAE;MACpD;IACF,CAAC;IAEDsF,oBAAoBA,CAAA,EAAG;MACrB;MACA,MAAMC,UAAS,GAAI;QACjBpE,IAAI,EAAE,IAAI,CAACA,IAAI,CAACnB,IAAI;QACpBwF,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;QACrCjB,UAAU,EAAE,IAAI,CAACtB,kBAAkB;QACnCwC,OAAO,EAAE,IAAI,CAACxD,eAAe;QAC7ByD,WAAW,EAAE,IAAI,CAACjD,uBAAuB,CAAC/B;MAC5C;;MAEA;MACAiF,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEP,UAAU;MAClC,IAAI,CAACzB,kBAAkB,CAAC,UAAU;IACpC,CAAC;IAED;IACAkB,oBAAoBA,CAAA,EAAG;MACrB;MACA,IAAI,CAAC7C,eAAe,CAACG,cAAa,GAAIiB,IAAI,CAACwC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC5D,eAAe,CAACG,cAAa,GAAI,EAAE;IAC9F,CAAC;IAED2C,oBAAoBA,CAAA,EAAG;MACrB;MACA,IAAI,CAAC9C,eAAe,CAACG,cAAa,GAAIiB,IAAI,CAACwC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC5D,eAAe,CAACG,cAAa,GAAI,EAAE;MAC5F,IAAI,CAACH,eAAe,CAACE,eAAc,GAAIkB,IAAI,CAACyC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC7D,eAAe,CAACE,eAAc,GAAI,GAAG;IACjG,CAAC;IAED6C,qBAAqBA,CAAA,EAAG;MACtB;MACA,IAAI,CAAC/C,eAAe,CAACE,eAAc,GAAIkB,IAAI,CAACyC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC7D,eAAe,CAACE,eAAc,GAAI,GAAG;IACjG,CAAC;IAED8C,oBAAoBA,CAAA,EAAG;MACrB;MACA,IAAI,CAAChD,eAAe,CAACC,mBAAkB,GAAImB,IAAI,CAACwC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC5D,eAAe,CAACC,mBAAkB,GAAI,GAAG;MACtG,IAAI,CAACR,KAAK,CAACM,UAAS,GAAIqB,IAAI,CAACwC,GAAG,CAAC,GAAG,EAAE,IAAI,CAACnE,KAAK,CAACM,UAAS,GAAI,CAAC;IACjE;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}