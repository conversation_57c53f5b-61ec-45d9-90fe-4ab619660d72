{"ast": null, "code": "import { createStore } from 'vuex';\nimport createPersistedState from 'vuex-persistedstate';\nimport auth from './modules/auth';\nimport projects from './modules/projects';\nimport phases from './modules/phases';\nimport tasks from './modules/tasks';\nimport risks from './modules/risks';\nimport meetings from './modules/meetings';\nimport users from './modules/users';\nimport relationships from './modules/relationships';\nimport dashboard from './modules/dashboard';\nimport workflow from './modules/workflow';\nexport default createStore({\n  modules: {\n    auth,\n    projects,\n    phases,\n    tasks,\n    risks,\n    meetings,\n    users,\n    relationships,\n    dashboard\n  },\n  plugins: [createPersistedState({\n    key: 'project-management-system',\n    paths: ['auth.token', 'auth.user']\n  })],\n  // 全局状态\n  state: {\n    loading: false,\n    error: null,\n    success: null,\n    notifications: []\n  },\n  // 用于更改状态的同步函数\n  mutations: {\n    SET_LOADING(state, isLoading) {\n      state.loading = isLoading;\n    },\n    SET_ERROR(state, error) {\n      state.error = error;\n    },\n    CLEAR_ERROR(state) {\n      state.error = null;\n    },\n    SET_SUCCESS(state, message) {\n      state.success = message;\n    },\n    CLEAR_SUCCESS(state) {\n      state.success = null;\n    },\n    ADD_NOTIFICATION(state, notification) {\n      state.notifications.push(notification);\n    },\n    REMOVE_NOTIFICATION(state, id) {\n      state.notifications = state.notifications.filter(n => n.id !== id);\n    },\n    CLEAR_NOTIFICATIONS(state) {\n      state.notifications = [];\n    }\n  },\n  // 用于业务逻辑处理的异步函数\n  actions: {\n    setLoading({\n      commit\n    }, isLoading) {\n      commit('SET_LOADING', isLoading);\n    },\n    setError({\n      commit\n    }, error) {\n      commit('SET_ERROR', error);\n      // 自动清除错误\n      setTimeout(() => {\n        commit('CLEAR_ERROR');\n      }, 5000);\n    },\n    clearError({\n      commit\n    }) {\n      commit('CLEAR_ERROR');\n    },\n    setSuccess({\n      commit\n    }, message) {\n      commit('SET_SUCCESS', message);\n      // 自动清除成功消息\n      setTimeout(() => {\n        commit('CLEAR_SUCCESS');\n      }, 5000);\n    },\n    clearSuccess({\n      commit\n    }) {\n      commit('CLEAR_SUCCESS');\n    },\n    addNotification({\n      commit\n    }, notification) {\n      const id = Date.now();\n      commit('ADD_NOTIFICATION', {\n        ...notification,\n        id\n      });\n      // 自动移除通知\n      setTimeout(() => {\n        commit('REMOVE_NOTIFICATION', id);\n      }, notification.timeout || 5000);\n      return id;\n    },\n    removeNotification({\n      commit\n    }, id) {\n      commit('REMOVE_NOTIFICATION', id);\n    },\n    clearNotifications({\n      commit\n    }) {\n      commit('CLEAR_NOTIFICATIONS');\n    }\n  },\n  // 用于从状态派生数据的计算属性\n  getters: {\n    isLoading: state => state.loading,\n    error: state => state.error,\n    success: state => state.success,\n    notifications: state => state.notifications\n  }\n});", "map": {"version": 3, "names": ["createStore", "createPersistedState", "auth", "projects", "phases", "tasks", "risks", "meetings", "users", "relationships", "dashboard", "workflow", "modules", "plugins", "key", "paths", "state", "loading", "error", "success", "notifications", "mutations", "SET_LOADING", "isLoading", "SET_ERROR", "CLEAR_ERROR", "SET_SUCCESS", "message", "CLEAR_SUCCESS", "ADD_NOTIFICATION", "notification", "push", "REMOVE_NOTIFICATION", "id", "filter", "n", "CLEAR_NOTIFICATIONS", "actions", "setLoading", "commit", "setError", "setTimeout", "clearError", "setSuccess", "clearSuccess", "addNotification", "Date", "now", "timeout", "removeNotification", "clearNotifications", "getters"], "sources": ["/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/技术测试/项目实施进度管理/project-management/frontend/src/store/index.js"], "sourcesContent": ["import { createStore } from 'vuex'\nimport createPersistedState from 'vuex-persistedstate'\nimport auth from './modules/auth'\nimport projects from './modules/projects'\nimport phases from './modules/phases'\nimport tasks from './modules/tasks'\nimport risks from './modules/risks'\nimport meetings from './modules/meetings'\nimport users from './modules/users'\nimport relationships from './modules/relationships'\nimport dashboard from './modules/dashboard'\nimport workflow from './modules/workflow'\n\nexport default createStore({\n  modules: {\n    auth,\n    projects,\n    phases,\n    tasks,\n    risks,\n    meetings,\n    users,\n    relationships,\n    dashboard\n  },\n  plugins: [\n    createPersistedState({\n      key: 'project-management-system',\n      paths: ['auth.token', 'auth.user']\n    })\n  ],\n  // 全局状态\n  state: {\n    loading: false,\n    error: null,\n    success: null,\n    notifications: []\n  },\n  // 用于更改状态的同步函数\n  mutations: {\n    SET_LOADING(state, isLoading) {\n      state.loading = isLoading\n    },\n    SET_ERROR(state, error) {\n      state.error = error\n    },\n    CLEAR_ERROR(state) {\n      state.error = null\n    },\n    SET_SUCCESS(state, message) {\n      state.success = message\n    },\n    CLEAR_SUCCESS(state) {\n      state.success = null\n    },\n    ADD_NOTIFICATION(state, notification) {\n      state.notifications.push(notification)\n    },\n    REMOVE_NOTIFICATION(state, id) {\n      state.notifications = state.notifications.filter(n => n.id !== id)\n    },\n    CLEAR_NOTIFICATIONS(state) {\n      state.notifications = []\n    }\n  },\n  // 用于业务逻辑处理的异步函数\n  actions: {\n    setLoading({ commit }, isLoading) {\n      commit('SET_LOADING', isLoading)\n    },\n    setError({ commit }, error) {\n      commit('SET_ERROR', error)\n      // 自动清除错误\n      setTimeout(() => {\n        commit('CLEAR_ERROR')\n      }, 5000)\n    },\n    clearError({ commit }) {\n      commit('CLEAR_ERROR')\n    },\n    setSuccess({ commit }, message) {\n      commit('SET_SUCCESS', message)\n      // 自动清除成功消息\n      setTimeout(() => {\n        commit('CLEAR_SUCCESS')\n      }, 5000)\n    },\n    clearSuccess({ commit }) {\n      commit('CLEAR_SUCCESS')\n    },\n    addNotification({ commit }, notification) {\n      const id = Date.now()\n      commit('ADD_NOTIFICATION', { ...notification, id })\n      // 自动移除通知\n      setTimeout(() => {\n        commit('REMOVE_NOTIFICATION', id)\n      }, notification.timeout || 5000)\n      return id\n    },\n    removeNotification({ commit }, id) {\n      commit('REMOVE_NOTIFICATION', id)\n    },\n    clearNotifications({ commit }) {\n      commit('CLEAR_NOTIFICATIONS')\n    }\n  },\n  // 用于从状态派生数据的计算属性\n  getters: {\n    isLoading: state => state.loading,\n    error: state => state.error,\n    success: state => state.success,\n    notifications: state => state.notifications\n  }\n})\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,MAAM;AAClC,OAAOC,oBAAoB,MAAM,qBAAqB;AACtD,OAAOC,IAAI,MAAM,gBAAgB;AACjC,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,QAAQ,MAAM,oBAAoB;AAEzC,eAAeX,WAAW,CAAC;EACzBY,OAAO,EAAE;IACPV,IAAI;IACJC,QAAQ;IACRC,MAAM;IACNC,KAAK;IACLC,KAAK;IACLC,QAAQ;IACRC,KAAK;IACLC,aAAa;IACbC;EACF,CAAC;EACDG,OAAO,EAAE,CACPZ,oBAAoB,CAAC;IACnBa,GAAG,EAAE,2BAA2B;IAChCC,KAAK,EAAE,CAAC,YAAY,EAAE,WAAW;EACnC,CAAC,CAAC,CACH;EACD;EACAC,KAAK,EAAE;IACLC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE,IAAI;IACbC,aAAa,EAAE;EACjB,CAAC;EACD;EACAC,SAAS,EAAE;IACTC,WAAWA,CAACN,KAAK,EAAEO,SAAS,EAAE;MAC5BP,KAAK,CAACC,OAAO,GAAGM,SAAS;IAC3B,CAAC;IACDC,SAASA,CAACR,KAAK,EAAEE,KAAK,EAAE;MACtBF,KAAK,CAACE,KAAK,GAAGA,KAAK;IACrB,CAAC;IACDO,WAAWA,CAACT,KAAK,EAAE;MACjBA,KAAK,CAACE,KAAK,GAAG,IAAI;IACpB,CAAC;IACDQ,WAAWA,CAACV,KAAK,EAAEW,OAAO,EAAE;MAC1BX,KAAK,CAACG,OAAO,GAAGQ,OAAO;IACzB,CAAC;IACDC,aAAaA,CAACZ,KAAK,EAAE;MACnBA,KAAK,CAACG,OAAO,GAAG,IAAI;IACtB,CAAC;IACDU,gBAAgBA,CAACb,KAAK,EAAEc,YAAY,EAAE;MACpCd,KAAK,CAACI,aAAa,CAACW,IAAI,CAACD,YAAY,CAAC;IACxC,CAAC;IACDE,mBAAmBA,CAAChB,KAAK,EAAEiB,EAAE,EAAE;MAC7BjB,KAAK,CAACI,aAAa,GAAGJ,KAAK,CAACI,aAAa,CAACc,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACF,EAAE,KAAKA,EAAE,CAAC;IACpE,CAAC;IACDG,mBAAmBA,CAACpB,KAAK,EAAE;MACzBA,KAAK,CAACI,aAAa,GAAG,EAAE;IAC1B;EACF,CAAC;EACD;EACAiB,OAAO,EAAE;IACPC,UAAUA,CAAC;MAAEC;IAAO,CAAC,EAAEhB,SAAS,EAAE;MAChCgB,MAAM,CAAC,aAAa,EAAEhB,SAAS,CAAC;IAClC,CAAC;IACDiB,QAAQA,CAAC;MAAED;IAAO,CAAC,EAAErB,KAAK,EAAE;MAC1BqB,MAAM,CAAC,WAAW,EAAErB,KAAK,CAAC;MAC1B;MACAuB,UAAU,CAAC,MAAM;QACfF,MAAM,CAAC,aAAa,CAAC;MACvB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IACDG,UAAUA,CAAC;MAAEH;IAAO,CAAC,EAAE;MACrBA,MAAM,CAAC,aAAa,CAAC;IACvB,CAAC;IACDI,UAAUA,CAAC;MAAEJ;IAAO,CAAC,EAAEZ,OAAO,EAAE;MAC9BY,MAAM,CAAC,aAAa,EAAEZ,OAAO,CAAC;MAC9B;MACAc,UAAU,CAAC,MAAM;QACfF,MAAM,CAAC,eAAe,CAAC;MACzB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IACDK,YAAYA,CAAC;MAAEL;IAAO,CAAC,EAAE;MACvBA,MAAM,CAAC,eAAe,CAAC;IACzB,CAAC;IACDM,eAAeA,CAAC;MAAEN;IAAO,CAAC,EAAET,YAAY,EAAE;MACxC,MAAMG,EAAE,GAAGa,IAAI,CAACC,GAAG,CAAC,CAAC;MACrBR,MAAM,CAAC,kBAAkB,EAAE;QAAE,GAAGT,YAAY;QAAEG;MAAG,CAAC,CAAC;MACnD;MACAQ,UAAU,CAAC,MAAM;QACfF,MAAM,CAAC,qBAAqB,EAAEN,EAAE,CAAC;MACnC,CAAC,EAAEH,YAAY,CAACkB,OAAO,IAAI,IAAI,CAAC;MAChC,OAAOf,EAAE;IACX,CAAC;IACDgB,kBAAkBA,CAAC;MAAEV;IAAO,CAAC,EAAEN,EAAE,EAAE;MACjCM,MAAM,CAAC,qBAAqB,EAAEN,EAAE,CAAC;IACnC,CAAC;IACDiB,kBAAkBA,CAAC;MAAEX;IAAO,CAAC,EAAE;MAC7BA,MAAM,CAAC,qBAAqB,CAAC;IAC/B;EACF,CAAC;EACD;EACAY,OAAO,EAAE;IACP5B,SAAS,EAAEP,KAAK,IAAIA,KAAK,CAACC,OAAO;IACjCC,KAAK,EAAEF,KAAK,IAAIA,KAAK,CAACE,KAAK;IAC3BC,OAAO,EAAEH,KAAK,IAAIA,KAAK,CAACG,OAAO;IAC/BC,aAAa,EAAEJ,KAAK,IAAIA,KAAK,CAACI;EAChC;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}