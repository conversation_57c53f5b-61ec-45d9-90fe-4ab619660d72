{"ast": null, "code": "export default {\n  name: 'ProfileView',\n  data() {\n    return {\n      editMode: false,\n      valid: true,\n      showUploadDialog: false,\n      avatarFile: null,\n      avatarPreview: null,\n      showSnackbar: false,\n      snackbarText: '',\n      nameRules: [v => !!v || '姓名不能为空', v => v.length <= 20 || '姓名不能超过20个字符'],\n      emailRules: [v => !!v || '邮箱不能为空', v => /.+@.+\\..+/.test(v) || '邮箱格式不正确'],\n      phoneRules: [v => !!v || '手机号码不能为空', v => /^1[3-9]\\d{9}$/.test(v) || '手机号码格式不正确'],\n      departments: ['技术部', '市场部', '销售部', '人力资源部', '财务部'],\n      user: {\n        name: '张三',\n        email: '<EMAIL>',\n        phone: '13800138000',\n        department: '技术部',\n        position: '项目经理',\n        joinDate: '2023-01-15',\n        address: '上海市浦东新区张江高科技园区',\n        bio: '拥有8年项目管理经验，专注于软件开发领域。擅长敏捷开发方法，曾成功带领团队完成多个大型项目。',\n        avatar: 'https://ui-avatars.com/api/?name=张三&background=random'\n      },\n      stats: {\n        projects: 12,\n        tasks: 156,\n        meetings: 48,\n        documents: 32,\n        taskCompletionRate: 92,\n        onTimeRate: 88\n      }\n    };\n  },\n  watch: {\n    avatarFile(file) {\n      if (file) {\n        this.createImagePreview(file);\n      } else {\n        this.avatarPreview = null;\n      }\n    }\n  },\n  methods: {\n    saveProfile() {\n      // 在实际应用中，这里会发送请求到后端保存个人资料\n      this.editMode = false;\n      this.showSuccessMessage('个人资料保存成功');\n    },\n    createImagePreview(file) {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = e => {\n        this.avatarPreview = e.target.result;\n      };\n    },\n    uploadAvatar() {\n      // 在实际应用中，这里会发送请求到后端上传头像\n      if (this.avatarPreview) {\n        this.user.avatar = this.avatarPreview;\n      }\n      this.showUploadDialog = false;\n      this.avatarFile = null;\n      this.showSuccessMessage('头像上传成功');\n    },\n    showSuccessMessage(text) {\n      this.snackbarText = text;\n      this.showSnackbar = true;\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "editMode", "valid", "showUploadDialog", "avatar<PERSON>ile", "avatarPreview", "showSnackbar", "snackbarText", "nameRules", "v", "length", "emailRules", "test", "phoneRules", "departments", "user", "email", "phone", "department", "position", "joinDate", "address", "bio", "avatar", "stats", "projects", "tasks", "meetings", "documents", "taskCompletionRate", "onTimeRate", "watch", "file", "createImagePreview", "methods", "saveProfile", "showSuccessMessage", "reader", "FileReader", "readAsDataURL", "onload", "e", "target", "result", "uploadAvatar", "text"], "sources": ["/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/技术测试/项目实施进度管理/project-management/frontend/src/views/Profile.vue"], "sourcesContent": ["<template>\n  <v-container>\n    <v-row>\n      <v-col cols=\"12\">\n        <div class=\"d-flex align-center justify-space-between mb-4\">\n          <div>\n            <h1 class=\"text-h4\">个人资料</h1>\n            <p class=\"text-subtitle-1 text-medium-emphasis\">\n              查看和编辑您的个人信息\n            </p>\n          </div>\n          <div>\n            <v-btn\n              color=\"primary\"\n              prepend-icon=\"mdi-pencil\"\n              @click=\"editMode = !editMode\"\n            >\n              {{ editMode ? '取消编辑' : '编辑资料' }}\n            </v-btn>\n          </div>\n        </div>\n      </v-col>\n    </v-row>\n\n    <v-row>\n      <v-col cols=\"12\" md=\"4\">\n        <v-card class=\"mb-4\">\n          <v-card-text class=\"text-center\">\n            <v-avatar size=\"150\" class=\"mb-4\">\n              <v-img :src=\"user.avatar\" alt=\"用户头像\"></v-img>\n            </v-avatar>\n\n            <h2 class=\"text-h5 mb-1\">{{ user.name }}</h2>\n            <p class=\"text-body-1 text-medium-emphasis\">{{ user.position }}</p>\n\n            <v-divider class=\"my-4\"></v-divider>\n\n            <v-btn\n              v-if=\"editMode\"\n              color=\"primary\"\n              variant=\"text\"\n              block\n              prepend-icon=\"mdi-camera\"\n              @click=\"showUploadDialog = true\"\n            >\n              更换头像\n            </v-btn>\n          </v-card-text>\n        </v-card>\n\n        <v-card>\n          <v-card-title>联系方式</v-card-title>\n          <v-card-text>\n            <div class=\"d-flex align-center mb-3\">\n              <v-icon icon=\"mdi-email-outline\" class=\"me-3\"></v-icon>\n              <div>\n                <div class=\"text-caption text-medium-emphasis\">邮箱</div>\n                <div class=\"text-body-1\">{{ user.email }}</div>\n              </div>\n            </div>\n\n            <div class=\"d-flex align-center mb-3\">\n              <v-icon icon=\"mdi-phone-outline\" class=\"me-3\"></v-icon>\n              <div>\n                <div class=\"text-caption text-medium-emphasis\">手机</div>\n                <div class=\"text-body-1\">{{ user.phone }}</div>\n              </div>\n            </div>\n\n            <div class=\"d-flex align-center mb-3\">\n              <v-icon icon=\"mdi-office-building-outline\" class=\"me-3\"></v-icon>\n              <div>\n                <div class=\"text-caption text-medium-emphasis\">部门</div>\n                <div class=\"text-body-1\">{{ user.department }}</div>\n              </div>\n            </div>\n\n            <div class=\"d-flex align-center\">\n              <v-icon icon=\"mdi-map-marker-outline\" class=\"me-3\"></v-icon>\n              <div>\n                <div class=\"text-caption text-medium-emphasis\">地址</div>\n                <div class=\"text-body-1\">{{ user.address }}</div>\n              </div>\n            </div>\n          </v-card-text>\n        </v-card>\n      </v-col>\n\n      <v-col cols=\"12\" md=\"8\">\n        <v-card class=\"mb-4\">\n          <v-card-title>个人资料</v-card-title>\n          <v-card-text>\n            <v-form ref=\"form\" v-model=\"valid\">\n              <v-row>\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.name\"\n                    label=\"姓名\"\n                    :rules=\"nameRules\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                    required\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.email\"\n                    label=\"邮箱\"\n                    :rules=\"emailRules\"\n                    variant=\"outlined\"\n                    readonly\n                    disabled\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.phone\"\n                    label=\"手机号码\"\n                    :rules=\"phoneRules\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-select\n                    v-model=\"user.department\"\n                    :items=\"departments\"\n                    label=\"部门\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                  ></v-select>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.position\"\n                    label=\"职位\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.joinDate\"\n                    label=\"入职日期\"\n                    variant=\"outlined\"\n                    readonly\n                    disabled\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\">\n                  <v-text-field\n                    v-model=\"user.address\"\n                    label=\"地址\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\">\n                  <v-textarea\n                    v-model=\"user.bio\"\n                    label=\"个人简介\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                    auto-grow\n                    rows=\"3\"\n                  ></v-textarea>\n                </v-col>\n              </v-row>\n\n              <v-row v-if=\"editMode\">\n                <v-col cols=\"12\" class=\"d-flex justify-end\">\n                  <v-btn\n                    color=\"primary\"\n                    :disabled=\"!valid\"\n                    @click=\"saveProfile\"\n                  >\n                    保存资料\n                  </v-btn>\n                </v-col>\n              </v-row>\n            </v-form>\n          </v-card-text>\n        </v-card>\n\n        <v-card>\n          <v-card-title>\n            <div class=\"d-flex align-center justify-space-between\">\n              <span>工作统计</span>\n              <v-btn\n                color=\"primary\"\n                variant=\"text\"\n                size=\"small\"\n                prepend-icon=\"mdi-chart-line\"\n                @click=\"showWorkflowOptimization = true\"\n              >\n                工作流优化\n              </v-btn>\n            </div>\n          </v-card-title>\n          <v-card-text>\n            <v-row>\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.projects }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">参与项目</div>\n                </div>\n              </v-col>\n\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.tasks }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">完成任务</div>\n                </div>\n              </v-col>\n\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.meetings }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">参加会议</div>\n                </div>\n              </v-col>\n\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.documents }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">提交文档</div>\n                </div>\n              </v-col>\n            </v-row>\n\n            <v-divider class=\"my-4\"></v-divider>\n\n            <div class=\"mb-4\">\n              <div class=\"d-flex align-center justify-space-between mb-2\">\n                <div class=\"text-body-1\">任务完成率</div>\n                <div class=\"text-body-1 font-weight-bold\">{{ stats.taskCompletionRate }}%</div>\n              </div>\n              <v-progress-linear\n                :model-value=\"stats.taskCompletionRate\"\n                color=\"success\"\n                height=\"8\"\n                rounded\n              ></v-progress-linear>\n            </div>\n\n            <div class=\"mb-4\">\n              <div class=\"d-flex align-center justify-space-between mb-2\">\n                <div class=\"text-body-1\">按时完成率</div>\n                <div class=\"text-body-1 font-weight-bold\">{{ stats.onTimeRate }}%</div>\n              </div>\n              <v-progress-linear\n                :model-value=\"stats.onTimeRate\"\n                color=\"info\"\n                height=\"8\"\n                rounded\n              ></v-progress-linear>\n            </div>\n\n            <!-- 工作流效率指标 -->\n            <div class=\"mb-4\">\n              <div class=\"d-flex align-center justify-space-between mb-2\">\n                <div class=\"text-body-1\">工作流效率</div>\n                <div class=\"text-body-1 font-weight-bold\">{{ workflowEfficiency }}%</div>\n              </div>\n              <v-progress-linear\n                :model-value=\"workflowEfficiency\"\n                :color=\"getEfficiencyColor(workflowEfficiency)\"\n                height=\"8\"\n                rounded\n              ></v-progress-linear>\n            </div>\n\n            <!-- 待处理事项 -->\n            <div v-if=\"pendingItems.length > 0\">\n              <v-divider class=\"my-4\"></v-divider>\n              <div class=\"text-subtitle-2 mb-2\">待处理事项</div>\n              <v-chip\n                v-for=\"item in pendingItems\"\n                :key=\"item.id\"\n                :color=\"item.priority === 'high' ? 'error' : item.priority === 'medium' ? 'warning' : 'info'\"\n                size=\"small\"\n                class=\"me-2 mb-2\"\n                @click=\"handlePendingItem(item)\"\n              >\n                {{ item.title }}\n              </v-chip>\n            </div>\n          </v-card-text>\n        </v-card>\n      </v-col>\n    </v-row>\n\n    <!-- 上传头像对话框 -->\n    <v-dialog v-model=\"showUploadDialog\" max-width=\"500\">\n      <v-card>\n        <v-card-title>上传头像</v-card-title>\n        <v-card-text>\n          <v-file-input\n            v-model=\"avatarFile\"\n            label=\"选择图片\"\n            accept=\"image/*\"\n            show-size\n            truncate-length=\"15\"\n            variant=\"outlined\"\n          ></v-file-input>\n\n          <div v-if=\"avatarPreview\" class=\"text-center mt-4\">\n            <v-avatar size=\"150\">\n              <v-img :src=\"avatarPreview\" alt=\"Avatar Preview\"></v-img>\n            </v-avatar>\n          </div>\n        </v-card-text>\n        <v-card-actions>\n          <v-spacer></v-spacer>\n          <v-btn\n            color=\"grey-darken-1\"\n            variant=\"text\"\n            @click=\"showUploadDialog = false\"\n          >\n            取消\n          </v-btn>\n          <v-btn\n            color=\"primary\"\n            variant=\"text\"\n            :disabled=\"!avatarFile\"\n            @click=\"uploadAvatar\"\n          >\n            上传\n          </v-btn>\n        </v-card-actions>\n      </v-card>\n    </v-dialog>\n\n    <!-- 工作流优化对话框 -->\n    <v-dialog v-model=\"showWorkflowOptimization\" max-width=\"1200\">\n      <v-card>\n        <v-card-title class=\"d-flex align-center\">\n          <v-icon icon=\"mdi-chart-line\" class=\"me-2\"></v-icon>\n          工作流优化分析\n        </v-card-title>\n\n        <v-card-text>\n          <v-row>\n            <!-- 工作流效率分析 -->\n            <v-col cols=\"12\" md=\"6\">\n              <v-card variant=\"outlined\">\n                <v-card-title class=\"text-h6\">效率分析</v-card-title>\n                <v-card-text>\n                  <div class=\"mb-4\">\n                    <div class=\"d-flex justify-space-between mb-2\">\n                      <span>任务处理速度</span>\n                      <span class=\"font-weight-bold\">{{ workflowMetrics.taskProcessingSpeed }}个/天</span>\n                    </div>\n                    <v-progress-linear\n                      :model-value=\"workflowMetrics.taskProcessingSpeed * 10\"\n                      color=\"primary\"\n                      height=\"6\"\n                    ></v-progress-linear>\n                  </div>\n\n                  <div class=\"mb-4\">\n                    <div class=\"d-flex justify-space-between mb-2\">\n                      <span>平均响应时间</span>\n                      <span class=\"font-weight-bold\">{{ workflowMetrics.avgResponseTime }}小时</span>\n                    </div>\n                    <v-progress-linear\n                      :model-value=\"Math.max(0, 100 - workflowMetrics.avgResponseTime * 5)\"\n                      color=\"info\"\n                      height=\"6\"\n                    ></v-progress-linear>\n                  </div>\n\n                  <div class=\"mb-4\">\n                    <div class=\"d-flex justify-space-between mb-2\">\n                      <span>工作流自动化率</span>\n                      <span class=\"font-weight-bold\">{{ workflowMetrics.automationRate }}%</span>\n                    </div>\n                    <v-progress-linear\n                      :model-value=\"workflowMetrics.automationRate\"\n                      color=\"success\"\n                      height=\"6\"\n                    ></v-progress-linear>\n                  </div>\n                </v-card-text>\n              </v-card>\n            </v-col>\n\n            <!-- 优化建议 -->\n            <v-col cols=\"12\" md=\"6\">\n              <v-card variant=\"outlined\">\n                <v-card-title class=\"text-h6\">优化建议</v-card-title>\n                <v-card-text>\n                  <v-list density=\"compact\">\n                    <v-list-item\n                      v-for=\"suggestion in optimizationSuggestions\"\n                      :key=\"suggestion.id\"\n                      :prepend-icon=\"suggestion.icon\"\n                      :title=\"suggestion.title\"\n                      :subtitle=\"suggestion.description\"\n                      @click=\"applySuggestion(suggestion)\"\n                    >\n                      <template v-slot:append>\n                        <v-chip\n                          :color=\"suggestion.impact === 'high' ? 'success' : suggestion.impact === 'medium' ? 'warning' : 'info'\"\n                          size=\"small\"\n                        >\n                          {{ suggestion.impact === 'high' ? '高效果' : suggestion.impact === 'medium' ? '中效果' : '低效果' }}\n                        </v-chip>\n                      </template>\n                    </v-list-item>\n                  </v-list>\n                </v-card-text>\n              </v-card>\n            </v-col>\n\n            <!-- 工作流模板 -->\n            <v-col cols=\"12\">\n              <v-card variant=\"outlined\">\n                <v-card-title class=\"text-h6\">工作流模板</v-card-title>\n                <v-card-text>\n                  <v-row>\n                    <v-col\n                      v-for=\"template in workflowTemplates\"\n                      :key=\"template.id\"\n                      cols=\"12\"\n                      md=\"4\"\n                    >\n                      <v-card\n                        variant=\"outlined\"\n                        class=\"workflow-template-card\"\n                        @click=\"applyWorkflowTemplate(template)\"\n                      >\n                        <v-card-text class=\"text-center\">\n                          <v-icon\n                            :icon=\"template.icon\"\n                            size=\"48\"\n                            :color=\"template.color\"\n                            class=\"mb-2\"\n                          ></v-icon>\n                          <div class=\"text-h6 mb-1\">{{ template.name }}</div>\n                          <div class=\"text-caption text-medium-emphasis\">{{ template.description }}</div>\n                        </v-card-text>\n                      </v-card>\n                    </v-col>\n                  </v-row>\n                </v-card-text>\n              </v-card>\n            </v-col>\n          </v-row>\n        </v-card-text>\n\n        <v-card-actions>\n          <v-spacer></v-spacer>\n          <v-btn\n            color=\"grey-darken-1\"\n            variant=\"text\"\n            @click=\"showWorkflowOptimization = false\"\n          >\n            关闭\n          </v-btn>\n          <v-btn\n            color=\"primary\"\n            variant=\"text\"\n            @click=\"exportWorkflowReport\"\n          >\n            导出报告\n          </v-btn>\n        </v-card-actions>\n      </v-card>\n    </v-dialog>\n\n    <!-- 操作成功提示 -->\n    <v-snackbar\n      v-model=\"showSnackbar\"\n      color=\"success\"\n    >\n      {{ snackbarText }}\n\n      <template v-slot:actions>\n        <v-btn\n          variant=\"text\"\n          @click=\"showSnackbar = false\"\n        >\n          关闭\n        </v-btn>\n      </template>\n    </v-snackbar>\n  </v-container>\n</template>\n\n<script>\nexport default {\n  name: 'ProfileView',\n  data() {\n    return {\n      editMode: false,\n      valid: true,\n      showUploadDialog: false,\n      avatarFile: null,\n      avatarPreview: null,\n      showSnackbar: false,\n      snackbarText: '',\n\n      nameRules: [\n        v => !!v || '姓名不能为空',\n        v => v.length <= 20 || '姓名不能超过20个字符'\n      ],\n      emailRules: [\n        v => !!v || '邮箱不能为空',\n        v => /.+@.+\\..+/.test(v) || '邮箱格式不正确'\n      ],\n      phoneRules: [\n        v => !!v || '手机号码不能为空',\n        v => /^1[3-9]\\d{9}$/.test(v) || '手机号码格式不正确'\n      ],\n\n      departments: ['技术部', '市场部', '销售部', '人力资源部', '财务部'],\n\n      user: {\n        name: '张三',\n        email: '<EMAIL>',\n        phone: '13800138000',\n        department: '技术部',\n        position: '项目经理',\n        joinDate: '2023-01-15',\n        address: '上海市浦东新区张江高科技园区',\n        bio: '拥有8年项目管理经验，专注于软件开发领域。擅长敏捷开发方法，曾成功带领团队完成多个大型项目。',\n        avatar: 'https://ui-avatars.com/api/?name=张三&background=random'\n      },\n\n      stats: {\n        projects: 12,\n        tasks: 156,\n        meetings: 48,\n        documents: 32,\n        taskCompletionRate: 92,\n        onTimeRate: 88\n      }\n    }\n  },\n  watch: {\n    avatarFile(file) {\n      if (file) {\n        this.createImagePreview(file)\n      } else {\n        this.avatarPreview = null\n      }\n    }\n  },\n  methods: {\n    saveProfile() {\n      // 在实际应用中，这里会发送请求到后端保存个人资料\n      this.editMode = false\n      this.showSuccessMessage('个人资料保存成功')\n    },\n    createImagePreview(file) {\n      const reader = new FileReader()\n      reader.readAsDataURL(file)\n      reader.onload = e => {\n        this.avatarPreview = e.target.result\n      }\n    },\n    uploadAvatar() {\n      // 在实际应用中，这里会发送请求到后端上传头像\n      if (this.avatarPreview) {\n        this.user.avatar = this.avatarPreview\n      }\n      this.showUploadDialog = false\n      this.avatarFile = null\n      this.showSuccessMessage('头像上传成功')\n    },\n    showSuccessMessage(text) {\n      this.snackbarText = text\n      this.showSnackbar = true\n    }\n  }\n}\n</script>\n"], "mappings": "AAkfA,eAAe;EACbA,IAAI,EAAE,aAAa;EACnBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,KAAK;MACfC,KAAK,EAAE,IAAI;MACXC,gBAAgB,EAAE,KAAK;MACvBC,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE,EAAE;MAEhBC,SAAS,EAAE,CACTC,CAAA,IAAK,CAAC,CAACA,CAAA,IAAK,QAAQ,EACpBA,CAAA,IAAKA,CAAC,CAACC,MAAK,IAAK,EAAC,IAAK,aAAY,CACpC;MACDC,UAAU,EAAE,CACVF,CAAA,IAAK,CAAC,CAACA,CAAA,IAAK,QAAQ,EACpBA,CAAA,IAAK,WAAW,CAACG,IAAI,CAACH,CAAC,KAAK,SAAQ,CACrC;MACDI,UAAU,EAAE,CACVJ,CAAA,IAAK,CAAC,CAACA,CAAA,IAAK,UAAU,EACtBA,CAAA,IAAK,eAAe,CAACG,IAAI,CAACH,CAAC,KAAK,WAAU,CAC3C;MAEDK,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC;MAElDC,IAAI,EAAE;QACJhB,IAAI,EAAE,IAAI;QACViB,KAAK,EAAE,sBAAsB;QAC7BC,KAAK,EAAE,aAAa;QACpBC,UAAU,EAAE,KAAK;QACjBC,QAAQ,EAAE,MAAM;QAChBC,QAAQ,EAAE,YAAY;QACtBC,OAAO,EAAE,gBAAgB;QACzBC,GAAG,EAAE,gDAAgD;QACrDC,MAAM,EAAE;MACV,CAAC;MAEDC,KAAK,EAAE;QACLC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,GAAG;QACVC,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAE,EAAE;QACbC,kBAAkB,EAAE,EAAE;QACtBC,UAAU,EAAE;MACd;IACF;EACF,CAAC;EACDC,KAAK,EAAE;IACL3B,UAAUA,CAAC4B,IAAI,EAAE;MACf,IAAIA,IAAI,EAAE;QACR,IAAI,CAACC,kBAAkB,CAACD,IAAI;MAC9B,OAAO;QACL,IAAI,CAAC3B,aAAY,GAAI,IAAG;MAC1B;IACF;EACF,CAAC;EACD6B,OAAO,EAAE;IACPC,WAAWA,CAAA,EAAG;MACZ;MACA,IAAI,CAAClC,QAAO,GAAI,KAAI;MACpB,IAAI,CAACmC,kBAAkB,CAAC,UAAU;IACpC,CAAC;IACDH,kBAAkBA,CAACD,IAAI,EAAE;MACvB,MAAMK,MAAK,GAAI,IAAIC,UAAU,CAAC;MAC9BD,MAAM,CAACE,aAAa,CAACP,IAAI;MACzBK,MAAM,CAACG,MAAK,GAAIC,CAAA,IAAK;QACnB,IAAI,CAACpC,aAAY,GAAIoC,CAAC,CAACC,MAAM,CAACC,MAAK;MACrC;IACF,CAAC;IACDC,YAAYA,CAAA,EAAG;MACb;MACA,IAAI,IAAI,CAACvC,aAAa,EAAE;QACtB,IAAI,CAACU,IAAI,CAACQ,MAAK,GAAI,IAAI,CAAClB,aAAY;MACtC;MACA,IAAI,CAACF,gBAAe,GAAI,KAAI;MAC5B,IAAI,CAACC,UAAS,GAAI,IAAG;MACrB,IAAI,CAACgC,kBAAkB,CAAC,QAAQ;IAClC,CAAC;IACDA,kBAAkBA,CAACS,IAAI,EAAE;MACvB,IAAI,CAACtC,YAAW,GAAIsC,IAAG;MACvB,IAAI,CAACvC,YAAW,GAAI,IAAG;IACzB;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}