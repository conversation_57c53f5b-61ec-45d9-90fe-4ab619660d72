{"ast": null, "code": "export default {\n  name: 'ProfileView',\n  data() {\n    return {\n      editMode: false,\n      valid: true,\n      showUploadDialog: false,\n      avatarFile: null,\n      avatarPreview: null,\n      showSnackbar: false,\n      snackbarText: '',\n      showWorkflowOptimization: false,\n      nameRules: [v => !!v || '姓名不能为空', v => v.length <= 20 || '姓名不能超过20个字符'],\n      emailRules: [v => !!v || '邮箱不能为空', v => /.+@.+\\..+/.test(v) || '邮箱格式不正确'],\n      phoneRules: [v => !!v || '手机号码不能为空', v => /^1[3-9]\\d{9}$/.test(v) || '手机号码格式不正确'],\n      departments: ['技术部', '市场部', '销售部', '人力资源部', '财务部'],\n      user: {\n        name: '张三',\n        email: 'zhang<PERSON>@example.com',\n        phone: '13800138000',\n        department: '技术部',\n        position: '项目经理',\n        joinDate: '2023-01-15',\n        address: '上海市浦东新区张江高科技园区',\n        bio: '拥有8年项目管理经验，专注于软件开发领域。擅长敏捷开发方法，曾成功带领团队完成多个大型项目。',\n        avatar: 'https://ui-avatars.com/api/?name=张三&background=random'\n      },\n      stats: {\n        projects: 12,\n        tasks: 156,\n        meetings: 48,\n        documents: 32,\n        taskCompletionRate: 92,\n        onTimeRate: 88\n      },\n      // 工作流优化相关数据\n      workflowMetrics: {\n        taskProcessingSpeed: 8.5,\n        avgResponseTime: 2.3,\n        automationRate: 75\n      },\n      pendingItems: [{\n        id: 1,\n        title: '项目A审批',\n        priority: 'high',\n        type: 'approval'\n      }, {\n        id: 2,\n        title: '任务B验收',\n        priority: 'medium',\n        type: 'review'\n      }, {\n        id: 3,\n        title: '会议记录确认',\n        priority: 'low',\n        type: 'confirmation'\n      }],\n      optimizationSuggestions: [{\n        id: 1,\n        title: '启用任务自动分配',\n        description: '根据团队成员工作负载自动分配新任务',\n        icon: 'mdi-account-multiple-plus',\n        impact: 'high',\n        action: 'enable_auto_assignment'\n      }, {\n        id: 2,\n        title: '设置状态自动流转',\n        description: '任务完成后自动触发下一阶段',\n        icon: 'mdi-arrow-right-circle',\n        impact: 'high',\n        action: 'enable_auto_transition'\n      }, {\n        id: 3,\n        title: '优化通知频率',\n        description: '减少非关键通知，提高工作专注度',\n        icon: 'mdi-bell-outline',\n        impact: 'medium',\n        action: 'optimize_notifications'\n      }, {\n        id: 4,\n        title: '启用智能提醒',\n        description: '基于历史数据预测任务延期风险',\n        icon: 'mdi-brain',\n        impact: 'high',\n        action: 'enable_smart_reminders'\n      }],\n      workflowTemplates: [{\n        id: 1,\n        name: '医疗项目标准流程',\n        description: '适用于医疗项目的标准化工作流',\n        icon: 'mdi-hospital-box',\n        color: 'primary'\n      }, {\n        id: 2,\n        name: '敏捷开发流程',\n        description: '快速迭代的敏捷开发工作流',\n        icon: 'mdi-rocket-launch',\n        color: 'success'\n      }, {\n        id: 3,\n        name: '审批密集型流程',\n        description: '需要多层审批的严格工作流',\n        icon: 'mdi-shield-check',\n        color: 'warning'\n      }]\n    };\n  },\n  computed: {\n    workflowEfficiency() {\n      // 基于任务完成率、按时完成率和自动化率计算工作流效率\n      const taskWeight = 0.4;\n      const timeWeight = 0.3;\n      const autoWeight = 0.3;\n      return Math.round(this.stats.taskCompletionRate * taskWeight + this.stats.onTimeRate * timeWeight + this.workflowMetrics.automationRate * autoWeight);\n    }\n  },\n  watch: {\n    avatarFile(file) {\n      if (file) {\n        this.createImagePreview(file);\n      } else {\n        this.avatarPreview = null;\n      }\n    }\n  },\n  methods: {\n    saveProfile() {\n      // 在实际应用中，这里会发送请求到后端保存个人资料\n      this.editMode = false;\n      this.showSuccessMessage('个人资料保存成功');\n    },\n    createImagePreview(file) {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = e => {\n        this.avatarPreview = e.target.result;\n      };\n    },\n    uploadAvatar() {\n      // 在实际应用中，这里会发送请求到后端上传头像\n      if (this.avatarPreview) {\n        this.user.avatar = this.avatarPreview;\n      }\n      this.showUploadDialog = false;\n      this.avatarFile = null;\n      this.showSuccessMessage('头像上传成功');\n    },\n    showSuccessMessage(text) {\n      this.snackbarText = text;\n      this.showSnackbar = true;\n    },\n    // 工作流优化相关方法\n    getEfficiencyColor(efficiency) {\n      if (efficiency >= 85) return 'success';\n      if (efficiency >= 70) return 'warning';\n      return 'error';\n    },\n    handlePendingItem(item) {\n      // 处理待办事项\n      switch (item.type) {\n        case 'approval':\n          this.$router.push('/projects');\n          break;\n        case 'review':\n          this.$router.push('/kanban');\n          break;\n        case 'confirmation':\n          this.$router.push('/meetings');\n          break;\n      }\n      this.showSuccessMessage(`正在处理：${item.title}`);\n    },\n    applySuggestion(suggestion) {\n      // 应用优化建议\n      switch (suggestion.action) {\n        case 'enable_auto_assignment':\n          this.enableAutoAssignment();\n          break;\n        case 'enable_auto_transition':\n          this.enableAutoTransition();\n          break;\n        case 'optimize_notifications':\n          this.optimizeNotifications();\n          break;\n        case 'enable_smart_reminders':\n          this.enableSmartReminders();\n          break;\n      }\n      this.showSuccessMessage(`已应用优化建议：${suggestion.title}`);\n    },\n    applyWorkflowTemplate(template) {\n      // 应用工作流模板\n      this.showSuccessMessage(`正在应用工作流模板：${template.name}`);\n      // 这里可以调用API来应用模板\n    },\n    exportWorkflowReport() {\n      // 导出工作流报告\n      const reportData = {\n        user: this.user.name,\n        date: new Date().toLocaleDateString(),\n        efficiency: this.workflowEfficiency,\n        metrics: this.workflowMetrics,\n        suggestions: this.optimizationSuggestions.length\n      };\n\n      // 模拟导出功能\n      console.log('导出工作流报告:', reportData);\n      this.showSuccessMessage('工作流报告已导出');\n    },\n    // 优化功能实现\n    enableAutoAssignment() {\n      // 启用自动分配功能\n      this.workflowMetrics.automationRate = Math.min(100, this.workflowMetrics.automationRate + 10);\n    },\n    enableAutoTransition() {\n      // 启用自动流转功能\n      this.workflowMetrics.automationRate = Math.min(100, this.workflowMetrics.automationRate + 15);\n      this.workflowMetrics.avgResponseTime = Math.max(0.5, this.workflowMetrics.avgResponseTime - 0.5);\n    },\n    optimizeNotifications() {\n      // 优化通知设置\n      this.workflowMetrics.avgResponseTime = Math.max(0.5, this.workflowMetrics.avgResponseTime - 0.3);\n    },\n    enableSmartReminders() {\n      // 启用智能提醒\n      this.workflowMetrics.taskProcessingSpeed = Math.min(15, this.workflowMetrics.taskProcessingSpeed + 1.5);\n      this.stats.onTimeRate = Math.min(100, this.stats.onTimeRate + 5);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "editMode", "valid", "showUploadDialog", "avatar<PERSON>ile", "avatarPreview", "showSnackbar", "snackbarText", "showWorkflowOptimization", "nameRules", "v", "length", "emailRules", "test", "phoneRules", "departments", "user", "email", "phone", "department", "position", "joinDate", "address", "bio", "avatar", "stats", "projects", "tasks", "meetings", "documents", "taskCompletionRate", "onTimeRate", "workflowMetrics", "taskProcessingSpeed", "avgResponseTime", "automationRate", "pendingItems", "id", "title", "priority", "type", "optimizationSuggestions", "description", "icon", "impact", "action", "workflowTemplates", "color", "computed", "workflowEfficiency", "taskWeight", "timeWeight", "autoWeight", "Math", "round", "watch", "file", "createImagePreview", "methods", "saveProfile", "showSuccessMessage", "reader", "FileReader", "readAsDataURL", "onload", "e", "target", "result", "uploadAvatar", "text", "getEfficiencyColor", "efficiency", "handlePendingItem", "item", "$router", "push", "applySuggestion", "suggestion", "enableAutoAssignment", "enableAutoTransition", "optimizeNotifications", "enableSmartReminders", "applyWorkflowTemplate", "template", "exportWorkflowReport", "reportData", "date", "Date", "toLocaleDateString", "metrics", "suggestions", "console", "log", "min", "max"], "sources": ["/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/技术测试/项目实施进度管理/project-management/frontend/src/views/Profile.vue"], "sourcesContent": ["<template>\n  <v-container>\n    <v-row>\n      <v-col cols=\"12\">\n        <div class=\"d-flex align-center justify-space-between mb-4\">\n          <div>\n            <h1 class=\"text-h4\">个人资料</h1>\n            <p class=\"text-subtitle-1 text-medium-emphasis\">\n              查看和编辑您的个人信息\n            </p>\n          </div>\n          <div>\n            <v-btn\n              color=\"primary\"\n              prepend-icon=\"mdi-pencil\"\n              @click=\"editMode = !editMode\"\n            >\n              {{ editMode ? '取消编辑' : '编辑资料' }}\n            </v-btn>\n          </div>\n        </div>\n      </v-col>\n    </v-row>\n\n    <v-row>\n      <v-col cols=\"12\" md=\"4\">\n        <v-card class=\"mb-4\">\n          <v-card-text class=\"text-center\">\n            <v-avatar size=\"150\" class=\"mb-4\">\n              <v-img :src=\"user.avatar\" alt=\"用户头像\"></v-img>\n            </v-avatar>\n\n            <h2 class=\"text-h5 mb-1\">{{ user.name }}</h2>\n            <p class=\"text-body-1 text-medium-emphasis\">{{ user.position }}</p>\n\n            <v-divider class=\"my-4\"></v-divider>\n\n            <v-btn\n              v-if=\"editMode\"\n              color=\"primary\"\n              variant=\"text\"\n              block\n              prepend-icon=\"mdi-camera\"\n              @click=\"showUploadDialog = true\"\n            >\n              更换头像\n            </v-btn>\n          </v-card-text>\n        </v-card>\n\n        <v-card>\n          <v-card-title>联系方式</v-card-title>\n          <v-card-text>\n            <div class=\"d-flex align-center mb-3\">\n              <v-icon icon=\"mdi-email-outline\" class=\"me-3\"></v-icon>\n              <div>\n                <div class=\"text-caption text-medium-emphasis\">邮箱</div>\n                <div class=\"text-body-1\">{{ user.email }}</div>\n              </div>\n            </div>\n\n            <div class=\"d-flex align-center mb-3\">\n              <v-icon icon=\"mdi-phone-outline\" class=\"me-3\"></v-icon>\n              <div>\n                <div class=\"text-caption text-medium-emphasis\">手机</div>\n                <div class=\"text-body-1\">{{ user.phone }}</div>\n              </div>\n            </div>\n\n            <div class=\"d-flex align-center mb-3\">\n              <v-icon icon=\"mdi-office-building-outline\" class=\"me-3\"></v-icon>\n              <div>\n                <div class=\"text-caption text-medium-emphasis\">部门</div>\n                <div class=\"text-body-1\">{{ user.department }}</div>\n              </div>\n            </div>\n\n            <div class=\"d-flex align-center\">\n              <v-icon icon=\"mdi-map-marker-outline\" class=\"me-3\"></v-icon>\n              <div>\n                <div class=\"text-caption text-medium-emphasis\">地址</div>\n                <div class=\"text-body-1\">{{ user.address }}</div>\n              </div>\n            </div>\n          </v-card-text>\n        </v-card>\n      </v-col>\n\n      <v-col cols=\"12\" md=\"8\">\n        <v-card class=\"mb-4\">\n          <v-card-title>个人资料</v-card-title>\n          <v-card-text>\n            <v-form ref=\"form\" v-model=\"valid\">\n              <v-row>\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.name\"\n                    label=\"姓名\"\n                    :rules=\"nameRules\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                    required\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.email\"\n                    label=\"邮箱\"\n                    :rules=\"emailRules\"\n                    variant=\"outlined\"\n                    readonly\n                    disabled\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.phone\"\n                    label=\"手机号码\"\n                    :rules=\"phoneRules\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-select\n                    v-model=\"user.department\"\n                    :items=\"departments\"\n                    label=\"部门\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                  ></v-select>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.position\"\n                    label=\"职位\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.joinDate\"\n                    label=\"入职日期\"\n                    variant=\"outlined\"\n                    readonly\n                    disabled\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\">\n                  <v-text-field\n                    v-model=\"user.address\"\n                    label=\"地址\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\">\n                  <v-textarea\n                    v-model=\"user.bio\"\n                    label=\"个人简介\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                    auto-grow\n                    rows=\"3\"\n                  ></v-textarea>\n                </v-col>\n              </v-row>\n\n              <v-row v-if=\"editMode\">\n                <v-col cols=\"12\" class=\"d-flex justify-end\">\n                  <v-btn\n                    color=\"primary\"\n                    :disabled=\"!valid\"\n                    @click=\"saveProfile\"\n                  >\n                    保存资料\n                  </v-btn>\n                </v-col>\n              </v-row>\n            </v-form>\n          </v-card-text>\n        </v-card>\n\n        <v-card>\n          <v-card-title>\n            <div class=\"d-flex align-center justify-space-between\">\n              <span>工作统计</span>\n              <v-btn\n                color=\"primary\"\n                variant=\"text\"\n                size=\"small\"\n                prepend-icon=\"mdi-chart-line\"\n                @click=\"showWorkflowOptimization = true\"\n              >\n                工作流优化\n              </v-btn>\n            </div>\n          </v-card-title>\n          <v-card-text>\n            <v-row>\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.projects }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">参与项目</div>\n                </div>\n              </v-col>\n\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.tasks }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">完成任务</div>\n                </div>\n              </v-col>\n\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.meetings }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">参加会议</div>\n                </div>\n              </v-col>\n\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.documents }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">提交文档</div>\n                </div>\n              </v-col>\n            </v-row>\n\n            <v-divider class=\"my-4\"></v-divider>\n\n            <div class=\"mb-4\">\n              <div class=\"d-flex align-center justify-space-between mb-2\">\n                <div class=\"text-body-1\">任务完成率</div>\n                <div class=\"text-body-1 font-weight-bold\">{{ stats.taskCompletionRate }}%</div>\n              </div>\n              <v-progress-linear\n                :model-value=\"stats.taskCompletionRate\"\n                color=\"success\"\n                height=\"8\"\n                rounded\n              ></v-progress-linear>\n            </div>\n\n            <div class=\"mb-4\">\n              <div class=\"d-flex align-center justify-space-between mb-2\">\n                <div class=\"text-body-1\">按时完成率</div>\n                <div class=\"text-body-1 font-weight-bold\">{{ stats.onTimeRate }}%</div>\n              </div>\n              <v-progress-linear\n                :model-value=\"stats.onTimeRate\"\n                color=\"info\"\n                height=\"8\"\n                rounded\n              ></v-progress-linear>\n            </div>\n\n            <!-- 工作流效率指标 -->\n            <div class=\"mb-4\">\n              <div class=\"d-flex align-center justify-space-between mb-2\">\n                <div class=\"text-body-1\">工作流效率</div>\n                <div class=\"text-body-1 font-weight-bold\">{{ workflowEfficiency }}%</div>\n              </div>\n              <v-progress-linear\n                :model-value=\"workflowEfficiency\"\n                :color=\"getEfficiencyColor(workflowEfficiency)\"\n                height=\"8\"\n                rounded\n              ></v-progress-linear>\n            </div>\n\n            <!-- 待处理事项 -->\n            <div v-if=\"pendingItems.length > 0\">\n              <v-divider class=\"my-4\"></v-divider>\n              <div class=\"text-subtitle-2 mb-2\">待处理事项</div>\n              <v-chip\n                v-for=\"item in pendingItems\"\n                :key=\"item.id\"\n                :color=\"item.priority === 'high' ? 'error' : item.priority === 'medium' ? 'warning' : 'info'\"\n                size=\"small\"\n                class=\"me-2 mb-2\"\n                @click=\"handlePendingItem(item)\"\n              >\n                {{ item.title }}\n              </v-chip>\n            </div>\n          </v-card-text>\n        </v-card>\n      </v-col>\n    </v-row>\n\n    <!-- 上传头像对话框 -->\n    <v-dialog v-model=\"showUploadDialog\" max-width=\"500\">\n      <v-card>\n        <v-card-title>上传头像</v-card-title>\n        <v-card-text>\n          <v-file-input\n            v-model=\"avatarFile\"\n            label=\"选择图片\"\n            accept=\"image/*\"\n            show-size\n            truncate-length=\"15\"\n            variant=\"outlined\"\n          ></v-file-input>\n\n          <div v-if=\"avatarPreview\" class=\"text-center mt-4\">\n            <v-avatar size=\"150\">\n              <v-img :src=\"avatarPreview\" alt=\"Avatar Preview\"></v-img>\n            </v-avatar>\n          </div>\n        </v-card-text>\n        <v-card-actions>\n          <v-spacer></v-spacer>\n          <v-btn\n            color=\"grey-darken-1\"\n            variant=\"text\"\n            @click=\"showUploadDialog = false\"\n          >\n            取消\n          </v-btn>\n          <v-btn\n            color=\"primary\"\n            variant=\"text\"\n            :disabled=\"!avatarFile\"\n            @click=\"uploadAvatar\"\n          >\n            上传\n          </v-btn>\n        </v-card-actions>\n      </v-card>\n    </v-dialog>\n\n    <!-- 工作流优化对话框 -->\n    <v-dialog v-model=\"showWorkflowOptimization\" max-width=\"1200\">\n      <v-card>\n        <v-card-title class=\"d-flex align-center\">\n          <v-icon icon=\"mdi-chart-line\" class=\"me-2\"></v-icon>\n          工作流优化分析\n        </v-card-title>\n\n        <v-card-text>\n          <v-row>\n            <!-- 工作流效率分析 -->\n            <v-col cols=\"12\" md=\"6\">\n              <v-card variant=\"outlined\">\n                <v-card-title class=\"text-h6\">效率分析</v-card-title>\n                <v-card-text>\n                  <div class=\"mb-4\">\n                    <div class=\"d-flex justify-space-between mb-2\">\n                      <span>任务处理速度</span>\n                      <span class=\"font-weight-bold\">{{ workflowMetrics.taskProcessingSpeed }}个/天</span>\n                    </div>\n                    <v-progress-linear\n                      :model-value=\"workflowMetrics.taskProcessingSpeed * 10\"\n                      color=\"primary\"\n                      height=\"6\"\n                    ></v-progress-linear>\n                  </div>\n\n                  <div class=\"mb-4\">\n                    <div class=\"d-flex justify-space-between mb-2\">\n                      <span>平均响应时间</span>\n                      <span class=\"font-weight-bold\">{{ workflowMetrics.avgResponseTime }}小时</span>\n                    </div>\n                    <v-progress-linear\n                      :model-value=\"Math.max(0, 100 - workflowMetrics.avgResponseTime * 5)\"\n                      color=\"info\"\n                      height=\"6\"\n                    ></v-progress-linear>\n                  </div>\n\n                  <div class=\"mb-4\">\n                    <div class=\"d-flex justify-space-between mb-2\">\n                      <span>工作流自动化率</span>\n                      <span class=\"font-weight-bold\">{{ workflowMetrics.automationRate }}%</span>\n                    </div>\n                    <v-progress-linear\n                      :model-value=\"workflowMetrics.automationRate\"\n                      color=\"success\"\n                      height=\"6\"\n                    ></v-progress-linear>\n                  </div>\n                </v-card-text>\n              </v-card>\n            </v-col>\n\n            <!-- 优化建议 -->\n            <v-col cols=\"12\" md=\"6\">\n              <v-card variant=\"outlined\">\n                <v-card-title class=\"text-h6\">优化建议</v-card-title>\n                <v-card-text>\n                  <v-list density=\"compact\">\n                    <v-list-item\n                      v-for=\"suggestion in optimizationSuggestions\"\n                      :key=\"suggestion.id\"\n                      :prepend-icon=\"suggestion.icon\"\n                      :title=\"suggestion.title\"\n                      :subtitle=\"suggestion.description\"\n                      @click=\"applySuggestion(suggestion)\"\n                    >\n                      <template v-slot:append>\n                        <v-chip\n                          :color=\"suggestion.impact === 'high' ? 'success' : suggestion.impact === 'medium' ? 'warning' : 'info'\"\n                          size=\"small\"\n                        >\n                          {{ suggestion.impact === 'high' ? '高效果' : suggestion.impact === 'medium' ? '中效果' : '低效果' }}\n                        </v-chip>\n                      </template>\n                    </v-list-item>\n                  </v-list>\n                </v-card-text>\n              </v-card>\n            </v-col>\n\n            <!-- 工作流模板 -->\n            <v-col cols=\"12\">\n              <v-card variant=\"outlined\">\n                <v-card-title class=\"text-h6\">工作流模板</v-card-title>\n                <v-card-text>\n                  <v-row>\n                    <v-col\n                      v-for=\"template in workflowTemplates\"\n                      :key=\"template.id\"\n                      cols=\"12\"\n                      md=\"4\"\n                    >\n                      <v-card\n                        variant=\"outlined\"\n                        class=\"workflow-template-card\"\n                        @click=\"applyWorkflowTemplate(template)\"\n                      >\n                        <v-card-text class=\"text-center\">\n                          <v-icon\n                            :icon=\"template.icon\"\n                            size=\"48\"\n                            :color=\"template.color\"\n                            class=\"mb-2\"\n                          ></v-icon>\n                          <div class=\"text-h6 mb-1\">{{ template.name }}</div>\n                          <div class=\"text-caption text-medium-emphasis\">{{ template.description }}</div>\n                        </v-card-text>\n                      </v-card>\n                    </v-col>\n                  </v-row>\n                </v-card-text>\n              </v-card>\n            </v-col>\n          </v-row>\n        </v-card-text>\n\n        <v-card-actions>\n          <v-spacer></v-spacer>\n          <v-btn\n            color=\"grey-darken-1\"\n            variant=\"text\"\n            @click=\"showWorkflowOptimization = false\"\n          >\n            关闭\n          </v-btn>\n          <v-btn\n            color=\"primary\"\n            variant=\"text\"\n            @click=\"exportWorkflowReport\"\n          >\n            导出报告\n          </v-btn>\n        </v-card-actions>\n      </v-card>\n    </v-dialog>\n\n    <!-- 操作成功提示 -->\n    <v-snackbar\n      v-model=\"showSnackbar\"\n      color=\"success\"\n    >\n      {{ snackbarText }}\n\n      <template v-slot:actions>\n        <v-btn\n          variant=\"text\"\n          @click=\"showSnackbar = false\"\n        >\n          关闭\n        </v-btn>\n      </template>\n    </v-snackbar>\n  </v-container>\n</template>\n\n<script>\nexport default {\n  name: 'ProfileView',\n  data() {\n    return {\n      editMode: false,\n      valid: true,\n      showUploadDialog: false,\n      avatarFile: null,\n      avatarPreview: null,\n      showSnackbar: false,\n      snackbarText: '',\n      showWorkflowOptimization: false,\n\n      nameRules: [\n        v => !!v || '姓名不能为空',\n        v => v.length <= 20 || '姓名不能超过20个字符'\n      ],\n      emailRules: [\n        v => !!v || '邮箱不能为空',\n        v => /.+@.+\\..+/.test(v) || '邮箱格式不正确'\n      ],\n      phoneRules: [\n        v => !!v || '手机号码不能为空',\n        v => /^1[3-9]\\d{9}$/.test(v) || '手机号码格式不正确'\n      ],\n\n      departments: ['技术部', '市场部', '销售部', '人力资源部', '财务部'],\n\n      user: {\n        name: '张三',\n        email: '<EMAIL>',\n        phone: '13800138000',\n        department: '技术部',\n        position: '项目经理',\n        joinDate: '2023-01-15',\n        address: '上海市浦东新区张江高科技园区',\n        bio: '拥有8年项目管理经验，专注于软件开发领域。擅长敏捷开发方法，曾成功带领团队完成多个大型项目。',\n        avatar: 'https://ui-avatars.com/api/?name=张三&background=random'\n      },\n\n      stats: {\n        projects: 12,\n        tasks: 156,\n        meetings: 48,\n        documents: 32,\n        taskCompletionRate: 92,\n        onTimeRate: 88\n      },\n\n      // 工作流优化相关数据\n      workflowMetrics: {\n        taskProcessingSpeed: 8.5,\n        avgResponseTime: 2.3,\n        automationRate: 75\n      },\n\n      pendingItems: [\n        { id: 1, title: '项目A审批', priority: 'high', type: 'approval' },\n        { id: 2, title: '任务B验收', priority: 'medium', type: 'review' },\n        { id: 3, title: '会议记录确认', priority: 'low', type: 'confirmation' }\n      ],\n\n      optimizationSuggestions: [\n        {\n          id: 1,\n          title: '启用任务自动分配',\n          description: '根据团队成员工作负载自动分配新任务',\n          icon: 'mdi-account-multiple-plus',\n          impact: 'high',\n          action: 'enable_auto_assignment'\n        },\n        {\n          id: 2,\n          title: '设置状态自动流转',\n          description: '任务完成后自动触发下一阶段',\n          icon: 'mdi-arrow-right-circle',\n          impact: 'high',\n          action: 'enable_auto_transition'\n        },\n        {\n          id: 3,\n          title: '优化通知频率',\n          description: '减少非关键通知，提高工作专注度',\n          icon: 'mdi-bell-outline',\n          impact: 'medium',\n          action: 'optimize_notifications'\n        },\n        {\n          id: 4,\n          title: '启用智能提醒',\n          description: '基于历史数据预测任务延期风险',\n          icon: 'mdi-brain',\n          impact: 'high',\n          action: 'enable_smart_reminders'\n        }\n      ],\n\n      workflowTemplates: [\n        {\n          id: 1,\n          name: '医疗项目标准流程',\n          description: '适用于医疗项目的标准化工作流',\n          icon: 'mdi-hospital-box',\n          color: 'primary'\n        },\n        {\n          id: 2,\n          name: '敏捷开发流程',\n          description: '快速迭代的敏捷开发工作流',\n          icon: 'mdi-rocket-launch',\n          color: 'success'\n        },\n        {\n          id: 3,\n          name: '审批密集型流程',\n          description: '需要多层审批的严格工作流',\n          icon: 'mdi-shield-check',\n          color: 'warning'\n        }\n      ]\n    }\n  },\n  computed: {\n    workflowEfficiency() {\n      // 基于任务完成率、按时完成率和自动化率计算工作流效率\n      const taskWeight = 0.4\n      const timeWeight = 0.3\n      const autoWeight = 0.3\n\n      return Math.round(\n        this.stats.taskCompletionRate * taskWeight +\n        this.stats.onTimeRate * timeWeight +\n        this.workflowMetrics.automationRate * autoWeight\n      )\n    }\n  },\n  watch: {\n    avatarFile(file) {\n      if (file) {\n        this.createImagePreview(file)\n      } else {\n        this.avatarPreview = null\n      }\n    }\n  },\n  methods: {\n    saveProfile() {\n      // 在实际应用中，这里会发送请求到后端保存个人资料\n      this.editMode = false\n      this.showSuccessMessage('个人资料保存成功')\n    },\n    createImagePreview(file) {\n      const reader = new FileReader()\n      reader.readAsDataURL(file)\n      reader.onload = e => {\n        this.avatarPreview = e.target.result\n      }\n    },\n    uploadAvatar() {\n      // 在实际应用中，这里会发送请求到后端上传头像\n      if (this.avatarPreview) {\n        this.user.avatar = this.avatarPreview\n      }\n      this.showUploadDialog = false\n      this.avatarFile = null\n      this.showSuccessMessage('头像上传成功')\n    },\n    showSuccessMessage(text) {\n      this.snackbarText = text\n      this.showSnackbar = true\n    },\n\n    // 工作流优化相关方法\n    getEfficiencyColor(efficiency) {\n      if (efficiency >= 85) return 'success'\n      if (efficiency >= 70) return 'warning'\n      return 'error'\n    },\n\n    handlePendingItem(item) {\n      // 处理待办事项\n      switch (item.type) {\n        case 'approval':\n          this.$router.push('/projects')\n          break\n        case 'review':\n          this.$router.push('/kanban')\n          break\n        case 'confirmation':\n          this.$router.push('/meetings')\n          break\n      }\n      this.showSuccessMessage(`正在处理：${item.title}`)\n    },\n\n    applySuggestion(suggestion) {\n      // 应用优化建议\n      switch (suggestion.action) {\n        case 'enable_auto_assignment':\n          this.enableAutoAssignment()\n          break\n        case 'enable_auto_transition':\n          this.enableAutoTransition()\n          break\n        case 'optimize_notifications':\n          this.optimizeNotifications()\n          break\n        case 'enable_smart_reminders':\n          this.enableSmartReminders()\n          break\n      }\n      this.showSuccessMessage(`已应用优化建议：${suggestion.title}`)\n    },\n\n    applyWorkflowTemplate(template) {\n      // 应用工作流模板\n      this.showSuccessMessage(`正在应用工作流模板：${template.name}`)\n      // 这里可以调用API来应用模板\n    },\n\n    exportWorkflowReport() {\n      // 导出工作流报告\n      const reportData = {\n        user: this.user.name,\n        date: new Date().toLocaleDateString(),\n        efficiency: this.workflowEfficiency,\n        metrics: this.workflowMetrics,\n        suggestions: this.optimizationSuggestions.length\n      }\n\n      // 模拟导出功能\n      console.log('导出工作流报告:', reportData)\n      this.showSuccessMessage('工作流报告已导出')\n    },\n\n    // 优化功能实现\n    enableAutoAssignment() {\n      // 启用自动分配功能\n      this.workflowMetrics.automationRate = Math.min(100, this.workflowMetrics.automationRate + 10)\n    },\n\n    enableAutoTransition() {\n      // 启用自动流转功能\n      this.workflowMetrics.automationRate = Math.min(100, this.workflowMetrics.automationRate + 15)\n      this.workflowMetrics.avgResponseTime = Math.max(0.5, this.workflowMetrics.avgResponseTime - 0.5)\n    },\n\n    optimizeNotifications() {\n      // 优化通知设置\n      this.workflowMetrics.avgResponseTime = Math.max(0.5, this.workflowMetrics.avgResponseTime - 0.3)\n    },\n\n    enableSmartReminders() {\n      // 启用智能提醒\n      this.workflowMetrics.taskProcessingSpeed = Math.min(15, this.workflowMetrics.taskProcessingSpeed + 1.5)\n      this.stats.onTimeRate = Math.min(100, this.stats.onTimeRate + 5)\n    }\n  }\n}\n</script>\n"], "mappings": "AAkfA,eAAe;EACbA,IAAI,EAAE,aAAa;EACnBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,KAAK;MACfC,KAAK,EAAE,IAAI;MACXC,gBAAgB,EAAE,KAAK;MACvBC,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE,EAAE;MAChBC,wBAAwB,EAAE,KAAK;MAE/BC,SAAS,EAAE,CACTC,CAAA,IAAK,CAAC,CAACA,CAAA,IAAK,QAAQ,EACpBA,CAAA,IAAKA,CAAC,CAACC,MAAK,IAAK,EAAC,IAAK,aAAY,CACpC;MACDC,UAAU,EAAE,CACVF,CAAA,IAAK,CAAC,CAACA,CAAA,IAAK,QAAQ,EACpBA,CAAA,IAAK,WAAW,CAACG,IAAI,CAACH,CAAC,KAAK,SAAQ,CACrC;MACDI,UAAU,EAAE,CACVJ,CAAA,IAAK,CAAC,CAACA,CAAA,IAAK,UAAU,EACtBA,CAAA,IAAK,eAAe,CAACG,IAAI,CAACH,CAAC,KAAK,WAAU,CAC3C;MAEDK,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC;MAElDC,IAAI,EAAE;QACJjB,IAAI,EAAE,IAAI;QACVkB,KAAK,EAAE,sBAAsB;QAC7BC,KAAK,EAAE,aAAa;QACpBC,UAAU,EAAE,KAAK;QACjBC,QAAQ,EAAE,MAAM;QAChBC,QAAQ,EAAE,YAAY;QACtBC,OAAO,EAAE,gBAAgB;QACzBC,GAAG,EAAE,gDAAgD;QACrDC,MAAM,EAAE;MACV,CAAC;MAEDC,KAAK,EAAE;QACLC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,GAAG;QACVC,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAE,EAAE;QACbC,kBAAkB,EAAE,EAAE;QACtBC,UAAU,EAAE;MACd,CAAC;MAED;MACAC,eAAe,EAAE;QACfC,mBAAmB,EAAE,GAAG;QACxBC,eAAe,EAAE,GAAG;QACpBC,cAAc,EAAE;MAClB,CAAC;MAEDC,YAAY,EAAE,CACZ;QAAEC,EAAE,EAAE,CAAC;QAAEC,KAAK,EAAE,OAAO;QAAEC,QAAQ,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAW,CAAC,EAC7D;QAAEH,EAAE,EAAE,CAAC;QAAEC,KAAK,EAAE,OAAO;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAS,CAAC,EAC7D;QAAEH,EAAE,EAAE,CAAC;QAAEC,KAAK,EAAE,QAAQ;QAAEC,QAAQ,EAAE,KAAK;QAAEC,IAAI,EAAE;MAAe,EACjE;MAEDC,uBAAuB,EAAE,CACvB;QACEJ,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,UAAU;QACjBI,WAAW,EAAE,mBAAmB;QAChCC,IAAI,EAAE,2BAA2B;QACjCC,MAAM,EAAE,MAAM;QACdC,MAAM,EAAE;MACV,CAAC,EACD;QACER,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,UAAU;QACjBI,WAAW,EAAE,eAAe;QAC5BC,IAAI,EAAE,wBAAwB;QAC9BC,MAAM,EAAE,MAAM;QACdC,MAAM,EAAE;MACV,CAAC,EACD;QACER,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,QAAQ;QACfI,WAAW,EAAE,iBAAiB;QAC9BC,IAAI,EAAE,kBAAkB;QACxBC,MAAM,EAAE,QAAQ;QAChBC,MAAM,EAAE;MACV,CAAC,EACD;QACER,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,QAAQ;QACfI,WAAW,EAAE,gBAAgB;QAC7BC,IAAI,EAAE,WAAW;QACjBC,MAAM,EAAE,MAAM;QACdC,MAAM,EAAE;MACV,EACD;MAEDC,iBAAiB,EAAE,CACjB;QACET,EAAE,EAAE,CAAC;QACLtC,IAAI,EAAE,UAAU;QAChB2C,WAAW,EAAE,gBAAgB;QAC7BC,IAAI,EAAE,kBAAkB;QACxBI,KAAK,EAAE;MACT,CAAC,EACD;QACEV,EAAE,EAAE,CAAC;QACLtC,IAAI,EAAE,QAAQ;QACd2C,WAAW,EAAE,cAAc;QAC3BC,IAAI,EAAE,mBAAmB;QACzBI,KAAK,EAAE;MACT,CAAC,EACD;QACEV,EAAE,EAAE,CAAC;QACLtC,IAAI,EAAE,SAAS;QACf2C,WAAW,EAAE,cAAc;QAC3BC,IAAI,EAAE,kBAAkB;QACxBI,KAAK,EAAE;MACT;IAEJ;EACF,CAAC;EACDC,QAAQ,EAAE;IACRC,kBAAkBA,CAAA,EAAG;MACnB;MACA,MAAMC,UAAS,GAAI,GAAE;MACrB,MAAMC,UAAS,GAAI,GAAE;MACrB,MAAMC,UAAS,GAAI,GAAE;MAErB,OAAOC,IAAI,CAACC,KAAK,CACf,IAAI,CAAC7B,KAAK,CAACK,kBAAiB,GAAIoB,UAAS,GACzC,IAAI,CAACzB,KAAK,CAACM,UAAS,GAAIoB,UAAS,GACjC,IAAI,CAACnB,eAAe,CAACG,cAAa,GAAIiB,UACxC;IACF;EACF,CAAC;EACDG,KAAK,EAAE;IACLnD,UAAUA,CAACoD,IAAI,EAAE;MACf,IAAIA,IAAI,EAAE;QACR,IAAI,CAACC,kBAAkB,CAACD,IAAI;MAC9B,OAAO;QACL,IAAI,CAACnD,aAAY,GAAI,IAAG;MAC1B;IACF;EACF,CAAC;EACDqD,OAAO,EAAE;IACPC,WAAWA,CAAA,EAAG;MACZ;MACA,IAAI,CAAC1D,QAAO,GAAI,KAAI;MACpB,IAAI,CAAC2D,kBAAkB,CAAC,UAAU;IACpC,CAAC;IACDH,kBAAkBA,CAACD,IAAI,EAAE;MACvB,MAAMK,MAAK,GAAI,IAAIC,UAAU,CAAC;MAC9BD,MAAM,CAACE,aAAa,CAACP,IAAI;MACzBK,MAAM,CAACG,MAAK,GAAIC,CAAA,IAAK;QACnB,IAAI,CAAC5D,aAAY,GAAI4D,CAAC,CAACC,MAAM,CAACC,MAAK;MACrC;IACF,CAAC;IACDC,YAAYA,CAAA,EAAG;MACb;MACA,IAAI,IAAI,CAAC/D,aAAa,EAAE;QACtB,IAAI,CAACW,IAAI,CAACQ,MAAK,GAAI,IAAI,CAACnB,aAAY;MACtC;MACA,IAAI,CAACF,gBAAe,GAAI,KAAI;MAC5B,IAAI,CAACC,UAAS,GAAI,IAAG;MACrB,IAAI,CAACwD,kBAAkB,CAAC,QAAQ;IAClC,CAAC;IACDA,kBAAkBA,CAACS,IAAI,EAAE;MACvB,IAAI,CAAC9D,YAAW,GAAI8D,IAAG;MACvB,IAAI,CAAC/D,YAAW,GAAI,IAAG;IACzB,CAAC;IAED;IACAgE,kBAAkBA,CAACC,UAAU,EAAE;MAC7B,IAAIA,UAAS,IAAK,EAAE,EAAE,OAAO,SAAQ;MACrC,IAAIA,UAAS,IAAK,EAAE,EAAE,OAAO,SAAQ;MACrC,OAAO,OAAM;IACf,CAAC;IAEDC,iBAAiBA,CAACC,IAAI,EAAE;MACtB;MACA,QAAQA,IAAI,CAACjC,IAAI;QACf,KAAK,UAAU;UACb,IAAI,CAACkC,OAAO,CAACC,IAAI,CAAC,WAAW;UAC7B;QACF,KAAK,QAAQ;UACX,IAAI,CAACD,OAAO,CAACC,IAAI,CAAC,SAAS;UAC3B;QACF,KAAK,cAAc;UACjB,IAAI,CAACD,OAAO,CAACC,IAAI,CAAC,WAAW;UAC7B;MACJ;MACA,IAAI,CAACf,kBAAkB,CAAC,QAAQa,IAAI,CAACnC,KAAK,EAAE;IAC9C,CAAC;IAEDsC,eAAeA,CAACC,UAAU,EAAE;MAC1B;MACA,QAAQA,UAAU,CAAChC,MAAM;QACvB,KAAK,wBAAwB;UAC3B,IAAI,CAACiC,oBAAoB,CAAC;UAC1B;QACF,KAAK,wBAAwB;UAC3B,IAAI,CAACC,oBAAoB,CAAC;UAC1B;QACF,KAAK,wBAAwB;UAC3B,IAAI,CAACC,qBAAqB,CAAC;UAC3B;QACF,KAAK,wBAAwB;UAC3B,IAAI,CAACC,oBAAoB,CAAC;UAC1B;MACJ;MACA,IAAI,CAACrB,kBAAkB,CAAC,WAAWiB,UAAU,CAACvC,KAAK,EAAE;IACvD,CAAC;IAED4C,qBAAqBA,CAACC,QAAQ,EAAE;MAC9B;MACA,IAAI,CAACvB,kBAAkB,CAAC,aAAauB,QAAQ,CAACpF,IAAI,EAAE;MACpD;IACF,CAAC;IAEDqF,oBAAoBA,CAAA,EAAG;MACrB;MACA,MAAMC,UAAS,GAAI;QACjBrE,IAAI,EAAE,IAAI,CAACA,IAAI,CAACjB,IAAI;QACpBuF,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;QACrCjB,UAAU,EAAE,IAAI,CAACtB,kBAAkB;QACnCwC,OAAO,EAAE,IAAI,CAACzD,eAAe;QAC7B0D,WAAW,EAAE,IAAI,CAACjD,uBAAuB,CAAC9B;MAC5C;;MAEA;MACAgF,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEP,UAAU;MAClC,IAAI,CAACzB,kBAAkB,CAAC,UAAU;IACpC,CAAC;IAED;IACAkB,oBAAoBA,CAAA,EAAG;MACrB;MACA,IAAI,CAAC9C,eAAe,CAACG,cAAa,GAAIkB,IAAI,CAACwC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC7D,eAAe,CAACG,cAAa,GAAI,EAAE;IAC9F,CAAC;IAED4C,oBAAoBA,CAAA,EAAG;MACrB;MACA,IAAI,CAAC/C,eAAe,CAACG,cAAa,GAAIkB,IAAI,CAACwC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC7D,eAAe,CAACG,cAAa,GAAI,EAAE;MAC5F,IAAI,CAACH,eAAe,CAACE,eAAc,GAAImB,IAAI,CAACyC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC9D,eAAe,CAACE,eAAc,GAAI,GAAG;IACjG,CAAC;IAED8C,qBAAqBA,CAAA,EAAG;MACtB;MACA,IAAI,CAAChD,eAAe,CAACE,eAAc,GAAImB,IAAI,CAACyC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC9D,eAAe,CAACE,eAAc,GAAI,GAAG;IACjG,CAAC;IAED+C,oBAAoBA,CAAA,EAAG;MACrB;MACA,IAAI,CAACjD,eAAe,CAACC,mBAAkB,GAAIoB,IAAI,CAACwC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC7D,eAAe,CAACC,mBAAkB,GAAI,GAAG;MACtG,IAAI,CAACR,KAAK,CAACM,UAAS,GAAIsB,IAAI,CAACwC,GAAG,CAAC,GAAG,EAAE,IAAI,CAACpE,KAAK,CAACM,UAAS,GAAI,CAAC;IACjE;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}