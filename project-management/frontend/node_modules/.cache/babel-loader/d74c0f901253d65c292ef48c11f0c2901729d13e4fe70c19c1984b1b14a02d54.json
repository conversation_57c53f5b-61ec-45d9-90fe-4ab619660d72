{"ast": null, "code": "/**\n * 模块间关联管理\n * 负责处理项目、任务、风险、会议等模块间的数据关联和业务逻辑\n */\n\nconst state = {\n  // 关联关系缓存\n  projectRelations: {},\n  // { projectId: { tasks: [], risks: [], meetings: [] } }\n  taskDependencies: {},\n  // { taskId: { dependencies: [], dependents: [] } }\n\n  // 统计数据缓存\n  projectStats: {},\n  // { projectId: { progress, taskStats, riskStats, etc. } }\n\n  // 同步状态\n  lastSyncTime: null,\n  syncInProgress: false\n};\nconst mutations = {\n  SET_PROJECT_RELATIONS(state, {\n    projectId,\n    relations\n  }) {\n    state.projectRelations = {\n      ...state.projectRelations,\n      [projectId]: relations\n    };\n  },\n  SET_PROJECT_STATS(state, {\n    projectId,\n    stats\n  }) {\n    state.projectStats = {\n      ...state.projectStats,\n      [projectId]: stats\n    };\n  },\n  SET_TASK_DEPENDENCIES(state, {\n    taskId,\n    dependencies\n  }) {\n    state.taskDependencies = {\n      ...state.taskDependencies,\n      [taskId]: dependencies\n    };\n  },\n  SET_SYNC_STATUS(state, status) {\n    state.syncInProgress = status;\n  },\n  SET_LAST_SYNC_TIME(state, time) {\n    state.lastSyncTime = time;\n  },\n  CLEAR_PROJECT_RELATIONS(state, projectId) {\n    delete state.projectRelations[projectId];\n    delete state.projectStats[projectId];\n  }\n};\nconst actions = {\n  /**\n   * 同步项目关联数据\n   */\n  async syncProjectRelations({\n    commit,\n    dispatch,\n    rootGetters\n  }, projectId) {\n    try {\n      commit('SET_SYNC_STATUS', true);\n\n      // 获取项目相关的所有数据\n      const [tasks, risks, meetings] = await Promise.all([dispatch('tasks/fetchTasksByProject', projectId, {\n        root: true\n      }), dispatch('risks/fetchRisksByProject', projectId, {\n        root: true\n      }), dispatch('meetings/fetchMeetingsByProject', projectId, {\n        root: true\n      })]);\n\n      // 构建关联关系\n      const relations = {\n        tasks: tasks || [],\n        risks: risks || [],\n        meetings: meetings || []\n      };\n      commit('SET_PROJECT_RELATIONS', {\n        projectId,\n        relations\n      });\n\n      // 计算项目统计数据\n      await dispatch('calculateProjectStats', projectId);\n      commit('SET_LAST_SYNC_TIME', new Date().toISOString());\n      return relations;\n    } catch (error) {\n      dispatch('setError', `同步项目关联数据失败: ${error.message}`, {\n        root: true\n      });\n      throw error;\n    } finally {\n      commit('SET_SYNC_STATUS', false);\n    }\n  },\n  /**\n   * 计算项目统计数据\n   */\n  async calculateProjectStats({\n    commit,\n    state,\n    rootGetters\n  }, projectId) {\n    try {\n      const relations = state.projectRelations[projectId];\n      if (!relations) return null;\n      const {\n        tasks,\n        risks,\n        meetings\n      } = relations;\n\n      // 任务统计\n      const taskStats = {\n        total: tasks.length,\n        completed: tasks.filter(t => t.status === 'completed').length,\n        inProgress: tasks.filter(t => t.status === 'in-progress').length,\n        pending: tasks.filter(t => t.status === 'pending').length,\n        overdue: tasks.filter(t => {\n          const dueDate = new Date(t.dueDate || t.endDate);\n          return t.status !== 'completed' && dueDate < new Date();\n        }).length\n      };\n\n      // 风险统计\n      const riskStats = {\n        total: risks.length,\n        open: risks.filter(r => r.status === 'open').length,\n        monitoring: risks.filter(r => r.status === 'monitoring').length,\n        mitigated: risks.filter(r => r.status === 'mitigated').length,\n        closed: risks.filter(r => r.status === 'closed').length,\n        high: risks.filter(r => r.impactLevel === 'high' || r.level === 'high').length,\n        critical: risks.filter(r => r.impactLevel === 'critical' || r.level === 'critical').length\n      };\n\n      // 会议统计\n      const meetingStats = {\n        total: meetings.length,\n        upcoming: meetings.filter(m => new Date(m.startTime) > new Date()).length,\n        completed: meetings.filter(m => m.status === 'completed').length\n      };\n\n      // 计算项目整体进度\n      const progress = taskStats.total > 0 ? Math.round(taskStats.completed / taskStats.total * 100) : 0;\n\n      // 计算项目健康度\n      const healthScore = calculateProjectHealth(taskStats, riskStats);\n      const stats = {\n        progress,\n        healthScore,\n        taskStats,\n        riskStats,\n        meetingStats,\n        lastUpdated: new Date().toISOString()\n      };\n      commit('SET_PROJECT_STATS', {\n        projectId,\n        stats\n      });\n\n      // 同步更新项目进度\n      await dispatch('projects/updateProjectProgress', {\n        projectId,\n        progress\n      }, {\n        root: true\n      });\n      return stats;\n    } catch (error) {\n      console.error('计算项目统计数据失败:', error);\n      throw error;\n    }\n  },\n  /**\n   * 处理任务状态变更的级联影响\n   */\n  async handleTaskStatusChange({\n    dispatch,\n    commit\n  }, {\n    taskId,\n    oldStatus,\n    newStatus,\n    projectId\n  }) {\n    try {\n      // 重新计算项目统计\n      await dispatch('calculateProjectStats', projectId);\n\n      // 如果任务完成，检查是否有依赖任务可以开始\n      if (newStatus === 'completed') {\n        await dispatch('checkDependentTasks', taskId);\n      }\n\n      // 如果任务延期，创建风险记录\n      if (newStatus === 'overdue') {\n        await dispatch('createTaskDelayRisk', {\n          taskId,\n          projectId\n        });\n      }\n\n      // 通知相关模块更新\n      await dispatch('notifyModulesUpdate', {\n        type: 'task_status_change',\n        taskId,\n        projectId,\n        oldStatus,\n        newStatus\n      });\n    } catch (error) {\n      console.error('处理任务状态变更失败:', error);\n    }\n  },\n  /**\n   * 处理项目状态变更的级联影响\n   */\n  async handleProjectStatusChange({\n    dispatch,\n    state\n  }, {\n    projectId,\n    oldStatus,\n    newStatus\n  }) {\n    try {\n      const relations = state.projectRelations[projectId];\n      if (!relations) return;\n\n      // 如果项目完成，自动完成所有未完成任务\n      if (newStatus === 'completed') {\n        const incompleteTasks = relations.tasks.filter(t => t.status !== 'completed');\n        for (const task of incompleteTasks) {\n          await dispatch('tasks/updateTaskStatus', {\n            taskId: task.id,\n            status: 'completed'\n          }, {\n            root: true\n          });\n        }\n\n        // 关闭所有开放的风险\n        const openRisks = relations.risks.filter(r => r.status === 'open');\n        for (const risk of openRisks) {\n          await dispatch('risks/updateRiskStatus', {\n            riskId: risk.id,\n            status: 'closed'\n          }, {\n            root: true\n          });\n        }\n      }\n\n      // 如果项目暂停，暂停所有进行中的任务\n      if (newStatus === 'paused') {\n        const activeTasks = relations.tasks.filter(t => t.status === 'in-progress');\n        for (const task of activeTasks) {\n          await dispatch('tasks/updateTaskStatus', {\n            taskId: task.id,\n            status: 'paused'\n          }, {\n            root: true\n          });\n        }\n      }\n\n      // 重新同步项目关联数据\n      await dispatch('syncProjectRelations', projectId);\n    } catch (error) {\n      console.error('处理项目状态变更失败:', error);\n    }\n  },\n  /**\n   * 创建任务延期风险\n   */\n  async createTaskDelayRisk({\n    dispatch\n  }, {\n    taskId,\n    projectId\n  }) {\n    try {\n      const riskData = {\n        title: `任务延期风险 - 任务ID: ${taskId}`,\n        description: '任务超过预期完成时间，可能影响项目进度',\n        projectId: projectId,\n        category: 'schedule',\n        impactLevel: 'medium',\n        status: 'open',\n        relatedTaskId: taskId\n      };\n      await dispatch('risks/createRisk', riskData, {\n        root: true\n      });\n    } catch (error) {\n      console.error('创建任务延期风险失败:', error);\n    }\n  },\n  /**\n   * 检查依赖任务\n   */\n  async checkDependentTasks({\n    dispatch,\n    state\n  }, taskId) {\n    try {\n      const dependencies = state.taskDependencies[taskId];\n      if (!dependencies || !dependencies.dependents) return;\n\n      // 检查依赖此任务的其他任务是否可以开始\n      for (const dependentTaskId of dependencies.dependents) {\n        await dispatch('tasks/checkTaskCanStart', dependentTaskId, {\n          root: true\n        });\n      }\n    } catch (error) {\n      console.error('检查依赖任务失败:', error);\n    }\n  },\n  /**\n   * 通知模块更新\n   */\n  async notifyModulesUpdate({\n    dispatch\n  }, {\n    type,\n    ...data\n  }) {\n    try {\n      // 根据事件类型通知相关模块\n      switch (type) {\n        case 'task_status_change':\n          // 通知甘特图更新\n          dispatch('gantt/refreshData', data.projectId, {\n            root: true\n          });\n          // 通知看板更新\n          dispatch('kanban/refreshData', data.projectId, {\n            root: true\n          });\n          break;\n        case 'project_status_change':\n          // 通知所有相关视图更新\n          dispatch('dashboard/refreshProjectData', data.projectId, {\n            root: true\n          });\n          break;\n        case 'risk_status_change':\n          // 通知风险仪表盘更新\n          dispatch('dashboard/refreshRiskData', data.projectId, {\n            root: true\n          });\n          break;\n      }\n    } catch (error) {\n      console.error('通知模块更新失败:', error);\n    }\n  },\n  /**\n   * 删除项目及其关联数据\n   */\n  async deleteProjectWithRelations({\n    dispatch,\n    commit\n  }, projectId) {\n    try {\n      // 删除关联的任务\n      await dispatch('tasks/deleteTasksByProject', projectId, {\n        root: true\n      });\n\n      // 删除关联的风险\n      await dispatch('risks/deleteRisksByProject', projectId, {\n        root: true\n      });\n\n      // 删除关联的会议\n      await dispatch('meetings/deleteMeetingsByProject', projectId, {\n        root: true\n      });\n\n      // 删除项目\n      await dispatch('projects/deleteProject', projectId, {\n        root: true\n      });\n\n      // 清理关联数据缓存\n      commit('CLEAR_PROJECT_RELATIONS', projectId);\n      dispatch('setSuccess', '项目及其关联数据删除成功', {\n        root: true\n      });\n    } catch (error) {\n      dispatch('setError', `删除项目失败: ${error.message}`, {\n        root: true\n      });\n      throw error;\n    }\n  }\n};\nconst getters = {\n  // 获取项目关联数据\n  getProjectRelations: state => projectId => state.projectRelations[projectId],\n  // 获取项目统计数据\n  getProjectStats: state => projectId => state.projectStats[projectId],\n  // 获取任务依赖关系\n  getTaskDependencies: state => taskId => state.taskDependencies[taskId],\n  // 检查同步状态\n  isSyncInProgress: state => state.syncInProgress,\n  // 获取最后同步时间\n  getLastSyncTime: state => state.lastSyncTime,\n  // 计算项目健康度\n  calculateProjectHealth: () => (taskStats, riskStats) => {\n    let score = 100;\n\n    // 任务延期扣分\n    if (taskStats.overdue > 0) {\n      score -= Math.min(taskStats.overdue * 10, 30);\n    }\n\n    // 高风险扣分\n    if (riskStats.high > 0) {\n      score -= Math.min(riskStats.high * 15, 40);\n    }\n\n    // 关键风险扣分\n    if (riskStats.critical > 0) {\n      score -= Math.min(riskStats.critical * 25, 50);\n    }\n\n    // 任务完成率加分\n    if (taskStats.total > 0) {\n      const completionRate = taskStats.completed / taskStats.total;\n      score += completionRate * 20;\n    }\n    return Math.max(0, Math.min(100, Math.round(score)));\n  }\n};\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions,\n  getters\n};", "map": {"version": 3, "names": ["state", "projectRelations", "taskDependencies", "projectStats", "lastSyncTime", "syncInProgress", "mutations", "SET_PROJECT_RELATIONS", "projectId", "relations", "SET_PROJECT_STATS", "stats", "SET_TASK_DEPENDENCIES", "taskId", "dependencies", "SET_SYNC_STATUS", "status", "SET_LAST_SYNC_TIME", "time", "CLEAR_PROJECT_RELATIONS", "actions", "syncProjectRelations", "commit", "dispatch", "rootGetters", "tasks", "risks", "meetings", "Promise", "all", "root", "Date", "toISOString", "error", "message", "calculateProjectStats", "taskStats", "total", "length", "completed", "filter", "t", "inProgress", "pending", "overdue", "dueDate", "endDate", "riskStats", "open", "r", "monitoring", "mitigated", "closed", "high", "impactLevel", "level", "critical", "meetingStats", "upcoming", "m", "startTime", "progress", "Math", "round", "healthScore", "calculateProjectHealth", "lastUpdated", "console", "handleTaskStatusChange", "oldStatus", "newStatus", "type", "handleProjectStatusChange", "incompleteTasks", "task", "id", "openRisks", "risk", "riskId", "activeTasks", "createTaskDelayRisk", "riskData", "title", "description", "category", "relatedTaskId", "checkDependentTasks", "dependents", "dependentTaskId", "notifyModulesUpdate", "data", "deleteProjectWithRelations", "getters", "getProjectRelations", "getProjectStats", "getTaskDependencies", "isSyncInProgress", "getLastSyncTime", "score", "min", "completionRate", "max", "namespaced"], "sources": ["/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/技术测试/项目实施进度管理/project-management/frontend/src/store/modules/relationships.js"], "sourcesContent": ["/**\n * 模块间关联管理\n * 负责处理项目、任务、风险、会议等模块间的数据关联和业务逻辑\n */\n\nconst state = {\n  // 关联关系缓存\n  projectRelations: {}, // { projectId: { tasks: [], risks: [], meetings: [] } }\n  taskDependencies: {}, // { taskId: { dependencies: [], dependents: [] } }\n\n  // 统计数据缓存\n  projectStats: {}, // { projectId: { progress, taskStats, riskStats, etc. } }\n\n  // 同步状态\n  lastSyncTime: null,\n  syncInProgress: false\n}\n\nconst mutations = {\n  SET_PROJECT_RELATIONS(state, { projectId, relations }) {\n    state.projectRelations = {\n      ...state.projectRelations,\n      [projectId]: relations\n    }\n  },\n\n  SET_PROJECT_STATS(state, { projectId, stats }) {\n    state.projectStats = {\n      ...state.projectStats,\n      [projectId]: stats\n    }\n  },\n\n  SET_TASK_DEPENDENCIES(state, { taskId, dependencies }) {\n    state.taskDependencies = {\n      ...state.taskDependencies,\n      [taskId]: dependencies\n    }\n  },\n\n  SET_SYNC_STATUS(state, status) {\n    state.syncInProgress = status\n  },\n\n  SET_LAST_SYNC_TIME(state, time) {\n    state.lastSyncTime = time\n  },\n\n  CLEAR_PROJECT_RELATIONS(state, projectId) {\n    delete state.projectRelations[projectId]\n    delete state.projectStats[projectId]\n  }\n}\n\nconst actions = {\n  /**\n   * 同步项目关联数据\n   */\n  async syncProjectRelations({ commit, dispatch, rootGetters }, projectId) {\n    try {\n      commit('SET_SYNC_STATUS', true)\n\n      // 获取项目相关的所有数据\n      const [tasks, risks, meetings] = await Promise.all([\n        dispatch('tasks/fetchTasksByProject', projectId, { root: true }),\n        dispatch('risks/fetchRisksByProject', projectId, { root: true }),\n        dispatch('meetings/fetchMeetingsByProject', projectId, { root: true })\n      ])\n\n      // 构建关联关系\n      const relations = {\n        tasks: tasks || [],\n        risks: risks || [],\n        meetings: meetings || []\n      }\n\n      commit('SET_PROJECT_RELATIONS', { projectId, relations })\n\n      // 计算项目统计数据\n      await dispatch('calculateProjectStats', projectId)\n\n      commit('SET_LAST_SYNC_TIME', new Date().toISOString())\n\n      return relations\n    } catch (error) {\n      dispatch('setError', `同步项目关联数据失败: ${error.message}`, { root: true })\n      throw error\n    } finally {\n      commit('SET_SYNC_STATUS', false)\n    }\n  },\n\n  /**\n   * 计算项目统计数据\n   */\n  async calculateProjectStats({ commit, state, rootGetters }, projectId) {\n    try {\n      const relations = state.projectRelations[projectId]\n      if (!relations) return null\n\n      const { tasks, risks, meetings } = relations\n\n      // 任务统计\n      const taskStats = {\n        total: tasks.length,\n        completed: tasks.filter(t => t.status === 'completed').length,\n        inProgress: tasks.filter(t => t.status === 'in-progress').length,\n        pending: tasks.filter(t => t.status === 'pending').length,\n        overdue: tasks.filter(t => {\n          const dueDate = new Date(t.dueDate || t.endDate)\n          return t.status !== 'completed' && dueDate < new Date()\n        }).length\n      }\n\n      // 风险统计\n      const riskStats = {\n        total: risks.length,\n        open: risks.filter(r => r.status === 'open').length,\n        monitoring: risks.filter(r => r.status === 'monitoring').length,\n        mitigated: risks.filter(r => r.status === 'mitigated').length,\n        closed: risks.filter(r => r.status === 'closed').length,\n        high: risks.filter(r => r.impactLevel === 'high' || r.level === 'high').length,\n        critical: risks.filter(r => r.impactLevel === 'critical' || r.level === 'critical').length\n      }\n\n      // 会议统计\n      const meetingStats = {\n        total: meetings.length,\n        upcoming: meetings.filter(m => new Date(m.startTime) > new Date()).length,\n        completed: meetings.filter(m => m.status === 'completed').length\n      }\n\n      // 计算项目整体进度\n      const progress = taskStats.total > 0\n        ? Math.round((taskStats.completed / taskStats.total) * 100)\n        : 0\n\n      // 计算项目健康度\n      const healthScore = calculateProjectHealth(taskStats, riskStats)\n\n      const stats = {\n        progress,\n        healthScore,\n        taskStats,\n        riskStats,\n        meetingStats,\n        lastUpdated: new Date().toISOString()\n      }\n\n      commit('SET_PROJECT_STATS', { projectId, stats })\n\n      // 同步更新项目进度\n      await dispatch('projects/updateProjectProgress', {\n        projectId,\n        progress\n      }, { root: true })\n\n      return stats\n    } catch (error) {\n      console.error('计算项目统计数据失败:', error)\n      throw error\n    }\n  },\n\n  /**\n   * 处理任务状态变更的级联影响\n   */\n  async handleTaskStatusChange({ dispatch, commit }, { taskId, oldStatus, newStatus, projectId }) {\n    try {\n      // 重新计算项目统计\n      await dispatch('calculateProjectStats', projectId)\n\n      // 如果任务完成，检查是否有依赖任务可以开始\n      if (newStatus === 'completed') {\n        await dispatch('checkDependentTasks', taskId)\n      }\n\n      // 如果任务延期，创建风险记录\n      if (newStatus === 'overdue') {\n        await dispatch('createTaskDelayRisk', { taskId, projectId })\n      }\n\n      // 通知相关模块更新\n      await dispatch('notifyModulesUpdate', {\n        type: 'task_status_change',\n        taskId,\n        projectId,\n        oldStatus,\n        newStatus\n      })\n\n    } catch (error) {\n      console.error('处理任务状态变更失败:', error)\n    }\n  },\n\n  /**\n   * 处理项目状态变更的级联影响\n   */\n  async handleProjectStatusChange({ dispatch, state }, { projectId, oldStatus, newStatus }) {\n    try {\n      const relations = state.projectRelations[projectId]\n      if (!relations) return\n\n      // 如果项目完成，自动完成所有未完成任务\n      if (newStatus === 'completed') {\n        const incompleteTasks = relations.tasks.filter(t => t.status !== 'completed')\n        for (const task of incompleteTasks) {\n          await dispatch('tasks/updateTaskStatus', {\n            taskId: task.id,\n            status: 'completed'\n          }, { root: true })\n        }\n\n        // 关闭所有开放的风险\n        const openRisks = relations.risks.filter(r => r.status === 'open')\n        for (const risk of openRisks) {\n          await dispatch('risks/updateRiskStatus', {\n            riskId: risk.id,\n            status: 'closed'\n          }, { root: true })\n        }\n      }\n\n      // 如果项目暂停，暂停所有进行中的任务\n      if (newStatus === 'paused') {\n        const activeTasks = relations.tasks.filter(t => t.status === 'in-progress')\n        for (const task of activeTasks) {\n          await dispatch('tasks/updateTaskStatus', {\n            taskId: task.id,\n            status: 'paused'\n          }, { root: true })\n        }\n      }\n\n      // 重新同步项目关联数据\n      await dispatch('syncProjectRelations', projectId)\n\n    } catch (error) {\n      console.error('处理项目状态变更失败:', error)\n    }\n  },\n\n  /**\n   * 创建任务延期风险\n   */\n  async createTaskDelayRisk({ dispatch }, { taskId, projectId }) {\n    try {\n      const riskData = {\n        title: `任务延期风险 - 任务ID: ${taskId}`,\n        description: '任务超过预期完成时间，可能影响项目进度',\n        projectId: projectId,\n        category: 'schedule',\n        impactLevel: 'medium',\n        status: 'open',\n        relatedTaskId: taskId\n      }\n\n      await dispatch('risks/createRisk', riskData, { root: true })\n    } catch (error) {\n      console.error('创建任务延期风险失败:', error)\n    }\n  },\n\n  /**\n   * 检查依赖任务\n   */\n  async checkDependentTasks({ dispatch, state }, taskId) {\n    try {\n      const dependencies = state.taskDependencies[taskId]\n      if (!dependencies || !dependencies.dependents) return\n\n      // 检查依赖此任务的其他任务是否可以开始\n      for (const dependentTaskId of dependencies.dependents) {\n        await dispatch('tasks/checkTaskCanStart', dependentTaskId, { root: true })\n      }\n    } catch (error) {\n      console.error('检查依赖任务失败:', error)\n    }\n  },\n\n  /**\n   * 通知模块更新\n   */\n  async notifyModulesUpdate({ dispatch }, { type, ...data }) {\n    try {\n      // 根据事件类型通知相关模块\n      switch (type) {\n        case 'task_status_change':\n          // 通知甘特图更新\n          dispatch('gantt/refreshData', data.projectId, { root: true })\n          // 通知看板更新\n          dispatch('kanban/refreshData', data.projectId, { root: true })\n          break\n\n        case 'project_status_change':\n          // 通知所有相关视图更新\n          dispatch('dashboard/refreshProjectData', data.projectId, { root: true })\n          break\n\n        case 'risk_status_change':\n          // 通知风险仪表盘更新\n          dispatch('dashboard/refreshRiskData', data.projectId, { root: true })\n          break\n      }\n    } catch (error) {\n      console.error('通知模块更新失败:', error)\n    }\n  },\n\n  /**\n   * 删除项目及其关联数据\n   */\n  async deleteProjectWithRelations({ dispatch, commit }, projectId) {\n    try {\n      // 删除关联的任务\n      await dispatch('tasks/deleteTasksByProject', projectId, { root: true })\n\n      // 删除关联的风险\n      await dispatch('risks/deleteRisksByProject', projectId, { root: true })\n\n      // 删除关联的会议\n      await dispatch('meetings/deleteMeetingsByProject', projectId, { root: true })\n\n      // 删除项目\n      await dispatch('projects/deleteProject', projectId, { root: true })\n\n      // 清理关联数据缓存\n      commit('CLEAR_PROJECT_RELATIONS', projectId)\n\n      dispatch('setSuccess', '项目及其关联数据删除成功', { root: true })\n    } catch (error) {\n      dispatch('setError', `删除项目失败: ${error.message}`, { root: true })\n      throw error\n    }\n  }\n}\n\nconst getters = {\n  // 获取项目关联数据\n  getProjectRelations: state => projectId => state.projectRelations[projectId],\n\n  // 获取项目统计数据\n  getProjectStats: state => projectId => state.projectStats[projectId],\n\n  // 获取任务依赖关系\n  getTaskDependencies: state => taskId => state.taskDependencies[taskId],\n\n  // 检查同步状态\n  isSyncInProgress: state => state.syncInProgress,\n\n  // 获取最后同步时间\n  getLastSyncTime: state => state.lastSyncTime,\n\n  // 计算项目健康度\n  calculateProjectHealth: () => (taskStats, riskStats) => {\n    let score = 100\n\n    // 任务延期扣分\n    if (taskStats.overdue > 0) {\n      score -= Math.min(taskStats.overdue * 10, 30)\n    }\n\n    // 高风险扣分\n    if (riskStats.high > 0) {\n      score -= Math.min(riskStats.high * 15, 40)\n    }\n\n    // 关键风险扣分\n    if (riskStats.critical > 0) {\n      score -= Math.min(riskStats.critical * 25, 50)\n    }\n\n    // 任务完成率加分\n    if (taskStats.total > 0) {\n      const completionRate = taskStats.completed / taskStats.total\n      score += completionRate * 20\n    }\n\n    return Math.max(0, Math.min(100, Math.round(score)))\n  }\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions,\n  getters\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,MAAMA,KAAK,GAAG;EACZ;EACAC,gBAAgB,EAAE,CAAC,CAAC;EAAE;EACtBC,gBAAgB,EAAE,CAAC,CAAC;EAAE;;EAEtB;EACAC,YAAY,EAAE,CAAC,CAAC;EAAE;;EAElB;EACAC,YAAY,EAAE,IAAI;EAClBC,cAAc,EAAE;AAClB,CAAC;AAED,MAAMC,SAAS,GAAG;EAChBC,qBAAqBA,CAACP,KAAK,EAAE;IAAEQ,SAAS;IAAEC;EAAU,CAAC,EAAE;IACrDT,KAAK,CAACC,gBAAgB,GAAG;MACvB,GAAGD,KAAK,CAACC,gBAAgB;MACzB,CAACO,SAAS,GAAGC;IACf,CAAC;EACH,CAAC;EAEDC,iBAAiBA,CAACV,KAAK,EAAE;IAAEQ,SAAS;IAAEG;EAAM,CAAC,EAAE;IAC7CX,KAAK,CAACG,YAAY,GAAG;MACnB,GAAGH,KAAK,CAACG,YAAY;MACrB,CAACK,SAAS,GAAGG;IACf,CAAC;EACH,CAAC;EAEDC,qBAAqBA,CAACZ,KAAK,EAAE;IAAEa,MAAM;IAAEC;EAAa,CAAC,EAAE;IACrDd,KAAK,CAACE,gBAAgB,GAAG;MACvB,GAAGF,KAAK,CAACE,gBAAgB;MACzB,CAACW,MAAM,GAAGC;IACZ,CAAC;EACH,CAAC;EAEDC,eAAeA,CAACf,KAAK,EAAEgB,MAAM,EAAE;IAC7BhB,KAAK,CAACK,cAAc,GAAGW,MAAM;EAC/B,CAAC;EAEDC,kBAAkBA,CAACjB,KAAK,EAAEkB,IAAI,EAAE;IAC9BlB,KAAK,CAACI,YAAY,GAAGc,IAAI;EAC3B,CAAC;EAEDC,uBAAuBA,CAACnB,KAAK,EAAEQ,SAAS,EAAE;IACxC,OAAOR,KAAK,CAACC,gBAAgB,CAACO,SAAS,CAAC;IACxC,OAAOR,KAAK,CAACG,YAAY,CAACK,SAAS,CAAC;EACtC;AACF,CAAC;AAED,MAAMY,OAAO,GAAG;EACd;AACF;AACA;EACE,MAAMC,oBAAoBA,CAAC;IAAEC,MAAM;IAAEC,QAAQ;IAAEC;EAAY,CAAC,EAAEhB,SAAS,EAAE;IACvE,IAAI;MACFc,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC;;MAE/B;MACA,MAAM,CAACG,KAAK,EAAEC,KAAK,EAAEC,QAAQ,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACjDN,QAAQ,CAAC,2BAA2B,EAAEf,SAAS,EAAE;QAAEsB,IAAI,EAAE;MAAK,CAAC,CAAC,EAChEP,QAAQ,CAAC,2BAA2B,EAAEf,SAAS,EAAE;QAAEsB,IAAI,EAAE;MAAK,CAAC,CAAC,EAChEP,QAAQ,CAAC,iCAAiC,EAAEf,SAAS,EAAE;QAAEsB,IAAI,EAAE;MAAK,CAAC,CAAC,CACvE,CAAC;;MAEF;MACA,MAAMrB,SAAS,GAAG;QAChBgB,KAAK,EAAEA,KAAK,IAAI,EAAE;QAClBC,KAAK,EAAEA,KAAK,IAAI,EAAE;QAClBC,QAAQ,EAAEA,QAAQ,IAAI;MACxB,CAAC;MAEDL,MAAM,CAAC,uBAAuB,EAAE;QAAEd,SAAS;QAAEC;MAAU,CAAC,CAAC;;MAEzD;MACA,MAAMc,QAAQ,CAAC,uBAAuB,EAAEf,SAAS,CAAC;MAElDc,MAAM,CAAC,oBAAoB,EAAE,IAAIS,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;MAEtD,OAAOvB,SAAS;IAClB,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdV,QAAQ,CAAC,UAAU,EAAE,eAAeU,KAAK,CAACC,OAAO,EAAE,EAAE;QAAEJ,IAAI,EAAE;MAAK,CAAC,CAAC;MACpE,MAAMG,KAAK;IACb,CAAC,SAAS;MACRX,MAAM,CAAC,iBAAiB,EAAE,KAAK,CAAC;IAClC;EACF,CAAC;EAED;AACF;AACA;EACE,MAAMa,qBAAqBA,CAAC;IAAEb,MAAM;IAAEtB,KAAK;IAAEwB;EAAY,CAAC,EAAEhB,SAAS,EAAE;IACrE,IAAI;MACF,MAAMC,SAAS,GAAGT,KAAK,CAACC,gBAAgB,CAACO,SAAS,CAAC;MACnD,IAAI,CAACC,SAAS,EAAE,OAAO,IAAI;MAE3B,MAAM;QAAEgB,KAAK;QAAEC,KAAK;QAAEC;MAAS,CAAC,GAAGlB,SAAS;;MAE5C;MACA,MAAM2B,SAAS,GAAG;QAChBC,KAAK,EAAEZ,KAAK,CAACa,MAAM;QACnBC,SAAS,EAAEd,KAAK,CAACe,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzB,MAAM,KAAK,WAAW,CAAC,CAACsB,MAAM;QAC7DI,UAAU,EAAEjB,KAAK,CAACe,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzB,MAAM,KAAK,aAAa,CAAC,CAACsB,MAAM;QAChEK,OAAO,EAAElB,KAAK,CAACe,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzB,MAAM,KAAK,SAAS,CAAC,CAACsB,MAAM;QACzDM,OAAO,EAAEnB,KAAK,CAACe,MAAM,CAACC,CAAC,IAAI;UACzB,MAAMI,OAAO,GAAG,IAAId,IAAI,CAACU,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACK,OAAO,CAAC;UAChD,OAAOL,CAAC,CAACzB,MAAM,KAAK,WAAW,IAAI6B,OAAO,GAAG,IAAId,IAAI,CAAC,CAAC;QACzD,CAAC,CAAC,CAACO;MACL,CAAC;;MAED;MACA,MAAMS,SAAS,GAAG;QAChBV,KAAK,EAAEX,KAAK,CAACY,MAAM;QACnBU,IAAI,EAAEtB,KAAK,CAACc,MAAM,CAACS,CAAC,IAAIA,CAAC,CAACjC,MAAM,KAAK,MAAM,CAAC,CAACsB,MAAM;QACnDY,UAAU,EAAExB,KAAK,CAACc,MAAM,CAACS,CAAC,IAAIA,CAAC,CAACjC,MAAM,KAAK,YAAY,CAAC,CAACsB,MAAM;QAC/Da,SAAS,EAAEzB,KAAK,CAACc,MAAM,CAACS,CAAC,IAAIA,CAAC,CAACjC,MAAM,KAAK,WAAW,CAAC,CAACsB,MAAM;QAC7Dc,MAAM,EAAE1B,KAAK,CAACc,MAAM,CAACS,CAAC,IAAIA,CAAC,CAACjC,MAAM,KAAK,QAAQ,CAAC,CAACsB,MAAM;QACvDe,IAAI,EAAE3B,KAAK,CAACc,MAAM,CAACS,CAAC,IAAIA,CAAC,CAACK,WAAW,KAAK,MAAM,IAAIL,CAAC,CAACM,KAAK,KAAK,MAAM,CAAC,CAACjB,MAAM;QAC9EkB,QAAQ,EAAE9B,KAAK,CAACc,MAAM,CAACS,CAAC,IAAIA,CAAC,CAACK,WAAW,KAAK,UAAU,IAAIL,CAAC,CAACM,KAAK,KAAK,UAAU,CAAC,CAACjB;MACtF,CAAC;;MAED;MACA,MAAMmB,YAAY,GAAG;QACnBpB,KAAK,EAAEV,QAAQ,CAACW,MAAM;QACtBoB,QAAQ,EAAE/B,QAAQ,CAACa,MAAM,CAACmB,CAAC,IAAI,IAAI5B,IAAI,CAAC4B,CAAC,CAACC,SAAS,CAAC,GAAG,IAAI7B,IAAI,CAAC,CAAC,CAAC,CAACO,MAAM;QACzEC,SAAS,EAAEZ,QAAQ,CAACa,MAAM,CAACmB,CAAC,IAAIA,CAAC,CAAC3C,MAAM,KAAK,WAAW,CAAC,CAACsB;MAC5D,CAAC;;MAED;MACA,MAAMuB,QAAQ,GAAGzB,SAAS,CAACC,KAAK,GAAG,CAAC,GAChCyB,IAAI,CAACC,KAAK,CAAE3B,SAAS,CAACG,SAAS,GAAGH,SAAS,CAACC,KAAK,GAAI,GAAG,CAAC,GACzD,CAAC;;MAEL;MACA,MAAM2B,WAAW,GAAGC,sBAAsB,CAAC7B,SAAS,EAAEW,SAAS,CAAC;MAEhE,MAAMpC,KAAK,GAAG;QACZkD,QAAQ;QACRG,WAAW;QACX5B,SAAS;QACTW,SAAS;QACTU,YAAY;QACZS,WAAW,EAAE,IAAInC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACtC,CAAC;MAEDV,MAAM,CAAC,mBAAmB,EAAE;QAAEd,SAAS;QAAEG;MAAM,CAAC,CAAC;;MAEjD;MACA,MAAMY,QAAQ,CAAC,gCAAgC,EAAE;QAC/Cf,SAAS;QACTqD;MACF,CAAC,EAAE;QAAE/B,IAAI,EAAE;MAAK,CAAC,CAAC;MAElB,OAAOnB,KAAK;IACd,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdkC,OAAO,CAAClC,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC,MAAMA,KAAK;IACb;EACF,CAAC;EAED;AACF;AACA;EACE,MAAMmC,sBAAsBA,CAAC;IAAE7C,QAAQ;IAAED;EAAO,CAAC,EAAE;IAAET,MAAM;IAAEwD,SAAS;IAAEC,SAAS;IAAE9D;EAAU,CAAC,EAAE;IAC9F,IAAI;MACF;MACA,MAAMe,QAAQ,CAAC,uBAAuB,EAAEf,SAAS,CAAC;;MAElD;MACA,IAAI8D,SAAS,KAAK,WAAW,EAAE;QAC7B,MAAM/C,QAAQ,CAAC,qBAAqB,EAAEV,MAAM,CAAC;MAC/C;;MAEA;MACA,IAAIyD,SAAS,KAAK,SAAS,EAAE;QAC3B,MAAM/C,QAAQ,CAAC,qBAAqB,EAAE;UAAEV,MAAM;UAAEL;QAAU,CAAC,CAAC;MAC9D;;MAEA;MACA,MAAMe,QAAQ,CAAC,qBAAqB,EAAE;QACpCgD,IAAI,EAAE,oBAAoB;QAC1B1D,MAAM;QACNL,SAAS;QACT6D,SAAS;QACTC;MACF,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACdkC,OAAO,CAAClC,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACrC;EACF,CAAC;EAED;AACF;AACA;EACE,MAAMuC,yBAAyBA,CAAC;IAAEjD,QAAQ;IAAEvB;EAAM,CAAC,EAAE;IAAEQ,SAAS;IAAE6D,SAAS;IAAEC;EAAU,CAAC,EAAE;IACxF,IAAI;MACF,MAAM7D,SAAS,GAAGT,KAAK,CAACC,gBAAgB,CAACO,SAAS,CAAC;MACnD,IAAI,CAACC,SAAS,EAAE;;MAEhB;MACA,IAAI6D,SAAS,KAAK,WAAW,EAAE;QAC7B,MAAMG,eAAe,GAAGhE,SAAS,CAACgB,KAAK,CAACe,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzB,MAAM,KAAK,WAAW,CAAC;QAC7E,KAAK,MAAM0D,IAAI,IAAID,eAAe,EAAE;UAClC,MAAMlD,QAAQ,CAAC,wBAAwB,EAAE;YACvCV,MAAM,EAAE6D,IAAI,CAACC,EAAE;YACf3D,MAAM,EAAE;UACV,CAAC,EAAE;YAAEc,IAAI,EAAE;UAAK,CAAC,CAAC;QACpB;;QAEA;QACA,MAAM8C,SAAS,GAAGnE,SAAS,CAACiB,KAAK,CAACc,MAAM,CAACS,CAAC,IAAIA,CAAC,CAACjC,MAAM,KAAK,MAAM,CAAC;QAClE,KAAK,MAAM6D,IAAI,IAAID,SAAS,EAAE;UAC5B,MAAMrD,QAAQ,CAAC,wBAAwB,EAAE;YACvCuD,MAAM,EAAED,IAAI,CAACF,EAAE;YACf3D,MAAM,EAAE;UACV,CAAC,EAAE;YAAEc,IAAI,EAAE;UAAK,CAAC,CAAC;QACpB;MACF;;MAEA;MACA,IAAIwC,SAAS,KAAK,QAAQ,EAAE;QAC1B,MAAMS,WAAW,GAAGtE,SAAS,CAACgB,KAAK,CAACe,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzB,MAAM,KAAK,aAAa,CAAC;QAC3E,KAAK,MAAM0D,IAAI,IAAIK,WAAW,EAAE;UAC9B,MAAMxD,QAAQ,CAAC,wBAAwB,EAAE;YACvCV,MAAM,EAAE6D,IAAI,CAACC,EAAE;YACf3D,MAAM,EAAE;UACV,CAAC,EAAE;YAAEc,IAAI,EAAE;UAAK,CAAC,CAAC;QACpB;MACF;;MAEA;MACA,MAAMP,QAAQ,CAAC,sBAAsB,EAAEf,SAAS,CAAC;IAEnD,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdkC,OAAO,CAAClC,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACrC;EACF,CAAC;EAED;AACF;AACA;EACE,MAAM+C,mBAAmBA,CAAC;IAAEzD;EAAS,CAAC,EAAE;IAAEV,MAAM;IAAEL;EAAU,CAAC,EAAE;IAC7D,IAAI;MACF,MAAMyE,QAAQ,GAAG;QACfC,KAAK,EAAE,kBAAkBrE,MAAM,EAAE;QACjCsE,WAAW,EAAE,qBAAqB;QAClC3E,SAAS,EAAEA,SAAS;QACpB4E,QAAQ,EAAE,UAAU;QACpB9B,WAAW,EAAE,QAAQ;QACrBtC,MAAM,EAAE,MAAM;QACdqE,aAAa,EAAExE;MACjB,CAAC;MAED,MAAMU,QAAQ,CAAC,kBAAkB,EAAE0D,QAAQ,EAAE;QAAEnD,IAAI,EAAE;MAAK,CAAC,CAAC;IAC9D,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdkC,OAAO,CAAClC,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACrC;EACF,CAAC;EAED;AACF;AACA;EACE,MAAMqD,mBAAmBA,CAAC;IAAE/D,QAAQ;IAAEvB;EAAM,CAAC,EAAEa,MAAM,EAAE;IACrD,IAAI;MACF,MAAMC,YAAY,GAAGd,KAAK,CAACE,gBAAgB,CAACW,MAAM,CAAC;MACnD,IAAI,CAACC,YAAY,IAAI,CAACA,YAAY,CAACyE,UAAU,EAAE;;MAE/C;MACA,KAAK,MAAMC,eAAe,IAAI1E,YAAY,CAACyE,UAAU,EAAE;QACrD,MAAMhE,QAAQ,CAAC,yBAAyB,EAAEiE,eAAe,EAAE;UAAE1D,IAAI,EAAE;QAAK,CAAC,CAAC;MAC5E;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdkC,OAAO,CAAClC,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;EAED;AACF;AACA;EACE,MAAMwD,mBAAmBA,CAAC;IAAElE;EAAS,CAAC,EAAE;IAAEgD,IAAI;IAAE,GAAGmB;EAAK,CAAC,EAAE;IACzD,IAAI;MACF;MACA,QAAQnB,IAAI;QACV,KAAK,oBAAoB;UACvB;UACAhD,QAAQ,CAAC,mBAAmB,EAAEmE,IAAI,CAAClF,SAAS,EAAE;YAAEsB,IAAI,EAAE;UAAK,CAAC,CAAC;UAC7D;UACAP,QAAQ,CAAC,oBAAoB,EAAEmE,IAAI,CAAClF,SAAS,EAAE;YAAEsB,IAAI,EAAE;UAAK,CAAC,CAAC;UAC9D;QAEF,KAAK,uBAAuB;UAC1B;UACAP,QAAQ,CAAC,8BAA8B,EAAEmE,IAAI,CAAClF,SAAS,EAAE;YAAEsB,IAAI,EAAE;UAAK,CAAC,CAAC;UACxE;QAEF,KAAK,oBAAoB;UACvB;UACAP,QAAQ,CAAC,2BAA2B,EAAEmE,IAAI,CAAClF,SAAS,EAAE;YAAEsB,IAAI,EAAE;UAAK,CAAC,CAAC;UACrE;MACJ;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdkC,OAAO,CAAClC,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;EAED;AACF;AACA;EACE,MAAM0D,0BAA0BA,CAAC;IAAEpE,QAAQ;IAAED;EAAO,CAAC,EAAEd,SAAS,EAAE;IAChE,IAAI;MACF;MACA,MAAMe,QAAQ,CAAC,4BAA4B,EAAEf,SAAS,EAAE;QAAEsB,IAAI,EAAE;MAAK,CAAC,CAAC;;MAEvE;MACA,MAAMP,QAAQ,CAAC,4BAA4B,EAAEf,SAAS,EAAE;QAAEsB,IAAI,EAAE;MAAK,CAAC,CAAC;;MAEvE;MACA,MAAMP,QAAQ,CAAC,kCAAkC,EAAEf,SAAS,EAAE;QAAEsB,IAAI,EAAE;MAAK,CAAC,CAAC;;MAE7E;MACA,MAAMP,QAAQ,CAAC,wBAAwB,EAAEf,SAAS,EAAE;QAAEsB,IAAI,EAAE;MAAK,CAAC,CAAC;;MAEnE;MACAR,MAAM,CAAC,yBAAyB,EAAEd,SAAS,CAAC;MAE5Ce,QAAQ,CAAC,YAAY,EAAE,cAAc,EAAE;QAAEO,IAAI,EAAE;MAAK,CAAC,CAAC;IACxD,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdV,QAAQ,CAAC,UAAU,EAAE,WAAWU,KAAK,CAACC,OAAO,EAAE,EAAE;QAAEJ,IAAI,EAAE;MAAK,CAAC,CAAC;MAChE,MAAMG,KAAK;IACb;EACF;AACF,CAAC;AAED,MAAM2D,OAAO,GAAG;EACd;EACAC,mBAAmB,EAAE7F,KAAK,IAAIQ,SAAS,IAAIR,KAAK,CAACC,gBAAgB,CAACO,SAAS,CAAC;EAE5E;EACAsF,eAAe,EAAE9F,KAAK,IAAIQ,SAAS,IAAIR,KAAK,CAACG,YAAY,CAACK,SAAS,CAAC;EAEpE;EACAuF,mBAAmB,EAAE/F,KAAK,IAAIa,MAAM,IAAIb,KAAK,CAACE,gBAAgB,CAACW,MAAM,CAAC;EAEtE;EACAmF,gBAAgB,EAAEhG,KAAK,IAAIA,KAAK,CAACK,cAAc;EAE/C;EACA4F,eAAe,EAAEjG,KAAK,IAAIA,KAAK,CAACI,YAAY;EAE5C;EACA6D,sBAAsB,EAAEA,CAAA,KAAM,CAAC7B,SAAS,EAAEW,SAAS,KAAK;IACtD,IAAImD,KAAK,GAAG,GAAG;;IAEf;IACA,IAAI9D,SAAS,CAACQ,OAAO,GAAG,CAAC,EAAE;MACzBsD,KAAK,IAAIpC,IAAI,CAACqC,GAAG,CAAC/D,SAAS,CAACQ,OAAO,GAAG,EAAE,EAAE,EAAE,CAAC;IAC/C;;IAEA;IACA,IAAIG,SAAS,CAACM,IAAI,GAAG,CAAC,EAAE;MACtB6C,KAAK,IAAIpC,IAAI,CAACqC,GAAG,CAACpD,SAAS,CAACM,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC;IAC5C;;IAEA;IACA,IAAIN,SAAS,CAACS,QAAQ,GAAG,CAAC,EAAE;MAC1B0C,KAAK,IAAIpC,IAAI,CAACqC,GAAG,CAACpD,SAAS,CAACS,QAAQ,GAAG,EAAE,EAAE,EAAE,CAAC;IAChD;;IAEA;IACA,IAAIpB,SAAS,CAACC,KAAK,GAAG,CAAC,EAAE;MACvB,MAAM+D,cAAc,GAAGhE,SAAS,CAACG,SAAS,GAAGH,SAAS,CAACC,KAAK;MAC5D6D,KAAK,IAAIE,cAAc,GAAG,EAAE;IAC9B;IAEA,OAAOtC,IAAI,CAACuC,GAAG,CAAC,CAAC,EAAEvC,IAAI,CAACqC,GAAG,CAAC,GAAG,EAAErC,IAAI,CAACC,KAAK,CAACmC,KAAK,CAAC,CAAC,CAAC;EACtD;AACF,CAAC;AAED,eAAe;EACbI,UAAU,EAAE,IAAI;EAChBtG,KAAK;EACLM,SAAS;EACTc,OAAO;EACPwE;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}