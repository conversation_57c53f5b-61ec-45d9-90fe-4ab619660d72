{"ast": null, "code": "/**\n * 工作流自动化服务\n * 提供工作流优化、自动化和智能分析功能\n */\n\nimport api from './api';\nclass WorkflowService {\n  constructor() {\n    this.automationRules = new Map();\n    this.workflowTemplates = new Map();\n    this.metrics = {\n      taskProcessingSpeed: 0,\n      avgResponseTime: 0,\n      automationRate: 0,\n      errorRate: 0\n    };\n  }\n\n  /**\n   * 工作流自动化规则管理\n   */\n\n  // 添加自动化规则\n  addAutomationRule(rule) {\n    this.automationRules.set(rule.id, {\n      ...rule,\n      createdAt: new Date(),\n      isActive: true,\n      executionCount: 0\n    });\n  }\n\n  // 执行自动化规则\n  async executeAutomationRule(ruleId, context) {\n    const rule = this.automationRules.get(ruleId);\n    if (!rule || !rule.isActive) return false;\n    try {\n      switch (rule.type) {\n        case 'task_auto_assignment':\n          return await this.autoAssignTask(context);\n        case 'status_auto_transition':\n          return await this.autoTransitionStatus(context);\n        case 'deadline_reminder':\n          return await this.sendDeadlineReminder(context);\n        case 'risk_auto_creation':\n          return await this.autoCreateRisk(context);\n        default:\n          return false;\n      }\n    } catch (error) {\n      console.error('自动化规则执行失败:', error);\n      return false;\n    }\n  }\n\n  /**\n   * 任务自动分配\n   */\n  async autoAssignTask(context) {\n    const {\n      taskId,\n      projectId\n    } = context;\n\n    // 获取团队成员工作负载\n    const teamMembers = await this.getTeamWorkload(projectId);\n\n    // 选择工作负载最轻的成员\n    const assignee = teamMembers.reduce((min, member) => member.workload < min.workload ? member : min);\n\n    // 分配任务\n    return await api.put(`/tasks/${taskId}`, {\n      assignee_id: assignee.id,\n      assigned_at: new Date().toISOString(),\n      assignment_reason: 'auto_assignment_by_workload'\n    });\n  }\n\n  /**\n   * 状态自动流转\n   */\n  async autoTransitionStatus(context) {\n    const {\n      taskId,\n      currentStatus,\n      projectId\n    } = context;\n\n    // 获取工作流配置\n    const workflow = await this.getProjectWorkflow(projectId);\n    const nextStatus = workflow.getNextStatus(currentStatus);\n    if (!nextStatus) return false;\n\n    // 检查流转条件\n    const canTransition = await this.checkTransitionConditions(taskId, nextStatus);\n    if (!canTransition) return false;\n\n    // 执行状态流转\n    return await api.put(`/tasks/${taskId}/status`, {\n      status: nextStatus,\n      transition_reason: 'auto_transition',\n      transition_time: new Date().toISOString()\n    });\n  }\n\n  /**\n   * 智能提醒系统\n   */\n  async sendDeadlineReminder(context) {\n    const {\n      taskId,\n      dueDate,\n      assigneeId\n    } = context;\n    const now = new Date();\n    const due = new Date(dueDate);\n    const hoursUntilDue = (due - now) / (1000 * 60 * 60);\n\n    // 根据紧急程度发送不同级别的提醒\n    let reminderType = 'normal';\n    if (hoursUntilDue <= 2) reminderType = 'urgent';else if (hoursUntilDue <= 24) reminderType = 'important';\n    return await api.post('/notifications', {\n      type: 'deadline_reminder',\n      recipient_id: assigneeId,\n      task_id: taskId,\n      priority: reminderType,\n      message: this.generateReminderMessage(hoursUntilDue, reminderType)\n    });\n  }\n\n  /**\n   * 风险自动创建\n   */\n  async autoCreateRisk(context) {\n    const {\n      taskId,\n      projectId,\n      riskType\n    } = context;\n    const riskData = {\n      project_id: projectId,\n      task_id: taskId,\n      title: this.generateRiskTitle(riskType),\n      description: this.generateRiskDescription(context),\n      probability: this.calculateRiskProbability(context),\n      impact: this.calculateRiskImpact(context),\n      status: 'open',\n      created_by: 'system',\n      auto_created: true\n    };\n    return await api.post('/risks', riskData);\n  }\n\n  /**\n   * 工作流模板管理\n   */\n\n  // 创建工作流模板\n  createWorkflowTemplate(template) {\n    const workflowTemplate = {\n      id: template.id,\n      name: template.name,\n      description: template.description,\n      type: template.type,\n      // medical, agile, waterfall\n      stages: template.stages,\n      automationRules: template.automationRules || [],\n      approvalFlow: template.approvalFlow || [],\n      createdAt: new Date()\n    };\n    this.workflowTemplates.set(template.id, workflowTemplate);\n    return workflowTemplate;\n  }\n\n  // 应用工作流模板\n  async applyWorkflowTemplate(projectId, templateId) {\n    const template = this.workflowTemplates.get(templateId);\n    if (!template) throw new Error('工作流模板不存在');\n    try {\n      // 应用阶段配置\n      await this.applyStageConfiguration(projectId, template.stages);\n\n      // 应用自动化规则\n      await this.applyAutomationRules(projectId, template.automationRules);\n\n      // 应用审批流程\n      await this.applyApprovalFlow(projectId, template.approvalFlow);\n      return {\n        success: true,\n        message: '工作流模板应用成功'\n      };\n    } catch (error) {\n      console.error('应用工作流模板失败:', error);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n\n  /**\n   * 工作流分析和优化\n   */\n\n  // 分析工作流效率\n  async analyzeWorkflowEfficiency(projectId, timeRange = 30) {\n    const endDate = new Date();\n    const startDate = new Date(endDate.getTime() - timeRange * 24 * 60 * 60 * 1000);\n    const [tasks, meetings, risks] = await Promise.all([api.get(`/tasks?project_id=${projectId}&start_date=${startDate.toISOString()}&end_date=${endDate.toISOString()}`), api.get(`/meetings?project_id=${projectId}&start_date=${startDate.toISOString()}&end_date=${endDate.toISOString()}`), api.get(`/risks?project_id=${projectId}&start_date=${startDate.toISOString()}&end_date=${endDate.toISOString()}`)]);\n    return {\n      taskMetrics: this.calculateTaskMetrics(tasks.data),\n      meetingMetrics: this.calculateMeetingMetrics(meetings.data),\n      riskMetrics: this.calculateRiskMetrics(risks.data),\n      overallEfficiency: this.calculateOverallEfficiency(tasks.data, meetings.data, risks.data),\n      bottlenecks: this.identifyBottlenecks(tasks.data),\n      suggestions: this.generateOptimizationSuggestions(tasks.data, meetings.data, risks.data)\n    };\n  }\n\n  // 生成优化建议\n  generateOptimizationSuggestions(tasks, meetings, risks) {\n    const suggestions = [];\n\n    // 分析任务延期情况\n    const overdueTasks = tasks.filter(task => new Date(task.due_date) < new Date() && task.status !== 'completed');\n    if (overdueTasks.length > tasks.length * 0.2) {\n      suggestions.push({\n        type: 'deadline_management',\n        title: '加强截止日期管理',\n        description: '当前有较多任务延期，建议启用智能提醒和自动预警',\n        impact: 'high',\n        action: 'enable_smart_reminders'\n      });\n    }\n\n    // 分析任务分配情况\n    const assignmentDistribution = this.analyzeTaskAssignment(tasks);\n    if (assignmentDistribution.imbalance > 0.3) {\n      suggestions.push({\n        type: 'workload_balance',\n        title: '优化工作负载分配',\n        description: '团队成员工作负载不均衡，建议启用自动分配',\n        impact: 'high',\n        action: 'enable_auto_assignment'\n      });\n    }\n\n    // 分析会议效率\n    const meetingEfficiency = this.calculateMeetingEfficiency(meetings);\n    if (meetingEfficiency < 0.7) {\n      suggestions.push({\n        type: 'meeting_optimization',\n        title: '优化会议管理',\n        description: '会议效率较低，建议制定会议标准和跟踪行动项',\n        impact: 'medium',\n        action: 'optimize_meetings'\n      });\n    }\n    return suggestions;\n  }\n\n  /**\n   * 辅助方法\n   */\n\n  generateReminderMessage(hoursUntilDue, type) {\n    if (type === 'urgent') {\n      return `⚠️ 紧急提醒：您的任务将在${Math.round(hoursUntilDue)}小时内到期，请尽快处理！`;\n    } else if (type === 'important') {\n      return `📅 重要提醒：您的任务将在${Math.round(hoursUntilDue)}小时内到期，请及时处理。`;\n    }\n    return `📋 友好提醒：您有任务即将到期，请关注进度。`;\n  }\n  calculateTaskMetrics(tasks) {\n    const completed = tasks.filter(t => t.status === 'completed').length;\n    const onTime = tasks.filter(t => t.status === 'completed' && new Date(t.completed_at) <= new Date(t.due_date)).length;\n    return {\n      completionRate: tasks.length > 0 ? completed / tasks.length * 100 : 0,\n      onTimeRate: completed > 0 ? onTime / completed * 100 : 0,\n      avgCompletionTime: this.calculateAvgCompletionTime(tasks),\n      totalTasks: tasks.length\n    };\n  }\n  calculateAvgCompletionTime(tasks) {\n    const completedTasks = tasks.filter(t => t.status === 'completed' && t.created_at && t.completed_at);\n    if (completedTasks.length === 0) return 0;\n    const totalTime = completedTasks.reduce((sum, task) => {\n      const created = new Date(task.created_at);\n      const completed = new Date(task.completed_at);\n      return sum + (completed - created);\n    }, 0);\n    return totalTime / completedTasks.length / (1000 * 60 * 60 * 24); // 转换为天数\n  }\n  async getTeamWorkload(projectId) {\n    // 模拟获取团队工作负载数据\n    return [{\n      id: 1,\n      name: '张三',\n      workload: 0.8\n    }, {\n      id: 2,\n      name: '李四',\n      workload: 0.6\n    }, {\n      id: 3,\n      name: '王五',\n      workload: 0.9\n    }];\n  }\n}\nexport default new WorkflowService();", "map": {"version": 3, "names": ["api", "WorkflowService", "constructor", "automationRules", "Map", "workflowTemplates", "metrics", "taskProcessingSpeed", "avgResponseTime", "automationRate", "errorRate", "addAutomationRule", "rule", "set", "id", "createdAt", "Date", "isActive", "executionCount", "executeAutomationRule", "ruleId", "context", "get", "type", "autoAssignTask", "autoTransitionStatus", "sendDeadlineReminder", "autoCreateRisk", "error", "console", "taskId", "projectId", "teamMembers", "getTeamWorkload", "assignee", "reduce", "min", "member", "workload", "put", "assignee_id", "assigned_at", "toISOString", "assignment_reason", "currentStatus", "workflow", "getProjectWorkflow", "nextStatus", "getNextStatus", "canTransition", "checkTransitionConditions", "status", "transition_reason", "transition_time", "dueDate", "assigneeId", "now", "due", "hoursUntilDue", "reminderType", "post", "recipient_id", "task_id", "priority", "message", "generateReminderMessage", "riskType", "riskData", "project_id", "title", "generateRiskTitle", "description", "generateRiskDescription", "probability", "calculateRiskProbability", "impact", "calculateRiskImpact", "created_by", "auto_created", "createWorkflowTemplate", "template", "workflowTemplate", "name", "stages", "approvalFlow", "applyWorkflowTemplate", "templateId", "Error", "applyStageConfiguration", "applyAutomationRules", "applyApprovalFlow", "success", "analyzeWorkflowEfficiency", "timeRange", "endDate", "startDate", "getTime", "tasks", "meetings", "risks", "Promise", "all", "taskMetrics", "calculateTaskMetrics", "data", "meetingMetrics", "calculateMeetingMetrics", "riskMetrics", "calculateRiskMetrics", "overallEfficiency", "calculateOverallEfficiency", "bottlenecks", "identifyBottlenecks", "suggestions", "generateOptimizationSuggestions", "overdueTasks", "filter", "task", "due_date", "length", "push", "action", "assignmentDistribution", "analyzeTaskAssignment", "imbalance", "meetingEfficiency", "calculateMeetingEfficiency", "Math", "round", "completed", "t", "onTime", "completed_at", "completionRate", "onTimeRate", "avgCompletionTime", "calculateAvgCompletionTime", "totalTasks", "completedTasks", "created_at", "totalTime", "sum", "created"], "sources": ["/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/技术测试/项目实施进度管理/project-management/frontend/src/services/workflowService.js"], "sourcesContent": ["/**\n * 工作流自动化服务\n * 提供工作流优化、自动化和智能分析功能\n */\n\nimport api from './api'\n\nclass WorkflowService {\n  constructor() {\n    this.automationRules = new Map()\n    this.workflowTemplates = new Map()\n    this.metrics = {\n      taskProcessingSpeed: 0,\n      avgResponseTime: 0,\n      automationRate: 0,\n      errorRate: 0\n    }\n  }\n\n  /**\n   * 工作流自动化规则管理\n   */\n  \n  // 添加自动化规则\n  addAutomationRule(rule) {\n    this.automationRules.set(rule.id, {\n      ...rule,\n      createdAt: new Date(),\n      isActive: true,\n      executionCount: 0\n    })\n  }\n\n  // 执行自动化规则\n  async executeAutomationRule(ruleId, context) {\n    const rule = this.automationRules.get(ruleId)\n    if (!rule || !rule.isActive) return false\n\n    try {\n      switch (rule.type) {\n        case 'task_auto_assignment':\n          return await this.autoAssignTask(context)\n        case 'status_auto_transition':\n          return await this.autoTransitionStatus(context)\n        case 'deadline_reminder':\n          return await this.sendDeadlineReminder(context)\n        case 'risk_auto_creation':\n          return await this.autoCreateRisk(context)\n        default:\n          return false\n      }\n    } catch (error) {\n      console.error('自动化规则执行失败:', error)\n      return false\n    }\n  }\n\n  /**\n   * 任务自动分配\n   */\n  async autoAssignTask(context) {\n    const { taskId, projectId } = context\n    \n    // 获取团队成员工作负载\n    const teamMembers = await this.getTeamWorkload(projectId)\n    \n    // 选择工作负载最轻的成员\n    const assignee = teamMembers.reduce((min, member) => \n      member.workload < min.workload ? member : min\n    )\n\n    // 分配任务\n    return await api.put(`/tasks/${taskId}`, {\n      assignee_id: assignee.id,\n      assigned_at: new Date().toISOString(),\n      assignment_reason: 'auto_assignment_by_workload'\n    })\n  }\n\n  /**\n   * 状态自动流转\n   */\n  async autoTransitionStatus(context) {\n    const { taskId, currentStatus, projectId } = context\n    \n    // 获取工作流配置\n    const workflow = await this.getProjectWorkflow(projectId)\n    const nextStatus = workflow.getNextStatus(currentStatus)\n    \n    if (!nextStatus) return false\n\n    // 检查流转条件\n    const canTransition = await this.checkTransitionConditions(taskId, nextStatus)\n    if (!canTransition) return false\n\n    // 执行状态流转\n    return await api.put(`/tasks/${taskId}/status`, {\n      status: nextStatus,\n      transition_reason: 'auto_transition',\n      transition_time: new Date().toISOString()\n    })\n  }\n\n  /**\n   * 智能提醒系统\n   */\n  async sendDeadlineReminder(context) {\n    const { taskId, dueDate, assigneeId } = context\n    const now = new Date()\n    const due = new Date(dueDate)\n    const hoursUntilDue = (due - now) / (1000 * 60 * 60)\n\n    // 根据紧急程度发送不同级别的提醒\n    let reminderType = 'normal'\n    if (hoursUntilDue <= 2) reminderType = 'urgent'\n    else if (hoursUntilDue <= 24) reminderType = 'important'\n\n    return await api.post('/notifications', {\n      type: 'deadline_reminder',\n      recipient_id: assigneeId,\n      task_id: taskId,\n      priority: reminderType,\n      message: this.generateReminderMessage(hoursUntilDue, reminderType)\n    })\n  }\n\n  /**\n   * 风险自动创建\n   */\n  async autoCreateRisk(context) {\n    const { taskId, projectId, riskType } = context\n    \n    const riskData = {\n      project_id: projectId,\n      task_id: taskId,\n      title: this.generateRiskTitle(riskType),\n      description: this.generateRiskDescription(context),\n      probability: this.calculateRiskProbability(context),\n      impact: this.calculateRiskImpact(context),\n      status: 'open',\n      created_by: 'system',\n      auto_created: true\n    }\n\n    return await api.post('/risks', riskData)\n  }\n\n  /**\n   * 工作流模板管理\n   */\n  \n  // 创建工作流模板\n  createWorkflowTemplate(template) {\n    const workflowTemplate = {\n      id: template.id,\n      name: template.name,\n      description: template.description,\n      type: template.type, // medical, agile, waterfall\n      stages: template.stages,\n      automationRules: template.automationRules || [],\n      approvalFlow: template.approvalFlow || [],\n      createdAt: new Date()\n    }\n    \n    this.workflowTemplates.set(template.id, workflowTemplate)\n    return workflowTemplate\n  }\n\n  // 应用工作流模板\n  async applyWorkflowTemplate(projectId, templateId) {\n    const template = this.workflowTemplates.get(templateId)\n    if (!template) throw new Error('工作流模板不存在')\n\n    try {\n      // 应用阶段配置\n      await this.applyStageConfiguration(projectId, template.stages)\n      \n      // 应用自动化规则\n      await this.applyAutomationRules(projectId, template.automationRules)\n      \n      // 应用审批流程\n      await this.applyApprovalFlow(projectId, template.approvalFlow)\n      \n      return { success: true, message: '工作流模板应用成功' }\n    } catch (error) {\n      console.error('应用工作流模板失败:', error)\n      return { success: false, error: error.message }\n    }\n  }\n\n  /**\n   * 工作流分析和优化\n   */\n  \n  // 分析工作流效率\n  async analyzeWorkflowEfficiency(projectId, timeRange = 30) {\n    const endDate = new Date()\n    const startDate = new Date(endDate.getTime() - timeRange * 24 * 60 * 60 * 1000)\n    \n    const [tasks, meetings, risks] = await Promise.all([\n      api.get(`/tasks?project_id=${projectId}&start_date=${startDate.toISOString()}&end_date=${endDate.toISOString()}`),\n      api.get(`/meetings?project_id=${projectId}&start_date=${startDate.toISOString()}&end_date=${endDate.toISOString()}`),\n      api.get(`/risks?project_id=${projectId}&start_date=${startDate.toISOString()}&end_date=${endDate.toISOString()}`)\n    ])\n\n    return {\n      taskMetrics: this.calculateTaskMetrics(tasks.data),\n      meetingMetrics: this.calculateMeetingMetrics(meetings.data),\n      riskMetrics: this.calculateRiskMetrics(risks.data),\n      overallEfficiency: this.calculateOverallEfficiency(tasks.data, meetings.data, risks.data),\n      bottlenecks: this.identifyBottlenecks(tasks.data),\n      suggestions: this.generateOptimizationSuggestions(tasks.data, meetings.data, risks.data)\n    }\n  }\n\n  // 生成优化建议\n  generateOptimizationSuggestions(tasks, meetings, risks) {\n    const suggestions = []\n    \n    // 分析任务延期情况\n    const overdueTasks = tasks.filter(task => new Date(task.due_date) < new Date() && task.status !== 'completed')\n    if (overdueTasks.length > tasks.length * 0.2) {\n      suggestions.push({\n        type: 'deadline_management',\n        title: '加强截止日期管理',\n        description: '当前有较多任务延期，建议启用智能提醒和自动预警',\n        impact: 'high',\n        action: 'enable_smart_reminders'\n      })\n    }\n\n    // 分析任务分配情况\n    const assignmentDistribution = this.analyzeTaskAssignment(tasks)\n    if (assignmentDistribution.imbalance > 0.3) {\n      suggestions.push({\n        type: 'workload_balance',\n        title: '优化工作负载分配',\n        description: '团队成员工作负载不均衡，建议启用自动分配',\n        impact: 'high',\n        action: 'enable_auto_assignment'\n      })\n    }\n\n    // 分析会议效率\n    const meetingEfficiency = this.calculateMeetingEfficiency(meetings)\n    if (meetingEfficiency < 0.7) {\n      suggestions.push({\n        type: 'meeting_optimization',\n        title: '优化会议管理',\n        description: '会议效率较低，建议制定会议标准和跟踪行动项',\n        impact: 'medium',\n        action: 'optimize_meetings'\n      })\n    }\n\n    return suggestions\n  }\n\n  /**\n   * 辅助方法\n   */\n  \n  generateReminderMessage(hoursUntilDue, type) {\n    if (type === 'urgent') {\n      return `⚠️ 紧急提醒：您的任务将在${Math.round(hoursUntilDue)}小时内到期，请尽快处理！`\n    } else if (type === 'important') {\n      return `📅 重要提醒：您的任务将在${Math.round(hoursUntilDue)}小时内到期，请及时处理。`\n    }\n    return `📋 友好提醒：您有任务即将到期，请关注进度。`\n  }\n\n  calculateTaskMetrics(tasks) {\n    const completed = tasks.filter(t => t.status === 'completed').length\n    const onTime = tasks.filter(t => t.status === 'completed' && new Date(t.completed_at) <= new Date(t.due_date)).length\n    \n    return {\n      completionRate: tasks.length > 0 ? (completed / tasks.length) * 100 : 0,\n      onTimeRate: completed > 0 ? (onTime / completed) * 100 : 0,\n      avgCompletionTime: this.calculateAvgCompletionTime(tasks),\n      totalTasks: tasks.length\n    }\n  }\n\n  calculateAvgCompletionTime(tasks) {\n    const completedTasks = tasks.filter(t => t.status === 'completed' && t.created_at && t.completed_at)\n    if (completedTasks.length === 0) return 0\n    \n    const totalTime = completedTasks.reduce((sum, task) => {\n      const created = new Date(task.created_at)\n      const completed = new Date(task.completed_at)\n      return sum + (completed - created)\n    }, 0)\n    \n    return totalTime / completedTasks.length / (1000 * 60 * 60 * 24) // 转换为天数\n  }\n\n  async getTeamWorkload(projectId) {\n    // 模拟获取团队工作负载数据\n    return [\n      { id: 1, name: '张三', workload: 0.8 },\n      { id: 2, name: '李四', workload: 0.6 },\n      { id: 3, name: '王五', workload: 0.9 }\n    ]\n  }\n}\n\nexport default new WorkflowService()\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,OAAOA,GAAG,MAAM,OAAO;AAEvB,MAAMC,eAAe,CAAC;EACpBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;IAChC,IAAI,CAACC,iBAAiB,GAAG,IAAID,GAAG,CAAC,CAAC;IAClC,IAAI,CAACE,OAAO,GAAG;MACbC,mBAAmB,EAAE,CAAC;MACtBC,eAAe,EAAE,CAAC;MAClBC,cAAc,EAAE,CAAC;MACjBC,SAAS,EAAE;IACb,CAAC;EACH;;EAEA;AACF;AACA;;EAEE;EACAC,iBAAiBA,CAACC,IAAI,EAAE;IACtB,IAAI,CAACT,eAAe,CAACU,GAAG,CAACD,IAAI,CAACE,EAAE,EAAE;MAChC,GAAGF,IAAI;MACPG,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;MACrBC,QAAQ,EAAE,IAAI;MACdC,cAAc,EAAE;IAClB,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMC,qBAAqBA,CAACC,MAAM,EAAEC,OAAO,EAAE;IAC3C,MAAMT,IAAI,GAAG,IAAI,CAACT,eAAe,CAACmB,GAAG,CAACF,MAAM,CAAC;IAC7C,IAAI,CAACR,IAAI,IAAI,CAACA,IAAI,CAACK,QAAQ,EAAE,OAAO,KAAK;IAEzC,IAAI;MACF,QAAQL,IAAI,CAACW,IAAI;QACf,KAAK,sBAAsB;UACzB,OAAO,MAAM,IAAI,CAACC,cAAc,CAACH,OAAO,CAAC;QAC3C,KAAK,wBAAwB;UAC3B,OAAO,MAAM,IAAI,CAACI,oBAAoB,CAACJ,OAAO,CAAC;QACjD,KAAK,mBAAmB;UACtB,OAAO,MAAM,IAAI,CAACK,oBAAoB,CAACL,OAAO,CAAC;QACjD,KAAK,oBAAoB;UACvB,OAAO,MAAM,IAAI,CAACM,cAAc,CAACN,OAAO,CAAC;QAC3C;UACE,OAAO,KAAK;MAChB;IACF,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,OAAO,KAAK;IACd;EACF;;EAEA;AACF;AACA;EACE,MAAMJ,cAAcA,CAACH,OAAO,EAAE;IAC5B,MAAM;MAAES,MAAM;MAAEC;IAAU,CAAC,GAAGV,OAAO;;IAErC;IACA,MAAMW,WAAW,GAAG,MAAM,IAAI,CAACC,eAAe,CAACF,SAAS,CAAC;;IAEzD;IACA,MAAMG,QAAQ,GAAGF,WAAW,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAC9CA,MAAM,CAACC,QAAQ,GAAGF,GAAG,CAACE,QAAQ,GAAGD,MAAM,GAAGD,GAC5C,CAAC;;IAED;IACA,OAAO,MAAMpC,GAAG,CAACuC,GAAG,CAAC,UAAUT,MAAM,EAAE,EAAE;MACvCU,WAAW,EAAEN,QAAQ,CAACpB,EAAE;MACxB2B,WAAW,EAAE,IAAIzB,IAAI,CAAC,CAAC,CAAC0B,WAAW,CAAC,CAAC;MACrCC,iBAAiB,EAAE;IACrB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACE,MAAMlB,oBAAoBA,CAACJ,OAAO,EAAE;IAClC,MAAM;MAAES,MAAM;MAAEc,aAAa;MAAEb;IAAU,CAAC,GAAGV,OAAO;;IAEpD;IACA,MAAMwB,QAAQ,GAAG,MAAM,IAAI,CAACC,kBAAkB,CAACf,SAAS,CAAC;IACzD,MAAMgB,UAAU,GAAGF,QAAQ,CAACG,aAAa,CAACJ,aAAa,CAAC;IAExD,IAAI,CAACG,UAAU,EAAE,OAAO,KAAK;;IAE7B;IACA,MAAME,aAAa,GAAG,MAAM,IAAI,CAACC,yBAAyB,CAACpB,MAAM,EAAEiB,UAAU,CAAC;IAC9E,IAAI,CAACE,aAAa,EAAE,OAAO,KAAK;;IAEhC;IACA,OAAO,MAAMjD,GAAG,CAACuC,GAAG,CAAC,UAAUT,MAAM,SAAS,EAAE;MAC9CqB,MAAM,EAAEJ,UAAU;MAClBK,iBAAiB,EAAE,iBAAiB;MACpCC,eAAe,EAAE,IAAIrC,IAAI,CAAC,CAAC,CAAC0B,WAAW,CAAC;IAC1C,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACE,MAAMhB,oBAAoBA,CAACL,OAAO,EAAE;IAClC,MAAM;MAAES,MAAM;MAAEwB,OAAO;MAAEC;IAAW,CAAC,GAAGlC,OAAO;IAC/C,MAAMmC,GAAG,GAAG,IAAIxC,IAAI,CAAC,CAAC;IACtB,MAAMyC,GAAG,GAAG,IAAIzC,IAAI,CAACsC,OAAO,CAAC;IAC7B,MAAMI,aAAa,GAAG,CAACD,GAAG,GAAGD,GAAG,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;;IAEpD;IACA,IAAIG,YAAY,GAAG,QAAQ;IAC3B,IAAID,aAAa,IAAI,CAAC,EAAEC,YAAY,GAAG,QAAQ,MAC1C,IAAID,aAAa,IAAI,EAAE,EAAEC,YAAY,GAAG,WAAW;IAExD,OAAO,MAAM3D,GAAG,CAAC4D,IAAI,CAAC,gBAAgB,EAAE;MACtCrC,IAAI,EAAE,mBAAmB;MACzBsC,YAAY,EAAEN,UAAU;MACxBO,OAAO,EAAEhC,MAAM;MACfiC,QAAQ,EAAEJ,YAAY;MACtBK,OAAO,EAAE,IAAI,CAACC,uBAAuB,CAACP,aAAa,EAAEC,YAAY;IACnE,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACE,MAAMhC,cAAcA,CAACN,OAAO,EAAE;IAC5B,MAAM;MAAES,MAAM;MAAEC,SAAS;MAAEmC;IAAS,CAAC,GAAG7C,OAAO;IAE/C,MAAM8C,QAAQ,GAAG;MACfC,UAAU,EAAErC,SAAS;MACrB+B,OAAO,EAAEhC,MAAM;MACfuC,KAAK,EAAE,IAAI,CAACC,iBAAiB,CAACJ,QAAQ,CAAC;MACvCK,WAAW,EAAE,IAAI,CAACC,uBAAuB,CAACnD,OAAO,CAAC;MAClDoD,WAAW,EAAE,IAAI,CAACC,wBAAwB,CAACrD,OAAO,CAAC;MACnDsD,MAAM,EAAE,IAAI,CAACC,mBAAmB,CAACvD,OAAO,CAAC;MACzC8B,MAAM,EAAE,MAAM;MACd0B,UAAU,EAAE,QAAQ;MACpBC,YAAY,EAAE;IAChB,CAAC;IAED,OAAO,MAAM9E,GAAG,CAAC4D,IAAI,CAAC,QAAQ,EAAEO,QAAQ,CAAC;EAC3C;;EAEA;AACF;AACA;;EAEE;EACAY,sBAAsBA,CAACC,QAAQ,EAAE;IAC/B,MAAMC,gBAAgB,GAAG;MACvBnE,EAAE,EAAEkE,QAAQ,CAAClE,EAAE;MACfoE,IAAI,EAAEF,QAAQ,CAACE,IAAI;MACnBX,WAAW,EAAES,QAAQ,CAACT,WAAW;MACjChD,IAAI,EAAEyD,QAAQ,CAACzD,IAAI;MAAE;MACrB4D,MAAM,EAAEH,QAAQ,CAACG,MAAM;MACvBhF,eAAe,EAAE6E,QAAQ,CAAC7E,eAAe,IAAI,EAAE;MAC/CiF,YAAY,EAAEJ,QAAQ,CAACI,YAAY,IAAI,EAAE;MACzCrE,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IAED,IAAI,CAACX,iBAAiB,CAACQ,GAAG,CAACmE,QAAQ,CAAClE,EAAE,EAAEmE,gBAAgB,CAAC;IACzD,OAAOA,gBAAgB;EACzB;;EAEA;EACA,MAAMI,qBAAqBA,CAACtD,SAAS,EAAEuD,UAAU,EAAE;IACjD,MAAMN,QAAQ,GAAG,IAAI,CAAC3E,iBAAiB,CAACiB,GAAG,CAACgE,UAAU,CAAC;IACvD,IAAI,CAACN,QAAQ,EAAE,MAAM,IAAIO,KAAK,CAAC,UAAU,CAAC;IAE1C,IAAI;MACF;MACA,MAAM,IAAI,CAACC,uBAAuB,CAACzD,SAAS,EAAEiD,QAAQ,CAACG,MAAM,CAAC;;MAE9D;MACA,MAAM,IAAI,CAACM,oBAAoB,CAAC1D,SAAS,EAAEiD,QAAQ,CAAC7E,eAAe,CAAC;;MAEpE;MACA,MAAM,IAAI,CAACuF,iBAAiB,CAAC3D,SAAS,EAAEiD,QAAQ,CAACI,YAAY,CAAC;MAE9D,OAAO;QAAEO,OAAO,EAAE,IAAI;QAAE3B,OAAO,EAAE;MAAY,CAAC;IAChD,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,OAAO;QAAE+D,OAAO,EAAE,KAAK;QAAE/D,KAAK,EAAEA,KAAK,CAACoC;MAAQ,CAAC;IACjD;EACF;;EAEA;AACF;AACA;;EAEE;EACA,MAAM4B,yBAAyBA,CAAC7D,SAAS,EAAE8D,SAAS,GAAG,EAAE,EAAE;IACzD,MAAMC,OAAO,GAAG,IAAI9E,IAAI,CAAC,CAAC;IAC1B,MAAM+E,SAAS,GAAG,IAAI/E,IAAI,CAAC8E,OAAO,CAACE,OAAO,CAAC,CAAC,GAAGH,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAE/E,MAAM,CAACI,KAAK,EAAEC,QAAQ,EAAEC,KAAK,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACjDrG,GAAG,CAACsB,GAAG,CAAC,qBAAqBS,SAAS,eAAegE,SAAS,CAACrD,WAAW,CAAC,CAAC,aAAaoD,OAAO,CAACpD,WAAW,CAAC,CAAC,EAAE,CAAC,EACjH1C,GAAG,CAACsB,GAAG,CAAC,wBAAwBS,SAAS,eAAegE,SAAS,CAACrD,WAAW,CAAC,CAAC,aAAaoD,OAAO,CAACpD,WAAW,CAAC,CAAC,EAAE,CAAC,EACpH1C,GAAG,CAACsB,GAAG,CAAC,qBAAqBS,SAAS,eAAegE,SAAS,CAACrD,WAAW,CAAC,CAAC,aAAaoD,OAAO,CAACpD,WAAW,CAAC,CAAC,EAAE,CAAC,CAClH,CAAC;IAEF,OAAO;MACL4D,WAAW,EAAE,IAAI,CAACC,oBAAoB,CAACN,KAAK,CAACO,IAAI,CAAC;MAClDC,cAAc,EAAE,IAAI,CAACC,uBAAuB,CAACR,QAAQ,CAACM,IAAI,CAAC;MAC3DG,WAAW,EAAE,IAAI,CAACC,oBAAoB,CAACT,KAAK,CAACK,IAAI,CAAC;MAClDK,iBAAiB,EAAE,IAAI,CAACC,0BAA0B,CAACb,KAAK,CAACO,IAAI,EAAEN,QAAQ,CAACM,IAAI,EAAEL,KAAK,CAACK,IAAI,CAAC;MACzFO,WAAW,EAAE,IAAI,CAACC,mBAAmB,CAACf,KAAK,CAACO,IAAI,CAAC;MACjDS,WAAW,EAAE,IAAI,CAACC,+BAA+B,CAACjB,KAAK,CAACO,IAAI,EAAEN,QAAQ,CAACM,IAAI,EAAEL,KAAK,CAACK,IAAI;IACzF,CAAC;EACH;;EAEA;EACAU,+BAA+BA,CAACjB,KAAK,EAAEC,QAAQ,EAAEC,KAAK,EAAE;IACtD,MAAMc,WAAW,GAAG,EAAE;;IAEtB;IACA,MAAME,YAAY,GAAGlB,KAAK,CAACmB,MAAM,CAACC,IAAI,IAAI,IAAIrG,IAAI,CAACqG,IAAI,CAACC,QAAQ,CAAC,GAAG,IAAItG,IAAI,CAAC,CAAC,IAAIqG,IAAI,CAAClE,MAAM,KAAK,WAAW,CAAC;IAC9G,IAAIgE,YAAY,CAACI,MAAM,GAAGtB,KAAK,CAACsB,MAAM,GAAG,GAAG,EAAE;MAC5CN,WAAW,CAACO,IAAI,CAAC;QACfjG,IAAI,EAAE,qBAAqB;QAC3B8C,KAAK,EAAE,UAAU;QACjBE,WAAW,EAAE,yBAAyB;QACtCI,MAAM,EAAE,MAAM;QACd8C,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;;IAEA;IACA,MAAMC,sBAAsB,GAAG,IAAI,CAACC,qBAAqB,CAAC1B,KAAK,CAAC;IAChE,IAAIyB,sBAAsB,CAACE,SAAS,GAAG,GAAG,EAAE;MAC1CX,WAAW,CAACO,IAAI,CAAC;QACfjG,IAAI,EAAE,kBAAkB;QACxB8C,KAAK,EAAE,UAAU;QACjBE,WAAW,EAAE,sBAAsB;QACnCI,MAAM,EAAE,MAAM;QACd8C,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;;IAEA;IACA,MAAMI,iBAAiB,GAAG,IAAI,CAACC,0BAA0B,CAAC5B,QAAQ,CAAC;IACnE,IAAI2B,iBAAiB,GAAG,GAAG,EAAE;MAC3BZ,WAAW,CAACO,IAAI,CAAC;QACfjG,IAAI,EAAE,sBAAsB;QAC5B8C,KAAK,EAAE,QAAQ;QACfE,WAAW,EAAE,uBAAuB;QACpCI,MAAM,EAAE,QAAQ;QAChB8C,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IAEA,OAAOR,WAAW;EACpB;;EAEA;AACF;AACA;;EAEEhD,uBAAuBA,CAACP,aAAa,EAAEnC,IAAI,EAAE;IAC3C,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACrB,OAAO,iBAAiBwG,IAAI,CAACC,KAAK,CAACtE,aAAa,CAAC,cAAc;IACjE,CAAC,MAAM,IAAInC,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,iBAAiBwG,IAAI,CAACC,KAAK,CAACtE,aAAa,CAAC,cAAc;IACjE;IACA,OAAO,yBAAyB;EAClC;EAEA6C,oBAAoBA,CAACN,KAAK,EAAE;IAC1B,MAAMgC,SAAS,GAAGhC,KAAK,CAACmB,MAAM,CAACc,CAAC,IAAIA,CAAC,CAAC/E,MAAM,KAAK,WAAW,CAAC,CAACoE,MAAM;IACpE,MAAMY,MAAM,GAAGlC,KAAK,CAACmB,MAAM,CAACc,CAAC,IAAIA,CAAC,CAAC/E,MAAM,KAAK,WAAW,IAAI,IAAInC,IAAI,CAACkH,CAAC,CAACE,YAAY,CAAC,IAAI,IAAIpH,IAAI,CAACkH,CAAC,CAACZ,QAAQ,CAAC,CAAC,CAACC,MAAM;IAErH,OAAO;MACLc,cAAc,EAAEpC,KAAK,CAACsB,MAAM,GAAG,CAAC,GAAIU,SAAS,GAAGhC,KAAK,CAACsB,MAAM,GAAI,GAAG,GAAG,CAAC;MACvEe,UAAU,EAAEL,SAAS,GAAG,CAAC,GAAIE,MAAM,GAAGF,SAAS,GAAI,GAAG,GAAG,CAAC;MAC1DM,iBAAiB,EAAE,IAAI,CAACC,0BAA0B,CAACvC,KAAK,CAAC;MACzDwC,UAAU,EAAExC,KAAK,CAACsB;IACpB,CAAC;EACH;EAEAiB,0BAA0BA,CAACvC,KAAK,EAAE;IAChC,MAAMyC,cAAc,GAAGzC,KAAK,CAACmB,MAAM,CAACc,CAAC,IAAIA,CAAC,CAAC/E,MAAM,KAAK,WAAW,IAAI+E,CAAC,CAACS,UAAU,IAAIT,CAAC,CAACE,YAAY,CAAC;IACpG,IAAIM,cAAc,CAACnB,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAEzC,MAAMqB,SAAS,GAAGF,cAAc,CAACvG,MAAM,CAAC,CAAC0G,GAAG,EAAExB,IAAI,KAAK;MACrD,MAAMyB,OAAO,GAAG,IAAI9H,IAAI,CAACqG,IAAI,CAACsB,UAAU,CAAC;MACzC,MAAMV,SAAS,GAAG,IAAIjH,IAAI,CAACqG,IAAI,CAACe,YAAY,CAAC;MAC7C,OAAOS,GAAG,IAAIZ,SAAS,GAAGa,OAAO,CAAC;IACpC,CAAC,EAAE,CAAC,CAAC;IAEL,OAAOF,SAAS,GAAGF,cAAc,CAACnB,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAC;EACnE;EAEA,MAAMtF,eAAeA,CAACF,SAAS,EAAE;IAC/B;IACA,OAAO,CACL;MAAEjB,EAAE,EAAE,CAAC;MAAEoE,IAAI,EAAE,IAAI;MAAE5C,QAAQ,EAAE;IAAI,CAAC,EACpC;MAAExB,EAAE,EAAE,CAAC;MAAEoE,IAAI,EAAE,IAAI;MAAE5C,QAAQ,EAAE;IAAI,CAAC,EACpC;MAAExB,EAAE,EAAE,CAAC;MAAEoE,IAAI,EAAE,IAAI;MAAE5C,QAAQ,EAAE;IAAI,CAAC,CACrC;EACH;AACF;AAEA,eAAe,IAAIrC,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}