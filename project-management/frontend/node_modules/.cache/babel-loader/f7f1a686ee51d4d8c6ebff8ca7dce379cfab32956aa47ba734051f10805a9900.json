{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, toDisplayString as _toDisplayString, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"google-content\"\n};\nconst _hoisted_2 = {\n  class: \"medical-page-header mb-6\"\n};\nconst _hoisted_3 = {\n  class: \"d-flex align-center justify-space-between\"\n};\nconst _hoisted_4 = {\n  class: \"d-flex align-center\"\n};\nconst _hoisted_5 = {\n  class: \"text-subtitle-1 text-medium-emphasis\"\n};\nconst _hoisted_6 = {\n  class: \"d-flex align-center ga-3\"\n};\nconst _hoisted_7 = {\n  class: \"medical-card-header pa-4\"\n};\nconst _hoisted_8 = {\n  class: \"text-center\"\n};\nconst _hoisted_9 = {\n  class: \"text-h5 mb-1 text-white\"\n};\nconst _hoisted_10 = {\n  class: \"text-body-1 text-white opacity-90\"\n};\nconst _hoisted_11 = {\n  class: \"text-center\"\n};\nconst _hoisted_12 = {\n  class: \"text-h6 medical-gradient-text\"\n};\nconst _hoisted_13 = {\n  class: \"text-h6 medical-gradient-text\"\n};\nconst _hoisted_14 = {\n  class: \"d-flex align-center justify-space-between\"\n};\nconst _hoisted_15 = {\n  class: \"text-center\"\n};\nconst _hoisted_16 = {\n  class: \"text-h4 font-weight-bold\"\n};\nconst _hoisted_17 = {\n  class: \"text-center\"\n};\nconst _hoisted_18 = {\n  class: \"text-h4 font-weight-bold\"\n};\nconst _hoisted_19 = {\n  class: \"text-center\"\n};\nconst _hoisted_20 = {\n  class: \"text-h4 font-weight-bold\"\n};\nconst _hoisted_21 = {\n  class: \"text-center\"\n};\nconst _hoisted_22 = {\n  class: \"text-h4 font-weight-bold\"\n};\nconst _hoisted_23 = {\n  class: \"mb-4\"\n};\nconst _hoisted_24 = {\n  class: \"d-flex align-center justify-space-between mb-2\"\n};\nconst _hoisted_25 = {\n  class: \"text-body-1 font-weight-bold\"\n};\nconst _hoisted_26 = {\n  class: \"mb-4\"\n};\nconst _hoisted_27 = {\n  class: \"d-flex align-center justify-space-between mb-2\"\n};\nconst _hoisted_28 = {\n  class: \"text-body-1 font-weight-bold\"\n};\nconst _hoisted_29 = {\n  class: \"mb-4\"\n};\nconst _hoisted_30 = {\n  class: \"d-flex align-center justify-space-between mb-2\"\n};\nconst _hoisted_31 = {\n  class: \"text-body-1 font-weight-bold\"\n};\nconst _hoisted_32 = {\n  key: 0\n};\nconst _hoisted_33 = {\n  key: 0,\n  class: \"text-center mt-4\"\n};\nconst _hoisted_34 = {\n  class: \"mb-4\"\n};\nconst _hoisted_35 = {\n  class: \"d-flex justify-space-between mb-2\"\n};\nconst _hoisted_36 = {\n  class: \"font-weight-bold\"\n};\nconst _hoisted_37 = {\n  class: \"mb-4\"\n};\nconst _hoisted_38 = {\n  class: \"d-flex justify-space-between mb-2\"\n};\nconst _hoisted_39 = {\n  class: \"font-weight-bold\"\n};\nconst _hoisted_40 = {\n  class: \"mb-4\"\n};\nconst _hoisted_41 = {\n  class: \"d-flex justify-space-between mb-2\"\n};\nconst _hoisted_42 = {\n  class: \"font-weight-bold\"\n};\nconst _hoisted_43 = {\n  class: \"text-h6 mb-1\"\n};\nconst _hoisted_44 = {\n  class: \"text-caption text-medium-emphasis\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_v_icon = _resolveComponent(\"v-icon\");\n  const _component_v_btn = _resolveComponent(\"v-btn\");\n  const _component_v_img = _resolveComponent(\"v-img\");\n  const _component_v_avatar = _resolveComponent(\"v-avatar\");\n  const _component_v_chip = _resolveComponent(\"v-chip\");\n  const _component_v_col = _resolveComponent(\"v-col\");\n  const _component_v_row = _resolveComponent(\"v-row\");\n  const _component_v_card_text = _resolveComponent(\"v-card-text\");\n  const _component_v_card = _resolveComponent(\"v-card\");\n  const _component_v_card_title = _resolveComponent(\"v-card-title\");\n  const _component_v_list_item_title = _resolveComponent(\"v-list-item-title\");\n  const _component_v_list_item_subtitle = _resolveComponent(\"v-list-item-subtitle\");\n  const _component_v_list_item = _resolveComponent(\"v-list-item\");\n  const _component_v_list = _resolveComponent(\"v-list\");\n  const _component_v_spacer = _resolveComponent(\"v-spacer\");\n  const _component_v_text_field = _resolveComponent(\"v-text-field\");\n  const _component_v_select = _resolveComponent(\"v-select\");\n  const _component_v_textarea = _resolveComponent(\"v-textarea\");\n  const _component_v_form = _resolveComponent(\"v-form\");\n  const _component_v_divider = _resolveComponent(\"v-divider\");\n  const _component_v_progress_linear = _resolveComponent(\"v-progress-linear\");\n  const _component_v_file_input = _resolveComponent(\"v-file-input\");\n  const _component_v_card_actions = _resolveComponent(\"v-card-actions\");\n  const _component_v_dialog = _resolveComponent(\"v-dialog\");\n  const _component_v_snackbar = _resolveComponent(\"v-snackbar\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 页面头部 - Google风格 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_v_icon, {\n    icon: \"mdi-account-circle\",\n    size: \"48\",\n    color: \"medical-primary\",\n    class: \"me-4\"\n  }), _createElementVNode(\"div\", null, [_cache[22] || (_cache[22] = _createElementVNode(\"h1\", {\n    class: \"medical-gradient-text text-h4 mb-1\"\n  }, \"个人资料\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_5, [_createVNode(_component_v_icon, {\n    icon: \"mdi-hospital-box\",\n    size: \"16\",\n    class: \"me-1\"\n  }), _cache[21] || (_cache[21] = _createTextVNode(\" 医疗项目管理系统 - 个人信息管理 \"))])])]), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_v_btn, {\n    color: $data.editMode ? 'medical-warning' : 'medical-primary',\n    \"prepend-icon\": $data.editMode ? 'mdi-close' : 'mdi-pencil',\n    class: \"medical-btn-primary\",\n    onClick: _cache[0] || (_cache[0] = $event => $data.editMode = !$data.editMode)\n  }, {\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($data.editMode ? '取消编辑' : '编辑资料'), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"color\", \"prepend-icon\"]), _createVNode(_component_v_btn, {\n    color: \"medical-info\",\n    \"prepend-icon\": \"mdi-chart-line\",\n    variant: \"outlined\",\n    onClick: _cache[1] || (_cache[1] = $event => $data.showWorkflowOptimization = true)\n  }, {\n    default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\" 工作流优化 \")])),\n    _: 1 /* STABLE */,\n    __: [23]\n  })])])]), _createVNode(_component_v_row, null, {\n    default: _withCtx(() => [_createVNode(_component_v_col, {\n      cols: \"12\",\n      md: \"4\"\n    }, {\n      default: _withCtx(() => [_createCommentVNode(\" 用户信息卡片 - 医疗主题 \"), _createVNode(_component_v_card, {\n        class: \"medical-card mb-4\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_v_avatar, {\n          size: \"120\",\n          class: \"mb-3\",\n          color: \"white\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_img, {\n            src: $data.user.avatar,\n            alt: \"用户头像\"\n          }, {\n            placeholder: _withCtx(() => [_createVNode(_component_v_icon, {\n              icon: \"mdi-account\",\n              size: \"60\",\n              color: \"medical-primary\"\n            })]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"src\"])]),\n          _: 1 /* STABLE */\n        }), _createElementVNode(\"h2\", _hoisted_9, _toDisplayString($data.user.name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_10, _toDisplayString($data.user.position), 1 /* TEXT */), _createVNode(_component_v_chip, {\n          color: \"white\",\n          \"text-color\": \"medical-primary\",\n          size: \"small\",\n          class: \"mt-2\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_icon, {\n            start: \"\",\n            icon: \"mdi-hospital-box\"\n          }), _cache[24] || (_cache[24] = _createTextVNode(\" 医疗项目专家 \"))]),\n          _: 1 /* STABLE */,\n          __: [24]\n        })])]), _createVNode(_component_v_card_text, {\n          class: \"pa-4\"\n        }, {\n          default: _withCtx(() => [$data.editMode ? (_openBlock(), _createBlock(_component_v_btn, {\n            key: 0,\n            color: \"medical-primary\",\n            variant: \"outlined\",\n            block: \"\",\n            \"prepend-icon\": \"mdi-camera\",\n            class: \"mb-3\",\n            onClick: _cache[2] || (_cache[2] = $event => $data.showUploadDialog = true)\n          }, {\n            default: _withCtx(() => _cache[25] || (_cache[25] = [_createTextVNode(\" 更换头像 \")])),\n            _: 1 /* STABLE */,\n            __: [25]\n          })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 快速统计 \"), _createElementVNode(\"div\", _hoisted_11, [_createVNode(_component_v_row, {\n            dense: \"\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_col, {\n              cols: \"6\"\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_12, _toDisplayString($data.stats.projects), 1 /* TEXT */), _cache[26] || (_cache[26] = _createElementVNode(\"div\", {\n                class: \"text-caption text-medium-emphasis\"\n              }, \"参与项目\", -1 /* HOISTED */))]),\n              _: 1 /* STABLE */,\n              __: [26]\n            }), _createVNode(_component_v_col, {\n              cols: \"6\"\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_13, _toDisplayString($options.workflowEfficiency) + \"%\", 1 /* TEXT */), _cache[27] || (_cache[27] = _createElementVNode(\"div\", {\n                class: \"text-caption text-medium-emphasis\"\n              }, \"工作效率\", -1 /* HOISTED */))]),\n              _: 1 /* STABLE */,\n              __: [27]\n            })]),\n            _: 1 /* STABLE */\n          })])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 联系方式卡片 - 医疗主题 \"), _createVNode(_component_v_card, {\n        class: \"medical-card\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_v_card_title, {\n          class: \"d-flex align-center\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_icon, {\n            icon: \"mdi-card-account-details\",\n            color: \"medical-primary\",\n            class: \"me-2\"\n          }), _cache[28] || (_cache[28] = _createTextVNode(\" 联系方式 \"))]),\n          _: 1 /* STABLE */,\n          __: [28]\n        }), _createVNode(_component_v_card_text, null, {\n          default: _withCtx(() => [_createVNode(_component_v_list, {\n            class: \"pa-0\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_list_item, {\n              class: \"px-0\"\n            }, {\n              prepend: _withCtx(() => [_createVNode(_component_v_avatar, {\n                size: \"40\",\n                color: \"medical-info\",\n                class: \"me-3\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_icon, {\n                  icon: \"mdi-email\",\n                  color: \"white\"\n                })]),\n                _: 1 /* STABLE */\n              })]),\n              default: _withCtx(() => [_createVNode(_component_v_list_item_title, {\n                class: \"text-caption text-medium-emphasis\"\n              }, {\n                default: _withCtx(() => _cache[29] || (_cache[29] = [_createTextVNode(\"邮箱地址\")])),\n                _: 1 /* STABLE */,\n                __: [29]\n              }), _createVNode(_component_v_list_item_subtitle, {\n                class: \"text-body-1 font-weight-medium\"\n              }, {\n                default: _withCtx(() => [_createTextVNode(_toDisplayString($data.user.email), 1 /* TEXT */)]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_v_list_item, {\n              class: \"px-0\"\n            }, {\n              prepend: _withCtx(() => [_createVNode(_component_v_avatar, {\n                size: \"40\",\n                color: \"medical-success\",\n                class: \"me-3\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_icon, {\n                  icon: \"mdi-phone\",\n                  color: \"white\"\n                })]),\n                _: 1 /* STABLE */\n              })]),\n              default: _withCtx(() => [_createVNode(_component_v_list_item_title, {\n                class: \"text-caption text-medium-emphasis\"\n              }, {\n                default: _withCtx(() => _cache[30] || (_cache[30] = [_createTextVNode(\"手机号码\")])),\n                _: 1 /* STABLE */,\n                __: [30]\n              }), _createVNode(_component_v_list_item_subtitle, {\n                class: \"text-body-1 font-weight-medium\"\n              }, {\n                default: _withCtx(() => [_createTextVNode(_toDisplayString($data.user.phone), 1 /* TEXT */)]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_v_list_item, {\n              class: \"px-0\"\n            }, {\n              prepend: _withCtx(() => [_createVNode(_component_v_avatar, {\n                size: \"40\",\n                color: \"medical-warning\",\n                class: \"me-3\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_icon, {\n                  icon: \"mdi-hospital-building\",\n                  color: \"white\"\n                })]),\n                _: 1 /* STABLE */\n              })]),\n              default: _withCtx(() => [_createVNode(_component_v_list_item_title, {\n                class: \"text-caption text-medium-emphasis\"\n              }, {\n                default: _withCtx(() => _cache[31] || (_cache[31] = [_createTextVNode(\"所属部门\")])),\n                _: 1 /* STABLE */,\n                __: [31]\n              }), _createVNode(_component_v_list_item_subtitle, {\n                class: \"text-body-1 font-weight-medium\"\n              }, {\n                default: _withCtx(() => [_createTextVNode(_toDisplayString($data.user.department), 1 /* TEXT */)]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_v_list_item, {\n              class: \"px-0\"\n            }, {\n              prepend: _withCtx(() => [_createVNode(_component_v_avatar, {\n                size: \"40\",\n                color: \"medical-accent\",\n                class: \"me-3\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_icon, {\n                  icon: \"mdi-map-marker\",\n                  color: \"white\"\n                })]),\n                _: 1 /* STABLE */\n              })]),\n              default: _withCtx(() => [_createVNode(_component_v_list_item_title, {\n                class: \"text-caption text-medium-emphasis\"\n              }, {\n                default: _withCtx(() => _cache[32] || (_cache[32] = [_createTextVNode(\"工作地址\")])),\n                _: 1 /* STABLE */,\n                __: [32]\n              }), _createVNode(_component_v_list_item_subtitle, {\n                class: \"text-body-1 font-weight-medium\"\n              }, {\n                default: _withCtx(() => [_createTextVNode(_toDisplayString($data.user.address), 1 /* TEXT */)]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_v_col, {\n      cols: \"12\",\n      md: \"8\"\n    }, {\n      default: _withCtx(() => [_createCommentVNode(\" 个人资料表单 - 医疗主题 \"), _createVNode(_component_v_card, {\n        class: \"medical-card mb-4\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_v_card_title, {\n          class: \"d-flex align-center\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_icon, {\n            icon: \"mdi-account-edit\",\n            color: \"medical-primary\",\n            class: \"me-2\"\n          }), _cache[33] || (_cache[33] = _createTextVNode(\" 个人资料 \")), _createVNode(_component_v_spacer), _createVNode(_component_v_chip, {\n            color: $data.editMode ? 'medical-warning' : 'medical-success',\n            size: \"small\",\n            variant: \"flat\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_icon, {\n              icon: $data.editMode ? 'mdi-pencil' : 'mdi-lock',\n              start: \"\",\n              size: \"16\"\n            }, null, 8 /* PROPS */, [\"icon\"]), _createTextVNode(\" \" + _toDisplayString($data.editMode ? '编辑模式' : '查看模式'), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"color\"])]),\n          _: 1 /* STABLE */,\n          __: [33]\n        }), _createVNode(_component_v_card_text, null, {\n          default: _withCtx(() => [_createVNode(_component_v_form, {\n            ref: \"form\",\n            modelValue: $data.valid,\n            \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $data.valid = $event)\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_row, null, {\n              default: _withCtx(() => [_createVNode(_component_v_col, {\n                cols: \"12\",\n                md: \"6\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_text_field, {\n                  modelValue: $data.user.name,\n                  \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $data.user.name = $event),\n                  label: \"姓名\",\n                  rules: $data.nameRules,\n                  variant: \"outlined\",\n                  readonly: !$data.editMode,\n                  color: $data.editMode ? 'medical-primary' : 'grey',\n                  \"prepend-inner-icon\": \"mdi-account\",\n                  required: \"\"\n                }, null, 8 /* PROPS */, [\"modelValue\", \"rules\", \"readonly\", \"color\"])]),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_v_col, {\n                cols: \"12\",\n                md: \"6\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_text_field, {\n                  modelValue: $data.user.email,\n                  \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $data.user.email = $event),\n                  label: \"邮箱地址\",\n                  rules: $data.emailRules,\n                  variant: \"outlined\",\n                  readonly: \"\",\n                  disabled: \"\",\n                  \"prepend-inner-icon\": \"mdi-email\"\n                }, null, 8 /* PROPS */, [\"modelValue\", \"rules\"])]),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_v_col, {\n                cols: \"12\",\n                md: \"6\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_text_field, {\n                  modelValue: $data.user.phone,\n                  \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.user.phone = $event),\n                  label: \"手机号码\",\n                  rules: $data.phoneRules,\n                  variant: \"outlined\",\n                  readonly: !$data.editMode,\n                  color: $data.editMode ? 'medical-primary' : 'grey',\n                  \"prepend-inner-icon\": \"mdi-phone\"\n                }, null, 8 /* PROPS */, [\"modelValue\", \"rules\", \"readonly\", \"color\"])]),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_v_col, {\n                cols: \"12\",\n                md: \"6\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_select, {\n                  modelValue: $data.user.department,\n                  \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.user.department = $event),\n                  items: $data.medicalDepartments,\n                  label: \"医疗部门\",\n                  variant: \"outlined\",\n                  readonly: !$data.editMode,\n                  color: $data.editMode ? 'medical-primary' : 'grey',\n                  \"prepend-inner-icon\": \"mdi-hospital-building\"\n                }, null, 8 /* PROPS */, [\"modelValue\", \"items\", \"readonly\", \"color\"])]),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_v_col, {\n                cols: \"12\",\n                md: \"6\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_text_field, {\n                  modelValue: $data.user.position,\n                  \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.user.position = $event),\n                  label: \"职位\",\n                  variant: \"outlined\",\n                  readonly: !$data.editMode,\n                  color: $data.editMode ? 'medical-primary' : 'grey',\n                  \"prepend-inner-icon\": \"mdi-badge-account\"\n                }, null, 8 /* PROPS */, [\"modelValue\", \"readonly\", \"color\"])]),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_v_col, {\n                cols: \"12\",\n                md: \"6\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_text_field, {\n                  modelValue: $data.user.joinDate,\n                  \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.user.joinDate = $event),\n                  label: \"入职日期\",\n                  variant: \"outlined\",\n                  readonly: \"\",\n                  disabled: \"\",\n                  \"prepend-inner-icon\": \"mdi-calendar\"\n                }, null, 8 /* PROPS */, [\"modelValue\"])]),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_v_col, {\n                cols: \"12\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_text_field, {\n                  modelValue: $data.user.address,\n                  \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.user.address = $event),\n                  label: \"工作地址\",\n                  variant: \"outlined\",\n                  readonly: !$data.editMode,\n                  color: $data.editMode ? 'medical-primary' : 'grey',\n                  \"prepend-inner-icon\": \"mdi-map-marker\"\n                }, null, 8 /* PROPS */, [\"modelValue\", \"readonly\", \"color\"])]),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_v_col, {\n                cols: \"12\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_textarea, {\n                  modelValue: $data.user.bio,\n                  \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $data.user.bio = $event),\n                  label: \"个人简介\",\n                  variant: \"outlined\",\n                  readonly: !$data.editMode,\n                  color: $data.editMode ? 'medical-primary' : 'grey',\n                  \"prepend-inner-icon\": \"mdi-text-account\",\n                  \"auto-grow\": \"\",\n                  rows: \"3\"\n                }, null, 8 /* PROPS */, [\"modelValue\", \"readonly\", \"color\"])]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            }), $data.editMode ? (_openBlock(), _createBlock(_component_v_row, {\n              key: 0\n            }, {\n              default: _withCtx(() => [_createVNode(_component_v_col, {\n                cols: \"12\",\n                class: \"d-flex justify-end ga-3\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_btn, {\n                  color: \"grey\",\n                  variant: \"outlined\",\n                  onClick: _cache[11] || (_cache[11] = $event => $data.editMode = false)\n                }, {\n                  default: _withCtx(() => _cache[34] || (_cache[34] = [_createTextVNode(\" 取消 \")])),\n                  _: 1 /* STABLE */,\n                  __: [34]\n                }), _createVNode(_component_v_btn, {\n                  color: \"medical-primary\",\n                  class: \"medical-btn-primary\",\n                  disabled: !$data.valid,\n                  onClick: $options.saveProfile\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_v_icon, {\n                    start: \"\",\n                    icon: \"mdi-content-save\"\n                  }), _cache[35] || (_cache[35] = _createTextVNode(\" 保存资料 \"))]),\n                  _: 1 /* STABLE */,\n                  __: [35]\n                }, 8 /* PROPS */, [\"disabled\", \"onClick\"])]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            })) : _createCommentVNode(\"v-if\", true)]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_v_card, null, {\n        default: _withCtx(() => [_createVNode(_component_v_card_title, null, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_14, [_cache[37] || (_cache[37] = _createElementVNode(\"span\", null, \"工作统计\", -1 /* HOISTED */)), _createVNode(_component_v_btn, {\n            color: \"primary\",\n            variant: \"text\",\n            size: \"small\",\n            \"prepend-icon\": \"mdi-chart-line\",\n            onClick: _cache[13] || (_cache[13] = $event => $data.showWorkflowOptimization = true)\n          }, {\n            default: _withCtx(() => _cache[36] || (_cache[36] = [_createTextVNode(\" 工作流优化 \")])),\n            _: 1 /* STABLE */,\n            __: [36]\n          })])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_v_card_text, null, {\n          default: _withCtx(() => [_createVNode(_component_v_row, null, {\n            default: _withCtx(() => [_createVNode(_component_v_col, {\n              cols: \"6\",\n              md: \"3\"\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, _toDisplayString($data.stats.projects), 1 /* TEXT */), _cache[38] || (_cache[38] = _createElementVNode(\"div\", {\n                class: \"text-caption text-medium-emphasis\"\n              }, \"参与项目\", -1 /* HOISTED */))])]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_v_col, {\n              cols: \"6\",\n              md: \"3\"\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, _toDisplayString($data.stats.tasks), 1 /* TEXT */), _cache[39] || (_cache[39] = _createElementVNode(\"div\", {\n                class: \"text-caption text-medium-emphasis\"\n              }, \"完成任务\", -1 /* HOISTED */))])]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_v_col, {\n              cols: \"6\",\n              md: \"3\"\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, _toDisplayString($data.stats.meetings), 1 /* TEXT */), _cache[40] || (_cache[40] = _createElementVNode(\"div\", {\n                class: \"text-caption text-medium-emphasis\"\n              }, \"参加会议\", -1 /* HOISTED */))])]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_v_col, {\n              cols: \"6\",\n              md: \"3\"\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, _toDisplayString($data.stats.documents), 1 /* TEXT */), _cache[41] || (_cache[41] = _createElementVNode(\"div\", {\n                class: \"text-caption text-medium-emphasis\"\n              }, \"提交文档\", -1 /* HOISTED */))])]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_v_divider, {\n            class: \"my-4\"\n          }), _createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, [_cache[42] || (_cache[42] = _createElementVNode(\"div\", {\n            class: \"text-body-1\"\n          }, \"任务完成率\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_25, _toDisplayString($data.stats.taskCompletionRate) + \"%\", 1 /* TEXT */)]), _createVNode(_component_v_progress_linear, {\n            \"model-value\": $data.stats.taskCompletionRate,\n            color: \"success\",\n            height: \"8\",\n            rounded: \"\"\n          }, null, 8 /* PROPS */, [\"model-value\"])]), _createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, [_cache[43] || (_cache[43] = _createElementVNode(\"div\", {\n            class: \"text-body-1\"\n          }, \"按时完成率\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_28, _toDisplayString($data.stats.onTimeRate) + \"%\", 1 /* TEXT */)]), _createVNode(_component_v_progress_linear, {\n            \"model-value\": $data.stats.onTimeRate,\n            color: \"info\",\n            height: \"8\",\n            rounded: \"\"\n          }, null, 8 /* PROPS */, [\"model-value\"])]), _createCommentVNode(\" 工作流效率指标 \"), _createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"div\", _hoisted_30, [_cache[44] || (_cache[44] = _createElementVNode(\"div\", {\n            class: \"text-body-1\"\n          }, \"工作流效率\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_31, _toDisplayString($options.workflowEfficiency) + \"%\", 1 /* TEXT */)]), _createVNode(_component_v_progress_linear, {\n            \"model-value\": $options.workflowEfficiency,\n            color: $options.getEfficiencyColor($options.workflowEfficiency),\n            height: \"8\",\n            rounded: \"\"\n          }, null, 8 /* PROPS */, [\"model-value\", \"color\"])]), _createCommentVNode(\" 待处理事项 \"), $data.pendingItems.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_32, [_createVNode(_component_v_divider, {\n            class: \"my-4\"\n          }), _cache[45] || (_cache[45] = _createElementVNode(\"div\", {\n            class: \"text-subtitle-2 mb-2\"\n          }, \"待处理事项\", -1 /* HOISTED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.pendingItems, item => {\n            return _openBlock(), _createBlock(_component_v_chip, {\n              key: item.id,\n              color: item.priority === 'high' ? 'error' : item.priority === 'medium' ? 'warning' : 'info',\n              size: \"small\",\n              class: \"me-2 mb-2\",\n              onClick: $event => $options.handlePendingItem(item)\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString(item.title), 1 /* TEXT */)]),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"color\", \"onClick\"]);\n          }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true)]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 上传头像对话框 \"), _createVNode(_component_v_dialog, {\n    modelValue: $data.showUploadDialog,\n    \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $data.showUploadDialog = $event),\n    \"max-width\": \"500\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_v_card, null, {\n      default: _withCtx(() => [_createVNode(_component_v_card_title, null, {\n        default: _withCtx(() => _cache[46] || (_cache[46] = [_createTextVNode(\"上传头像\")])),\n        _: 1 /* STABLE */,\n        __: [46]\n      }), _createVNode(_component_v_card_text, null, {\n        default: _withCtx(() => [_createVNode(_component_v_file_input, {\n          modelValue: $data.avatarFile,\n          \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $data.avatarFile = $event),\n          label: \"选择图片\",\n          accept: \"image/*\",\n          \"show-size\": \"\",\n          \"truncate-length\": \"15\",\n          variant: \"outlined\"\n        }, null, 8 /* PROPS */, [\"modelValue\"]), $data.avatarPreview ? (_openBlock(), _createElementBlock(\"div\", _hoisted_33, [_createVNode(_component_v_avatar, {\n          size: \"150\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_img, {\n            src: $data.avatarPreview,\n            alt: \"Avatar Preview\"\n          }, null, 8 /* PROPS */, [\"src\"])]),\n          _: 1 /* STABLE */\n        })])) : _createCommentVNode(\"v-if\", true)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_v_card_actions, null, {\n        default: _withCtx(() => [_createVNode(_component_v_spacer), _createVNode(_component_v_btn, {\n          color: \"grey-darken-1\",\n          variant: \"text\",\n          onClick: _cache[15] || (_cache[15] = $event => $data.showUploadDialog = false)\n        }, {\n          default: _withCtx(() => _cache[47] || (_cache[47] = [_createTextVNode(\" 取消 \")])),\n          _: 1 /* STABLE */,\n          __: [47]\n        }), _createVNode(_component_v_btn, {\n          color: \"primary\",\n          variant: \"text\",\n          disabled: !$data.avatarFile,\n          onClick: $options.uploadAvatar\n        }, {\n          default: _withCtx(() => _cache[48] || (_cache[48] = [_createTextVNode(\" 上传 \")])),\n          _: 1 /* STABLE */,\n          __: [48]\n        }, 8 /* PROPS */, [\"disabled\", \"onClick\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 工作流优化对话框 \"), _createVNode(_component_v_dialog, {\n    modelValue: $data.showWorkflowOptimization,\n    \"onUpdate:modelValue\": _cache[18] || (_cache[18] = $event => $data.showWorkflowOptimization = $event),\n    \"max-width\": \"1200\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_v_card, null, {\n      default: _withCtx(() => [_createVNode(_component_v_card_title, {\n        class: \"d-flex align-center\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_v_icon, {\n          icon: \"mdi-chart-line\",\n          class: \"me-2\"\n        }), _cache[49] || (_cache[49] = _createTextVNode(\" 工作流优化分析 \"))]),\n        _: 1 /* STABLE */,\n        __: [49]\n      }), _createVNode(_component_v_card_text, null, {\n        default: _withCtx(() => [_createVNode(_component_v_row, null, {\n          default: _withCtx(() => [_createCommentVNode(\" 工作流效率分析 \"), _createVNode(_component_v_col, {\n            cols: \"12\",\n            md: \"6\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_card, {\n              variant: \"outlined\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_v_card_title, {\n                class: \"text-h6\"\n              }, {\n                default: _withCtx(() => _cache[50] || (_cache[50] = [_createTextVNode(\"效率分析\")])),\n                _: 1 /* STABLE */,\n                __: [50]\n              }), _createVNode(_component_v_card_text, null, {\n                default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_34, [_createElementVNode(\"div\", _hoisted_35, [_cache[51] || (_cache[51] = _createElementVNode(\"span\", null, \"任务处理速度\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_36, _toDisplayString($data.workflowMetrics.taskProcessingSpeed) + \"个/天\", 1 /* TEXT */)]), _createVNode(_component_v_progress_linear, {\n                  \"model-value\": $data.workflowMetrics.taskProcessingSpeed * 10,\n                  color: \"primary\",\n                  height: \"6\"\n                }, null, 8 /* PROPS */, [\"model-value\"])]), _createElementVNode(\"div\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_cache[52] || (_cache[52] = _createElementVNode(\"span\", null, \"平均响应时间\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_39, _toDisplayString($data.workflowMetrics.avgResponseTime) + \"小时\", 1 /* TEXT */)]), _createVNode(_component_v_progress_linear, {\n                  \"model-value\": Math.max(0, 100 - $data.workflowMetrics.avgResponseTime * 5),\n                  color: \"info\",\n                  height: \"6\"\n                }, null, 8 /* PROPS */, [\"model-value\"])]), _createElementVNode(\"div\", _hoisted_40, [_createElementVNode(\"div\", _hoisted_41, [_cache[53] || (_cache[53] = _createElementVNode(\"span\", null, \"工作流自动化率\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_42, _toDisplayString($data.workflowMetrics.automationRate) + \"%\", 1 /* TEXT */)]), _createVNode(_component_v_progress_linear, {\n                  \"model-value\": $data.workflowMetrics.automationRate,\n                  color: \"success\",\n                  height: \"6\"\n                }, null, 8 /* PROPS */, [\"model-value\"])])]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          }), _createCommentVNode(\" 优化建议 \"), _createVNode(_component_v_col, {\n            cols: \"12\",\n            md: \"6\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_card, {\n              variant: \"outlined\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_v_card_title, {\n                class: \"text-h6\"\n              }, {\n                default: _withCtx(() => _cache[54] || (_cache[54] = [_createTextVNode(\"优化建议\")])),\n                _: 1 /* STABLE */,\n                __: [54]\n              }), _createVNode(_component_v_card_text, null, {\n                default: _withCtx(() => [_createVNode(_component_v_list, {\n                  density: \"compact\"\n                }, {\n                  default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.optimizationSuggestions, suggestion => {\n                    return _openBlock(), _createBlock(_component_v_list_item, {\n                      key: suggestion.id,\n                      \"prepend-icon\": suggestion.icon,\n                      title: suggestion.title,\n                      subtitle: suggestion.description,\n                      onClick: $event => $options.applySuggestion(suggestion)\n                    }, {\n                      append: _withCtx(() => [_createVNode(_component_v_chip, {\n                        color: suggestion.impact === 'high' ? 'success' : suggestion.impact === 'medium' ? 'warning' : 'info',\n                        size: \"small\"\n                      }, {\n                        default: _withCtx(() => [_createTextVNode(_toDisplayString(suggestion.impact === 'high' ? '高效果' : suggestion.impact === 'medium' ? '中效果' : '低效果'), 1 /* TEXT */)]),\n                        _: 2 /* DYNAMIC */\n                      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"color\"])]),\n                      _: 2 /* DYNAMIC */\n                    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"prepend-icon\", \"title\", \"subtitle\", \"onClick\"]);\n                  }), 128 /* KEYED_FRAGMENT */))]),\n                  _: 1 /* STABLE */\n                })]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          }), _createCommentVNode(\" 工作流模板 \"), _createVNode(_component_v_col, {\n            cols: \"12\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_card, {\n              variant: \"outlined\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_v_card_title, {\n                class: \"text-h6\"\n              }, {\n                default: _withCtx(() => _cache[55] || (_cache[55] = [_createTextVNode(\"工作流模板\")])),\n                _: 1 /* STABLE */,\n                __: [55]\n              }), _createVNode(_component_v_card_text, null, {\n                default: _withCtx(() => [_createVNode(_component_v_row, null, {\n                  default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.workflowTemplates, template => {\n                    return _openBlock(), _createBlock(_component_v_col, {\n                      key: template.id,\n                      cols: \"12\",\n                      md: \"4\"\n                    }, {\n                      default: _withCtx(() => [_createVNode(_component_v_card, {\n                        variant: \"outlined\",\n                        class: \"workflow-template-card\",\n                        onClick: $event => $options.applyWorkflowTemplate(template)\n                      }, {\n                        default: _withCtx(() => [_createVNode(_component_v_card_text, {\n                          class: \"text-center\"\n                        }, {\n                          default: _withCtx(() => [_createVNode(_component_v_icon, {\n                            icon: template.icon,\n                            size: \"48\",\n                            color: template.color,\n                            class: \"mb-2\"\n                          }, null, 8 /* PROPS */, [\"icon\", \"color\"]), _createElementVNode(\"div\", _hoisted_43, _toDisplayString(template.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_44, _toDisplayString(template.description), 1 /* TEXT */)]),\n                          _: 2 /* DYNAMIC */\n                        }, 1024 /* DYNAMIC_SLOTS */)]),\n                        _: 2 /* DYNAMIC */\n                      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n                      _: 2 /* DYNAMIC */\n                    }, 1024 /* DYNAMIC_SLOTS */);\n                  }), 128 /* KEYED_FRAGMENT */))]),\n                  _: 1 /* STABLE */\n                })]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_v_card_actions, null, {\n        default: _withCtx(() => [_createVNode(_component_v_spacer), _createVNode(_component_v_btn, {\n          color: \"grey-darken-1\",\n          variant: \"text\",\n          onClick: _cache[17] || (_cache[17] = $event => $data.showWorkflowOptimization = false)\n        }, {\n          default: _withCtx(() => _cache[56] || (_cache[56] = [_createTextVNode(\" 关闭 \")])),\n          _: 1 /* STABLE */,\n          __: [56]\n        }), _createVNode(_component_v_btn, {\n          color: \"primary\",\n          variant: \"text\",\n          onClick: $options.exportWorkflowReport\n        }, {\n          default: _withCtx(() => _cache[57] || (_cache[57] = [_createTextVNode(\" 导出报告 \")])),\n          _: 1 /* STABLE */,\n          __: [57]\n        }, 8 /* PROPS */, [\"onClick\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 操作成功提示 \"), _createVNode(_component_v_snackbar, {\n    modelValue: $data.showSnackbar,\n    \"onUpdate:modelValue\": _cache[20] || (_cache[20] = $event => $data.showSnackbar = $event),\n    color: \"success\"\n  }, {\n    actions: _withCtx(() => [_createVNode(_component_v_btn, {\n      variant: \"text\",\n      onClick: _cache[19] || (_cache[19] = $event => $data.showSnackbar = false)\n    }, {\n      default: _withCtx(() => _cache[58] || (_cache[58] = [_createTextVNode(\" 关闭 \")])),\n      _: 1 /* STABLE */,\n      __: [58]\n    })]),\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($data.snackbarText) + \" \", 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_v_icon", "icon", "size", "color", "_hoisted_5", "_createTextVNode", "_hoisted_6", "_component_v_btn", "$data", "editMode", "onClick", "_cache", "$event", "default", "_withCtx", "_toDisplayString", "_", "variant", "showWorkflowOptimization", "__", "_component_v_row", "_component_v_col", "cols", "md", "_component_v_card", "_hoisted_7", "_hoisted_8", "_component_v_avatar", "_component_v_img", "src", "user", "avatar", "alt", "placeholder", "_hoisted_9", "name", "_hoisted_10", "position", "_component_v_chip", "start", "_component_v_card_text", "_createBlock", "block", "showUploadDialog", "_hoisted_11", "dense", "_hoisted_12", "stats", "projects", "_hoisted_13", "$options", "workflowEfficiency", "_component_v_card_title", "_component_v_list", "_component_v_list_item", "prepend", "_component_v_list_item_title", "_component_v_list_item_subtitle", "email", "phone", "department", "address", "_component_v_spacer", "_component_v_form", "ref", "modelValue", "valid", "_component_v_text_field", "label", "rules", "nameRules", "readonly", "required", "emailRules", "disabled", "phoneRules", "_component_v_select", "items", "medicalDepartments", "joinDate", "_component_v_textarea", "bio", "rows", "saveProfile", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "tasks", "_hoisted_19", "_hoisted_20", "meetings", "_hoisted_21", "_hoisted_22", "documents", "_component_v_divider", "_hoisted_23", "_hoisted_24", "_hoisted_25", "taskCompletionRate", "_component_v_progress_linear", "height", "rounded", "_hoisted_26", "_hoisted_27", "_hoisted_28", "onTimeRate", "_hoisted_29", "_hoisted_30", "_hoisted_31", "getEfficiencyColor", "pendingItems", "length", "_hoisted_32", "_Fragment", "_renderList", "item", "id", "priority", "handlePendingItem", "title", "_component_v_dialog", "_component_v_file_input", "avatar<PERSON>ile", "accept", "avatarPreview", "_hoisted_33", "_component_v_card_actions", "uploadAvatar", "_hoisted_34", "_hoisted_35", "_hoisted_36", "workflowMetrics", "taskProcessingSpeed", "_hoisted_37", "_hoisted_38", "_hoisted_39", "avgResponseTime", "Math", "max", "_hoisted_40", "_hoisted_41", "_hoisted_42", "automationRate", "density", "optimizationSuggestions", "suggestion", "subtitle", "description", "applySuggestion", "append", "impact", "workflowTemplates", "template", "applyWorkflowTemplate", "_hoisted_43", "_hoisted_44", "exportWorkflowReport", "_component_v_snackbar", "showSnackbar", "actions", "snackbarText"], "sources": ["/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/技术测试/项目实施进度管理/project-management/frontend/src/views/Profile.vue"], "sourcesContent": ["<template>\n  <div class=\"google-content\">\n    <!-- 页面头部 - Google风格 -->\n    <div class=\"medical-page-header mb-6\">\n      <div class=\"d-flex align-center justify-space-between\">\n        <div class=\"d-flex align-center\">\n          <v-icon\n            icon=\"mdi-account-circle\"\n            size=\"48\"\n            color=\"medical-primary\"\n            class=\"me-4\"\n          ></v-icon>\n          <div>\n            <h1 class=\"medical-gradient-text text-h4 mb-1\">个人资料</h1>\n            <p class=\"text-subtitle-1 text-medium-emphasis\">\n              <v-icon icon=\"mdi-hospital-box\" size=\"16\" class=\"me-1\"></v-icon>\n              医疗项目管理系统 - 个人信息管理\n            </p>\n          </div>\n        </div>\n        <div class=\"d-flex align-center ga-3\">\n          <v-btn\n            :color=\"editMode ? 'medical-warning' : 'medical-primary'\"\n            :prepend-icon=\"editMode ? 'mdi-close' : 'mdi-pencil'\"\n            class=\"medical-btn-primary\"\n            @click=\"editMode = !editMode\"\n          >\n            {{ editMode ? '取消编辑' : '编辑资料' }}\n          </v-btn>\n          <v-btn\n            color=\"medical-info\"\n            prepend-icon=\"mdi-chart-line\"\n            variant=\"outlined\"\n            @click=\"showWorkflowOptimization = true\"\n          >\n            工作流优化\n          </v-btn>\n        </div>\n      </div>\n    </div>\n\n    <v-row>\n      <v-col cols=\"12\" md=\"4\">\n        <!-- 用户信息卡片 - 医疗主题 -->\n        <v-card class=\"medical-card mb-4\">\n          <div class=\"medical-card-header pa-4\">\n            <div class=\"text-center\">\n              <v-avatar size=\"120\" class=\"mb-3\" color=\"white\">\n                <v-img :src=\"user.avatar\" alt=\"用户头像\">\n                  <template v-slot:placeholder>\n                    <v-icon icon=\"mdi-account\" size=\"60\" color=\"medical-primary\"></v-icon>\n                  </template>\n                </v-img>\n              </v-avatar>\n              <h2 class=\"text-h5 mb-1 text-white\">{{ user.name }}</h2>\n              <p class=\"text-body-1 text-white opacity-90\">{{ user.position }}</p>\n              <v-chip\n                color=\"white\"\n                text-color=\"medical-primary\"\n                size=\"small\"\n                class=\"mt-2\"\n              >\n                <v-icon start icon=\"mdi-hospital-box\"></v-icon>\n                医疗项目专家\n              </v-chip>\n            </div>\n          </div>\n\n          <v-card-text class=\"pa-4\">\n            <v-btn\n              v-if=\"editMode\"\n              color=\"medical-primary\"\n              variant=\"outlined\"\n              block\n              prepend-icon=\"mdi-camera\"\n              class=\"mb-3\"\n              @click=\"showUploadDialog = true\"\n            >\n              更换头像\n            </v-btn>\n\n            <!-- 快速统计 -->\n            <div class=\"text-center\">\n              <v-row dense>\n                <v-col cols=\"6\">\n                  <div class=\"text-h6 medical-gradient-text\">{{ stats.projects }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">参与项目</div>\n                </v-col>\n                <v-col cols=\"6\">\n                  <div class=\"text-h6 medical-gradient-text\">{{ workflowEfficiency }}%</div>\n                  <div class=\"text-caption text-medium-emphasis\">工作效率</div>\n                </v-col>\n              </v-row>\n            </div>\n          </v-card-text>\n        </v-card>\n\n        <!-- 联系方式卡片 - 医疗主题 -->\n        <v-card class=\"medical-card\">\n          <v-card-title class=\"d-flex align-center\">\n            <v-icon icon=\"mdi-card-account-details\" color=\"medical-primary\" class=\"me-2\"></v-icon>\n            联系方式\n          </v-card-title>\n          <v-card-text>\n            <v-list class=\"pa-0\">\n              <v-list-item class=\"px-0\">\n                <template v-slot:prepend>\n                  <v-avatar size=\"40\" color=\"medical-info\" class=\"me-3\">\n                    <v-icon icon=\"mdi-email\" color=\"white\"></v-icon>\n                  </v-avatar>\n                </template>\n                <v-list-item-title class=\"text-caption text-medium-emphasis\">邮箱地址</v-list-item-title>\n                <v-list-item-subtitle class=\"text-body-1 font-weight-medium\">{{ user.email }}</v-list-item-subtitle>\n              </v-list-item>\n\n              <v-list-item class=\"px-0\">\n                <template v-slot:prepend>\n                  <v-avatar size=\"40\" color=\"medical-success\" class=\"me-3\">\n                    <v-icon icon=\"mdi-phone\" color=\"white\"></v-icon>\n                  </v-avatar>\n                </template>\n                <v-list-item-title class=\"text-caption text-medium-emphasis\">手机号码</v-list-item-title>\n                <v-list-item-subtitle class=\"text-body-1 font-weight-medium\">{{ user.phone }}</v-list-item-subtitle>\n              </v-list-item>\n\n              <v-list-item class=\"px-0\">\n                <template v-slot:prepend>\n                  <v-avatar size=\"40\" color=\"medical-warning\" class=\"me-3\">\n                    <v-icon icon=\"mdi-hospital-building\" color=\"white\"></v-icon>\n                  </v-avatar>\n                </template>\n                <v-list-item-title class=\"text-caption text-medium-emphasis\">所属部门</v-list-item-title>\n                <v-list-item-subtitle class=\"text-body-1 font-weight-medium\">{{ user.department }}</v-list-item-subtitle>\n              </v-list-item>\n\n              <v-list-item class=\"px-0\">\n                <template v-slot:prepend>\n                  <v-avatar size=\"40\" color=\"medical-accent\" class=\"me-3\">\n                    <v-icon icon=\"mdi-map-marker\" color=\"white\"></v-icon>\n                  </v-avatar>\n                </template>\n                <v-list-item-title class=\"text-caption text-medium-emphasis\">工作地址</v-list-item-title>\n                <v-list-item-subtitle class=\"text-body-1 font-weight-medium\">{{ user.address }}</v-list-item-subtitle>\n              </v-list-item>\n            </v-list>\n          </v-card-text>\n        </v-card>\n      </v-col>\n\n      <v-col cols=\"12\" md=\"8\">\n        <!-- 个人资料表单 - 医疗主题 -->\n        <v-card class=\"medical-card mb-4\">\n          <v-card-title class=\"d-flex align-center\">\n            <v-icon icon=\"mdi-account-edit\" color=\"medical-primary\" class=\"me-2\"></v-icon>\n            个人资料\n            <v-spacer></v-spacer>\n            <v-chip\n              :color=\"editMode ? 'medical-warning' : 'medical-success'\"\n              size=\"small\"\n              variant=\"flat\"\n            >\n              <v-icon\n                :icon=\"editMode ? 'mdi-pencil' : 'mdi-lock'\"\n                start\n                size=\"16\"\n              ></v-icon>\n              {{ editMode ? '编辑模式' : '查看模式' }}\n            </v-chip>\n          </v-card-title>\n          <v-card-text>\n            <v-form ref=\"form\" v-model=\"valid\">\n              <v-row>\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.name\"\n                    label=\"姓名\"\n                    :rules=\"nameRules\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                    :color=\"editMode ? 'medical-primary' : 'grey'\"\n                    prepend-inner-icon=\"mdi-account\"\n                    required\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.email\"\n                    label=\"邮箱地址\"\n                    :rules=\"emailRules\"\n                    variant=\"outlined\"\n                    readonly\n                    disabled\n                    prepend-inner-icon=\"mdi-email\"\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.phone\"\n                    label=\"手机号码\"\n                    :rules=\"phoneRules\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                    :color=\"editMode ? 'medical-primary' : 'grey'\"\n                    prepend-inner-icon=\"mdi-phone\"\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-select\n                    v-model=\"user.department\"\n                    :items=\"medicalDepartments\"\n                    label=\"医疗部门\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                    :color=\"editMode ? 'medical-primary' : 'grey'\"\n                    prepend-inner-icon=\"mdi-hospital-building\"\n                  ></v-select>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.position\"\n                    label=\"职位\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                    :color=\"editMode ? 'medical-primary' : 'grey'\"\n                    prepend-inner-icon=\"mdi-badge-account\"\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.joinDate\"\n                    label=\"入职日期\"\n                    variant=\"outlined\"\n                    readonly\n                    disabled\n                    prepend-inner-icon=\"mdi-calendar\"\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\">\n                  <v-text-field\n                    v-model=\"user.address\"\n                    label=\"工作地址\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                    :color=\"editMode ? 'medical-primary' : 'grey'\"\n                    prepend-inner-icon=\"mdi-map-marker\"\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\">\n                  <v-textarea\n                    v-model=\"user.bio\"\n                    label=\"个人简介\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                    :color=\"editMode ? 'medical-primary' : 'grey'\"\n                    prepend-inner-icon=\"mdi-text-account\"\n                    auto-grow\n                    rows=\"3\"\n                  ></v-textarea>\n                </v-col>\n              </v-row>\n\n              <v-row v-if=\"editMode\">\n                <v-col cols=\"12\" class=\"d-flex justify-end ga-3\">\n                  <v-btn\n                    color=\"grey\"\n                    variant=\"outlined\"\n                    @click=\"editMode = false\"\n                  >\n                    取消\n                  </v-btn>\n                  <v-btn\n                    color=\"medical-primary\"\n                    class=\"medical-btn-primary\"\n                    :disabled=\"!valid\"\n                    @click=\"saveProfile\"\n                  >\n                    <v-icon start icon=\"mdi-content-save\"></v-icon>\n                    保存资料\n                  </v-btn>\n                </v-col>\n              </v-row>\n            </v-form>\n          </v-card-text>\n        </v-card>\n\n        <v-card>\n          <v-card-title>\n            <div class=\"d-flex align-center justify-space-between\">\n              <span>工作统计</span>\n              <v-btn\n                color=\"primary\"\n                variant=\"text\"\n                size=\"small\"\n                prepend-icon=\"mdi-chart-line\"\n                @click=\"showWorkflowOptimization = true\"\n              >\n                工作流优化\n              </v-btn>\n            </div>\n          </v-card-title>\n          <v-card-text>\n            <v-row>\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.projects }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">参与项目</div>\n                </div>\n              </v-col>\n\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.tasks }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">完成任务</div>\n                </div>\n              </v-col>\n\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.meetings }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">参加会议</div>\n                </div>\n              </v-col>\n\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.documents }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">提交文档</div>\n                </div>\n              </v-col>\n            </v-row>\n\n            <v-divider class=\"my-4\"></v-divider>\n\n            <div class=\"mb-4\">\n              <div class=\"d-flex align-center justify-space-between mb-2\">\n                <div class=\"text-body-1\">任务完成率</div>\n                <div class=\"text-body-1 font-weight-bold\">{{ stats.taskCompletionRate }}%</div>\n              </div>\n              <v-progress-linear\n                :model-value=\"stats.taskCompletionRate\"\n                color=\"success\"\n                height=\"8\"\n                rounded\n              ></v-progress-linear>\n            </div>\n\n            <div class=\"mb-4\">\n              <div class=\"d-flex align-center justify-space-between mb-2\">\n                <div class=\"text-body-1\">按时完成率</div>\n                <div class=\"text-body-1 font-weight-bold\">{{ stats.onTimeRate }}%</div>\n              </div>\n              <v-progress-linear\n                :model-value=\"stats.onTimeRate\"\n                color=\"info\"\n                height=\"8\"\n                rounded\n              ></v-progress-linear>\n            </div>\n\n            <!-- 工作流效率指标 -->\n            <div class=\"mb-4\">\n              <div class=\"d-flex align-center justify-space-between mb-2\">\n                <div class=\"text-body-1\">工作流效率</div>\n                <div class=\"text-body-1 font-weight-bold\">{{ workflowEfficiency }}%</div>\n              </div>\n              <v-progress-linear\n                :model-value=\"workflowEfficiency\"\n                :color=\"getEfficiencyColor(workflowEfficiency)\"\n                height=\"8\"\n                rounded\n              ></v-progress-linear>\n            </div>\n\n            <!-- 待处理事项 -->\n            <div v-if=\"pendingItems.length > 0\">\n              <v-divider class=\"my-4\"></v-divider>\n              <div class=\"text-subtitle-2 mb-2\">待处理事项</div>\n              <v-chip\n                v-for=\"item in pendingItems\"\n                :key=\"item.id\"\n                :color=\"item.priority === 'high' ? 'error' : item.priority === 'medium' ? 'warning' : 'info'\"\n                size=\"small\"\n                class=\"me-2 mb-2\"\n                @click=\"handlePendingItem(item)\"\n              >\n                {{ item.title }}\n              </v-chip>\n            </div>\n          </v-card-text>\n        </v-card>\n      </v-col>\n    </v-row>\n\n    <!-- 上传头像对话框 -->\n    <v-dialog v-model=\"showUploadDialog\" max-width=\"500\">\n      <v-card>\n        <v-card-title>上传头像</v-card-title>\n        <v-card-text>\n          <v-file-input\n            v-model=\"avatarFile\"\n            label=\"选择图片\"\n            accept=\"image/*\"\n            show-size\n            truncate-length=\"15\"\n            variant=\"outlined\"\n          ></v-file-input>\n\n          <div v-if=\"avatarPreview\" class=\"text-center mt-4\">\n            <v-avatar size=\"150\">\n              <v-img :src=\"avatarPreview\" alt=\"Avatar Preview\"></v-img>\n            </v-avatar>\n          </div>\n        </v-card-text>\n        <v-card-actions>\n          <v-spacer></v-spacer>\n          <v-btn\n            color=\"grey-darken-1\"\n            variant=\"text\"\n            @click=\"showUploadDialog = false\"\n          >\n            取消\n          </v-btn>\n          <v-btn\n            color=\"primary\"\n            variant=\"text\"\n            :disabled=\"!avatarFile\"\n            @click=\"uploadAvatar\"\n          >\n            上传\n          </v-btn>\n        </v-card-actions>\n      </v-card>\n    </v-dialog>\n\n    <!-- 工作流优化对话框 -->\n    <v-dialog v-model=\"showWorkflowOptimization\" max-width=\"1200\">\n      <v-card>\n        <v-card-title class=\"d-flex align-center\">\n          <v-icon icon=\"mdi-chart-line\" class=\"me-2\"></v-icon>\n          工作流优化分析\n        </v-card-title>\n\n        <v-card-text>\n          <v-row>\n            <!-- 工作流效率分析 -->\n            <v-col cols=\"12\" md=\"6\">\n              <v-card variant=\"outlined\">\n                <v-card-title class=\"text-h6\">效率分析</v-card-title>\n                <v-card-text>\n                  <div class=\"mb-4\">\n                    <div class=\"d-flex justify-space-between mb-2\">\n                      <span>任务处理速度</span>\n                      <span class=\"font-weight-bold\">{{ workflowMetrics.taskProcessingSpeed }}个/天</span>\n                    </div>\n                    <v-progress-linear\n                      :model-value=\"workflowMetrics.taskProcessingSpeed * 10\"\n                      color=\"primary\"\n                      height=\"6\"\n                    ></v-progress-linear>\n                  </div>\n\n                  <div class=\"mb-4\">\n                    <div class=\"d-flex justify-space-between mb-2\">\n                      <span>平均响应时间</span>\n                      <span class=\"font-weight-bold\">{{ workflowMetrics.avgResponseTime }}小时</span>\n                    </div>\n                    <v-progress-linear\n                      :model-value=\"Math.max(0, 100 - workflowMetrics.avgResponseTime * 5)\"\n                      color=\"info\"\n                      height=\"6\"\n                    ></v-progress-linear>\n                  </div>\n\n                  <div class=\"mb-4\">\n                    <div class=\"d-flex justify-space-between mb-2\">\n                      <span>工作流自动化率</span>\n                      <span class=\"font-weight-bold\">{{ workflowMetrics.automationRate }}%</span>\n                    </div>\n                    <v-progress-linear\n                      :model-value=\"workflowMetrics.automationRate\"\n                      color=\"success\"\n                      height=\"6\"\n                    ></v-progress-linear>\n                  </div>\n                </v-card-text>\n              </v-card>\n            </v-col>\n\n            <!-- 优化建议 -->\n            <v-col cols=\"12\" md=\"6\">\n              <v-card variant=\"outlined\">\n                <v-card-title class=\"text-h6\">优化建议</v-card-title>\n                <v-card-text>\n                  <v-list density=\"compact\">\n                    <v-list-item\n                      v-for=\"suggestion in optimizationSuggestions\"\n                      :key=\"suggestion.id\"\n                      :prepend-icon=\"suggestion.icon\"\n                      :title=\"suggestion.title\"\n                      :subtitle=\"suggestion.description\"\n                      @click=\"applySuggestion(suggestion)\"\n                    >\n                      <template v-slot:append>\n                        <v-chip\n                          :color=\"suggestion.impact === 'high' ? 'success' : suggestion.impact === 'medium' ? 'warning' : 'info'\"\n                          size=\"small\"\n                        >\n                          {{ suggestion.impact === 'high' ? '高效果' : suggestion.impact === 'medium' ? '中效果' : '低效果' }}\n                        </v-chip>\n                      </template>\n                    </v-list-item>\n                  </v-list>\n                </v-card-text>\n              </v-card>\n            </v-col>\n\n            <!-- 工作流模板 -->\n            <v-col cols=\"12\">\n              <v-card variant=\"outlined\">\n                <v-card-title class=\"text-h6\">工作流模板</v-card-title>\n                <v-card-text>\n                  <v-row>\n                    <v-col\n                      v-for=\"template in workflowTemplates\"\n                      :key=\"template.id\"\n                      cols=\"12\"\n                      md=\"4\"\n                    >\n                      <v-card\n                        variant=\"outlined\"\n                        class=\"workflow-template-card\"\n                        @click=\"applyWorkflowTemplate(template)\"\n                      >\n                        <v-card-text class=\"text-center\">\n                          <v-icon\n                            :icon=\"template.icon\"\n                            size=\"48\"\n                            :color=\"template.color\"\n                            class=\"mb-2\"\n                          ></v-icon>\n                          <div class=\"text-h6 mb-1\">{{ template.name }}</div>\n                          <div class=\"text-caption text-medium-emphasis\">{{ template.description }}</div>\n                        </v-card-text>\n                      </v-card>\n                    </v-col>\n                  </v-row>\n                </v-card-text>\n              </v-card>\n            </v-col>\n          </v-row>\n        </v-card-text>\n\n        <v-card-actions>\n          <v-spacer></v-spacer>\n          <v-btn\n            color=\"grey-darken-1\"\n            variant=\"text\"\n            @click=\"showWorkflowOptimization = false\"\n          >\n            关闭\n          </v-btn>\n          <v-btn\n            color=\"primary\"\n            variant=\"text\"\n            @click=\"exportWorkflowReport\"\n          >\n            导出报告\n          </v-btn>\n        </v-card-actions>\n      </v-card>\n    </v-dialog>\n\n    <!-- 操作成功提示 -->\n    <v-snackbar\n      v-model=\"showSnackbar\"\n      color=\"success\"\n    >\n      {{ snackbarText }}\n\n      <template v-slot:actions>\n        <v-btn\n          variant=\"text\"\n          @click=\"showSnackbar = false\"\n        >\n          关闭\n        </v-btn>\n      </template>\n    </v-snackbar>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'ProfileView',\n  data() {\n    return {\n      editMode: false,\n      valid: true,\n      showUploadDialog: false,\n      avatarFile: null,\n      avatarPreview: null,\n      showSnackbar: false,\n      snackbarText: '',\n      showWorkflowOptimization: false,\n\n      nameRules: [\n        v => !!v || '姓名不能为空',\n        v => v.length <= 20 || '姓名不能超过20个字符'\n      ],\n      emailRules: [\n        v => !!v || '邮箱不能为空',\n        v => /.+@.+\\..+/.test(v) || '邮箱格式不正确'\n      ],\n      phoneRules: [\n        v => !!v || '手机号码不能为空',\n        v => /^1[3-9]\\d{9}$/.test(v) || '手机号码格式不正确'\n      ],\n\n      // 医疗部门选项\n      medicalDepartments: [\n        { title: '信息技术部', value: '信息技术部' },\n        { title: '医疗信息部', value: '医疗信息部' },\n        { title: '项目管理部', value: '项目管理部' },\n        { title: '系统集成部', value: '系统集成部' },\n        { title: '数据分析部', value: '数据分析部' },\n        { title: '质量管理部', value: '质量管理部' },\n        { title: '运维支持部', value: '运维支持部' },\n        { title: '培训服务部', value: '培训服务部' }\n      ],\n\n      user: {\n        name: '李医生',\n        email: '<EMAIL>',\n        phone: '13800138000',\n        department: '医疗信息部',\n        position: '医疗项目经理',\n        joinDate: '2022-03-15',\n        address: '北京市朝阳区医疗科技园区A座',\n        bio: '拥有10年医疗信息化项目管理经验，专注于医院信息系统集成与数据迁移。擅长医疗流程优化，曾成功带领团队完成多个三甲医院的信息化建设项目。持有PMP项目管理认证和医疗信息化专业认证。',\n        avatar: 'https://ui-avatars.com/api/?name=李医生&background=1565C0&color=fff'\n      },\n\n      stats: {\n        projects: 15,        // 参与的医疗项目数量\n        tasks: 189,          // 完成的任务数量\n        meetings: 67,        // 参加的项目会议\n        documents: 45,       // 提交的项目文档\n        taskCompletionRate: 94,  // 任务完成率\n        onTimeRate: 91       // 按时完成率\n      },\n\n      // 工作流优化相关数据\n      workflowMetrics: {\n        taskProcessingSpeed: 8.5,\n        avgResponseTime: 2.3,\n        automationRate: 75\n      },\n\n      pendingItems: [\n        { id: 1, title: '北京协和医院HIS系统审批', priority: 'high', type: 'approval' },\n        { id: 2, title: '上海瑞金医院数据迁移验收', priority: 'medium', type: 'review' },\n        { id: 3, title: '医疗设备接口测试确认', priority: 'high', type: 'confirmation' },\n        { id: 4, title: '医院培训计划审核', priority: 'low', type: 'approval' }\n      ],\n\n      optimizationSuggestions: [\n        {\n          id: 1,\n          title: '启用任务自动分配',\n          description: '根据团队成员工作负载自动分配新任务',\n          icon: 'mdi-account-multiple-plus',\n          impact: 'high',\n          action: 'enable_auto_assignment'\n        },\n        {\n          id: 2,\n          title: '设置状态自动流转',\n          description: '任务完成后自动触发下一阶段',\n          icon: 'mdi-arrow-right-circle',\n          impact: 'high',\n          action: 'enable_auto_transition'\n        },\n        {\n          id: 3,\n          title: '优化通知频率',\n          description: '减少非关键通知，提高工作专注度',\n          icon: 'mdi-bell-outline',\n          impact: 'medium',\n          action: 'optimize_notifications'\n        },\n        {\n          id: 4,\n          title: '启用智能提醒',\n          description: '基于历史数据预测任务延期风险',\n          icon: 'mdi-brain',\n          impact: 'high',\n          action: 'enable_smart_reminders'\n        }\n      ],\n\n      workflowTemplates: [\n        {\n          id: 1,\n          name: '医疗项目标准流程',\n          description: '适用于医疗项目的标准化工作流',\n          icon: 'mdi-hospital-box',\n          color: 'primary'\n        },\n        {\n          id: 2,\n          name: '敏捷开发流程',\n          description: '快速迭代的敏捷开发工作流',\n          icon: 'mdi-rocket-launch',\n          color: 'success'\n        },\n        {\n          id: 3,\n          name: '审批密集型流程',\n          description: '需要多层审批的严格工作流',\n          icon: 'mdi-shield-check',\n          color: 'warning'\n        }\n      ]\n    }\n  },\n  computed: {\n    workflowEfficiency() {\n      // 基于任务完成率、按时完成率和自动化率计算工作流效率\n      const taskWeight = 0.4\n      const timeWeight = 0.3\n      const autoWeight = 0.3\n\n      return Math.round(\n        this.stats.taskCompletionRate * taskWeight +\n        this.stats.onTimeRate * timeWeight +\n        this.workflowMetrics.automationRate * autoWeight\n      )\n    }\n  },\n  watch: {\n    avatarFile(file) {\n      if (file) {\n        this.createImagePreview(file)\n      } else {\n        this.avatarPreview = null\n      }\n    }\n  },\n  methods: {\n    saveProfile() {\n      // 在实际应用中，这里会发送请求到后端保存个人资料\n      this.editMode = false\n      this.showSuccessMessage('个人资料保存成功')\n    },\n    createImagePreview(file) {\n      const reader = new FileReader()\n      reader.readAsDataURL(file)\n      reader.onload = e => {\n        this.avatarPreview = e.target.result\n      }\n    },\n    uploadAvatar() {\n      // 在实际应用中，这里会发送请求到后端上传头像\n      if (this.avatarPreview) {\n        this.user.avatar = this.avatarPreview\n      }\n      this.showUploadDialog = false\n      this.avatarFile = null\n      this.showSuccessMessage('头像上传成功')\n    },\n    showSuccessMessage(text) {\n      this.snackbarText = text\n      this.showSnackbar = true\n    },\n\n    // 工作流优化相关方法\n    getEfficiencyColor(efficiency) {\n      if (efficiency >= 85) return 'success'\n      if (efficiency >= 70) return 'warning'\n      return 'error'\n    },\n\n    handlePendingItem(item) {\n      // 处理待办事项\n      switch (item.type) {\n        case 'approval':\n          this.$router.push('/projects')\n          break\n        case 'review':\n          this.$router.push('/kanban')\n          break\n        case 'confirmation':\n          this.$router.push('/meetings')\n          break\n      }\n      this.showSuccessMessage(`正在处理：${item.title}`)\n    },\n\n    applySuggestion(suggestion) {\n      // 应用优化建议\n      switch (suggestion.action) {\n        case 'enable_auto_assignment':\n          this.enableAutoAssignment()\n          break\n        case 'enable_auto_transition':\n          this.enableAutoTransition()\n          break\n        case 'optimize_notifications':\n          this.optimizeNotifications()\n          break\n        case 'enable_smart_reminders':\n          this.enableSmartReminders()\n          break\n      }\n      this.showSuccessMessage(`已应用优化建议：${suggestion.title}`)\n    },\n\n    applyWorkflowTemplate(template) {\n      // 应用工作流模板\n      this.showSuccessMessage(`正在应用工作流模板：${template.name}`)\n      // 这里可以调用API来应用模板\n    },\n\n    exportWorkflowReport() {\n      // 导出工作流报告\n      const reportData = {\n        user: this.user.name,\n        date: new Date().toLocaleDateString(),\n        efficiency: this.workflowEfficiency,\n        metrics: this.workflowMetrics,\n        suggestions: this.optimizationSuggestions.length\n      }\n\n      // 模拟导出功能\n      console.log('导出工作流报告:', reportData)\n      this.showSuccessMessage('工作流报告已导出')\n    },\n\n    // 优化功能实现\n    enableAutoAssignment() {\n      // 启用自动分配功能\n      this.workflowMetrics.automationRate = Math.min(100, this.workflowMetrics.automationRate + 10)\n    },\n\n    enableAutoTransition() {\n      // 启用自动流转功能\n      this.workflowMetrics.automationRate = Math.min(100, this.workflowMetrics.automationRate + 15)\n      this.workflowMetrics.avgResponseTime = Math.max(0.5, this.workflowMetrics.avgResponseTime - 0.5)\n    },\n\n    optimizeNotifications() {\n      // 优化通知设置\n      this.workflowMetrics.avgResponseTime = Math.max(0.5, this.workflowMetrics.avgResponseTime - 0.3)\n    },\n\n    enableSmartReminders() {\n      // 启用智能提醒\n      this.workflowMetrics.taskProcessingSpeed = Math.min(15, this.workflowMetrics.taskProcessingSpeed + 1.5)\n      this.stats.onTimeRate = Math.min(100, this.stats.onTimeRate + 5)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.workflow-template-card {\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.workflow-template-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.workflow-template-card .v-card-text {\n  padding: 24px;\n}\n\n/* 工作流效率指标样式 */\n.v-progress-linear {\n  border-radius: 4px;\n}\n\n/* 待处理事项样式 */\n.v-chip {\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.v-chip:hover {\n  transform: scale(1.05);\n}\n\n/* 优化建议列表样式 */\n.v-list-item {\n  border-radius: 8px;\n  margin-bottom: 8px;\n  transition: background-color 0.2s ease;\n}\n\n.v-list-item:hover {\n  background-color: rgba(0, 0, 0, 0.04);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .workflow-template-card .v-card-text {\n    padding: 16px;\n  }\n\n  .v-icon {\n    font-size: 36px !important;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAA2C;;EAC/CA,KAAK,EAAC;AAAqB;;EASzBA,KAAK,EAAC;AAAsC;;EAM9CA,KAAK,EAAC;AAA0B;;EAyB9BA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAAa;;EAQlBA,KAAK,EAAC;AAAyB;;EAChCA,KAAK,EAAC;AAAmC;;EA2BzCA,KAAK,EAAC;AAAa;;EAGbA,KAAK,EAAC;AAA+B;;EAIrCA,KAAK,EAAC;AAA+B;;EA6M3CA,KAAK,EAAC;AAA2C;;EAgB7CA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAA0B;;EAMlCA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAA0B;;EAMlCA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAA0B;;EAMlCA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAA0B;;EAQtCA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAgD;;EAEpDA,KAAK,EAAC;AAA8B;;EAUxCA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAgD;;EAEpDA,KAAK,EAAC;AAA8B;;EAWxCA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAgD;;EAEpDA,KAAK,EAAC;AAA8B;;EAlXzDC,GAAA;AAAA;;EAAAA,GAAA;EA8ZoCD,KAAK,EAAC;;;EA0CnBA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAmC;;EAEtCA,KAAK,EAAC;AAAkB;;EAS7BA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAmC;;EAEtCA,KAAK,EAAC;AAAkB;;EAS7BA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAmC;;EAEtCA,KAAK,EAAC;AAAkB;;EAgErBA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAniBtEE,mBAAA,CAklBM,OAllBNC,UAklBM,GAjlBJC,mBAAA,qBAAwB,EACxBC,mBAAA,CAoCM,OApCNC,UAoCM,GAnCJD,mBAAA,CAkCM,OAlCNE,UAkCM,GAjCJF,mBAAA,CAcM,OAdNG,UAcM,GAbJC,YAAA,CAKUC,iBAAA;IAJRC,IAAI,EAAC,oBAAoB;IACzBC,IAAI,EAAC,IAAI;IACTC,KAAK,EAAC,iBAAiB;IACvBb,KAAK,EAAC;MAERK,mBAAA,CAMM,c,4BALJA,mBAAA,CAAwD;IAApDL,KAAK,EAAC;EAAoC,GAAC,MAAI,sBACnDK,mBAAA,CAGI,KAHJS,UAGI,GAFFL,YAAA,CAAgEC,iBAAA;IAAxDC,IAAI,EAAC,kBAAkB;IAACC,IAAI,EAAC,IAAI;IAACZ,KAAK,EAAC;kCAf9De,gBAAA,CAe8E,qBAElE,G,OAGJV,mBAAA,CAiBM,OAjBNW,UAiBM,GAhBJP,YAAA,CAOQQ,gBAAA;IANLJ,KAAK,EAAEK,KAAA,CAAAC,QAAQ;IACf,cAAY,EAAED,KAAA,CAAAC,QAAQ;IACvBnB,KAAK,EAAC,qBAAqB;IAC1BoB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEJ,KAAA,CAAAC,QAAQ,IAAID,KAAA,CAAAC,QAAQ;;IAzBxCI,OAAA,EAAAC,QAAA,CA2BY,MAAgC,CA3B5CT,gBAAA,CAAAU,gBAAA,CA2BeP,KAAA,CAAAC,QAAQ,mC;IA3BvBO,CAAA;gDA6BUjB,YAAA,CAOQQ,gBAAA;IANNJ,KAAK,EAAC,cAAc;IACpB,cAAY,EAAC,gBAAgB;IAC7Bc,OAAO,EAAC,UAAU;IACjBP,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEJ,KAAA,CAAAU,wBAAwB;;IAjC5CL,OAAA,EAAAC,QAAA,CAkCW,MAEDH,MAAA,SAAAA,MAAA,QApCVN,gBAAA,CAkCW,SAED,E;IApCVW,CAAA;IAAAG,EAAA;YAyCIpB,YAAA,CAqWQqB,gBAAA;IA9YZP,OAAA,EAAAC,QAAA,CA0CM,MAyGQ,CAzGRf,YAAA,CAyGQsB,gBAAA;MAzGDC,IAAI,EAAC,IAAI;MAACC,EAAE,EAAC;;MA1C1BV,OAAA,EAAAC,QAAA,CA2CQ,MAAsB,CAAtBpB,mBAAA,mBAAsB,EACtBK,YAAA,CAmDSyB,iBAAA;QAnDDlC,KAAK,EAAC;MAAmB;QA5CzCuB,OAAA,EAAAC,QAAA,CA6CU,MAqBM,CArBNnB,mBAAA,CAqBM,OArBN8B,UAqBM,GApBJ9B,mBAAA,CAmBM,OAnBN+B,UAmBM,GAlBJ3B,YAAA,CAMW4B,mBAAA;UANDzB,IAAI,EAAC,KAAK;UAACZ,KAAK,EAAC,MAAM;UAACa,KAAK,EAAC;;UA/CtDU,OAAA,EAAAC,QAAA,CAgDgB,MAIQ,CAJRf,YAAA,CAIQ6B,gBAAA;YAJAC,GAAG,EAAErB,KAAA,CAAAsB,IAAI,CAACC,MAAM;YAAEC,GAAG,EAAC;;YACXC,WAAW,EAAAnB,QAAA,CAC1B,MAAsE,CAAtEf,YAAA,CAAsEC,iBAAA;cAA9DC,IAAI,EAAC,aAAa;cAACC,IAAI,EAAC,IAAI;cAACC,KAAK,EAAC;;YAlD/Da,CAAA;;UAAAA,CAAA;YAsDcrB,mBAAA,CAAwD,MAAxDuC,UAAwD,EAAAnB,gBAAA,CAAjBP,KAAA,CAAAsB,IAAI,CAACK,IAAI,kBAChDxC,mBAAA,CAAoE,KAApEyC,WAAoE,EAAArB,gBAAA,CAApBP,KAAA,CAAAsB,IAAI,CAACO,QAAQ,kBAC7DtC,YAAA,CAQSuC,iBAAA;UAPPnC,KAAK,EAAC,OAAO;UACb,YAAU,EAAC,iBAAiB;UAC5BD,IAAI,EAAC,OAAO;UACZZ,KAAK,EAAC;;UA5DtBuB,OAAA,EAAAC,QAAA,CA8DgB,MAA+C,CAA/Cf,YAAA,CAA+CC,iBAAA;YAAvCuC,KAAK,EAAL,EAAK;YAACtC,IAAI,EAAC;0CA9DnCI,gBAAA,CA8D+D,UAEjD,G;UAhEdW,CAAA;UAAAG,EAAA;gBAoEUpB,YAAA,CA0BcyC,sBAAA;UA1BDlD,KAAK,EAAC;QAAM;UApEnCuB,OAAA,EAAAC,QAAA,CAqEY,MAUQ,CATAN,KAAA,CAAAC,QAAQ,I,cADhBgC,YAAA,CAUQlC,gBAAA;YA/EpBhB,GAAA;YAuEcY,KAAK,EAAC,iBAAiB;YACvBc,OAAO,EAAC,UAAU;YAClByB,KAAK,EAAL,EAAK;YACL,cAAY,EAAC,YAAY;YACzBpD,KAAK,EAAC,MAAM;YACXoB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEJ,KAAA,CAAAmC,gBAAgB;;YA5EtC9B,OAAA,EAAAC,QAAA,CA6Ea,MAEDH,MAAA,SAAAA,MAAA,QA/EZN,gBAAA,CA6Ea,QAED,E;YA/EZW,CAAA;YAAAG,EAAA;gBAAAzB,mBAAA,gBAiFYA,mBAAA,UAAa,EACbC,mBAAA,CAWM,OAXNiD,WAWM,GAVJ7C,YAAA,CASQqB,gBAAA;YATDyB,KAAK,EAAL;UAAK;YAnF1BhC,OAAA,EAAAC,QAAA,CAoFgB,MAGQ,CAHRf,YAAA,CAGQsB,gBAAA;cAHDC,IAAI,EAAC;YAAG;cApF/BT,OAAA,EAAAC,QAAA,CAqFkB,MAAqE,CAArEnB,mBAAA,CAAqE,OAArEmD,WAAqE,EAAA/B,gBAAA,CAAvBP,KAAA,CAAAuC,KAAK,CAACC,QAAQ,kB,4BAC5DrD,mBAAA,CAAyD;gBAApDL,KAAK,EAAC;cAAmC,GAAC,MAAI,qB;cAtFrE0B,CAAA;cAAAG,EAAA;gBAwFgBpB,YAAA,CAGQsB,gBAAA;cAHDC,IAAI,EAAC;YAAG;cAxF/BT,OAAA,EAAAC,QAAA,CAyFkB,MAA0E,CAA1EnB,mBAAA,CAA0E,OAA1EsD,WAA0E,EAAAlC,gBAAA,CAA5BmC,QAAA,CAAAC,kBAAkB,IAAG,GAAC,iB,4BACpExD,mBAAA,CAAyD;gBAApDL,KAAK,EAAC;cAAmC,GAAC,MAAI,qB;cA1FrE0B,CAAA;cAAAG,EAAA;;YAAAH,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;UAiGQtB,mBAAA,mBAAsB,EACtBK,YAAA,CAgDSyB,iBAAA;QAhDDlC,KAAK,EAAC;MAAc;QAlGpCuB,OAAA,EAAAC,QAAA,CAmGU,MAGe,CAHff,YAAA,CAGeqD,uBAAA;UAHD9D,KAAK,EAAC;QAAqB;UAnGnDuB,OAAA,EAAAC,QAAA,CAoGY,MAAsF,CAAtFf,YAAA,CAAsFC,iBAAA;YAA9EC,IAAI,EAAC,0BAA0B;YAACE,KAAK,EAAC,iBAAiB;YAACb,KAAK,EAAC;0CApGlFe,gBAAA,CAoGkG,QAExF,G;UAtGVW,CAAA;UAAAG,EAAA;YAuGUpB,YAAA,CA0CcyC,sBAAA;UAjJxB3B,OAAA,EAAAC,QAAA,CAwGY,MAwCS,CAxCTf,YAAA,CAwCSsD,iBAAA;YAxCD/D,KAAK,EAAC;UAAM;YAxGhCuB,OAAA,EAAAC,QAAA,CAyGc,MAQc,CARdf,YAAA,CAQcuD,sBAAA;cARDhE,KAAK,EAAC;YAAM;cACNiE,OAAO,EAAAzC,QAAA,CACtB,MAEW,CAFXf,YAAA,CAEW4B,mBAAA;gBAFDzB,IAAI,EAAC,IAAI;gBAACC,KAAK,EAAC,cAAc;gBAACb,KAAK,EAAC;;gBA3GjEuB,OAAA,EAAAC,QAAA,CA4GoB,MAAgD,CAAhDf,YAAA,CAAgDC,iBAAA;kBAAxCC,IAAI,EAAC,WAAW;kBAACE,KAAK,EAAC;;gBA5GnDa,CAAA;;cAAAH,OAAA,EAAAC,QAAA,CA+GgB,MAAqF,CAArFf,YAAA,CAAqFyD,4BAAA;gBAAlElE,KAAK,EAAC;cAAmC;gBA/G5EuB,OAAA,EAAAC,QAAA,CA+G6E,MAAIH,MAAA,SAAAA,MAAA,QA/GjFN,gBAAA,CA+G6E,MAAI,E;gBA/GjFW,CAAA;gBAAAG,EAAA;kBAgHgBpB,YAAA,CAAoG0D,+BAAA;gBAA9EnE,KAAK,EAAC;cAAgC;gBAhH5EuB,OAAA,EAAAC,QAAA,CAgH6E,MAAgB,CAhH7FT,gBAAA,CAAAU,gBAAA,CAgHgFP,KAAA,CAAAsB,IAAI,CAAC4B,KAAK,iB;gBAhH1F1C,CAAA;;cAAAA,CAAA;gBAmHcjB,YAAA,CAQcuD,sBAAA;cARDhE,KAAK,EAAC;YAAM;cACNiE,OAAO,EAAAzC,QAAA,CACtB,MAEW,CAFXf,YAAA,CAEW4B,mBAAA;gBAFDzB,IAAI,EAAC,IAAI;gBAACC,KAAK,EAAC,iBAAiB;gBAACb,KAAK,EAAC;;gBArHpEuB,OAAA,EAAAC,QAAA,CAsHoB,MAAgD,CAAhDf,YAAA,CAAgDC,iBAAA;kBAAxCC,IAAI,EAAC,WAAW;kBAACE,KAAK,EAAC;;gBAtHnDa,CAAA;;cAAAH,OAAA,EAAAC,QAAA,CAyHgB,MAAqF,CAArFf,YAAA,CAAqFyD,4BAAA;gBAAlElE,KAAK,EAAC;cAAmC;gBAzH5EuB,OAAA,EAAAC,QAAA,CAyH6E,MAAIH,MAAA,SAAAA,MAAA,QAzHjFN,gBAAA,CAyH6E,MAAI,E;gBAzHjFW,CAAA;gBAAAG,EAAA;kBA0HgBpB,YAAA,CAAoG0D,+BAAA;gBAA9EnE,KAAK,EAAC;cAAgC;gBA1H5EuB,OAAA,EAAAC,QAAA,CA0H6E,MAAgB,CA1H7FT,gBAAA,CAAAU,gBAAA,CA0HgFP,KAAA,CAAAsB,IAAI,CAAC6B,KAAK,iB;gBA1H1F3C,CAAA;;cAAAA,CAAA;gBA6HcjB,YAAA,CAQcuD,sBAAA;cARDhE,KAAK,EAAC;YAAM;cACNiE,OAAO,EAAAzC,QAAA,CACtB,MAEW,CAFXf,YAAA,CAEW4B,mBAAA;gBAFDzB,IAAI,EAAC,IAAI;gBAACC,KAAK,EAAC,iBAAiB;gBAACb,KAAK,EAAC;;gBA/HpEuB,OAAA,EAAAC,QAAA,CAgIoB,MAA4D,CAA5Df,YAAA,CAA4DC,iBAAA;kBAApDC,IAAI,EAAC,uBAAuB;kBAACE,KAAK,EAAC;;gBAhI/Da,CAAA;;cAAAH,OAAA,EAAAC,QAAA,CAmIgB,MAAqF,CAArFf,YAAA,CAAqFyD,4BAAA;gBAAlElE,KAAK,EAAC;cAAmC;gBAnI5EuB,OAAA,EAAAC,QAAA,CAmI6E,MAAIH,MAAA,SAAAA,MAAA,QAnIjFN,gBAAA,CAmI6E,MAAI,E;gBAnIjFW,CAAA;gBAAAG,EAAA;kBAoIgBpB,YAAA,CAAyG0D,+BAAA;gBAAnFnE,KAAK,EAAC;cAAgC;gBApI5EuB,OAAA,EAAAC,QAAA,CAoI6E,MAAqB,CApIlGT,gBAAA,CAAAU,gBAAA,CAoIgFP,KAAA,CAAAsB,IAAI,CAAC8B,UAAU,iB;gBApI/F5C,CAAA;;cAAAA,CAAA;gBAuIcjB,YAAA,CAQcuD,sBAAA;cARDhE,KAAK,EAAC;YAAM;cACNiE,OAAO,EAAAzC,QAAA,CACtB,MAEW,CAFXf,YAAA,CAEW4B,mBAAA;gBAFDzB,IAAI,EAAC,IAAI;gBAACC,KAAK,EAAC,gBAAgB;gBAACb,KAAK,EAAC;;gBAzInEuB,OAAA,EAAAC,QAAA,CA0IoB,MAAqD,CAArDf,YAAA,CAAqDC,iBAAA;kBAA7CC,IAAI,EAAC,gBAAgB;kBAACE,KAAK,EAAC;;gBA1IxDa,CAAA;;cAAAH,OAAA,EAAAC,QAAA,CA6IgB,MAAqF,CAArFf,YAAA,CAAqFyD,4BAAA;gBAAlElE,KAAK,EAAC;cAAmC;gBA7I5EuB,OAAA,EAAAC,QAAA,CA6I6E,MAAIH,MAAA,SAAAA,MAAA,QA7IjFN,gBAAA,CA6I6E,MAAI,E;gBA7IjFW,CAAA;gBAAAG,EAAA;kBA8IgBpB,YAAA,CAAsG0D,+BAAA;gBAAhFnE,KAAK,EAAC;cAAgC;gBA9I5EuB,OAAA,EAAAC,QAAA,CA8I6E,MAAkB,CA9I/FT,gBAAA,CAAAU,gBAAA,CA8IgFP,KAAA,CAAAsB,IAAI,CAAC+B,OAAO,iB;gBA9I5F7C,CAAA;;cAAAA,CAAA;;YAAAA,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;QAqJMjB,YAAA,CAwPQsB,gBAAA;MAxPDC,IAAI,EAAC,IAAI;MAACC,EAAE,EAAC;;MArJ1BV,OAAA,EAAAC,QAAA,CAsJQ,MAAsB,CAAtBpB,mBAAA,mBAAsB,EACtBK,YAAA,CA2ISyB,iBAAA;QA3IDlC,KAAK,EAAC;MAAmB;QAvJzCuB,OAAA,EAAAC,QAAA,CAwJU,MAgBe,CAhBff,YAAA,CAgBeqD,uBAAA;UAhBD9D,KAAK,EAAC;QAAqB;UAxJnDuB,OAAA,EAAAC,QAAA,CAyJY,MAA8E,CAA9Ef,YAAA,CAA8EC,iBAAA;YAAtEC,IAAI,EAAC,kBAAkB;YAACE,KAAK,EAAC,iBAAiB;YAACb,KAAK,EAAC;0CAzJ1Ee,gBAAA,CAyJ0F,QAE9E,IAAAN,YAAA,CAAqB+D,mBAAA,GACrB/D,YAAA,CAWSuC,iBAAA;YAVNnC,KAAK,EAAEK,KAAA,CAAAC,QAAQ;YAChBP,IAAI,EAAC,OAAO;YACZe,OAAO,EAAC;;YA/JtBJ,OAAA,EAAAC,QAAA,CAiKc,MAIU,CAJVf,YAAA,CAIUC,iBAAA;cAHPC,IAAI,EAAEO,KAAA,CAAAC,QAAQ;cACf8B,KAAK,EAAL,EAAK;cACLrC,IAAI,EAAC;+CApKrBG,gBAAA,CAqKwB,GACV,GAAAU,gBAAA,CAAGP,KAAA,CAAAC,QAAQ,mC;YAtKzBO,CAAA;;UAAAA,CAAA;UAAAG,EAAA;YAyKUpB,YAAA,CAwHcyC,sBAAA;UAjSxB3B,OAAA,EAAAC,QAAA,CA0KY,MAsHS,CAtHTf,YAAA,CAsHSgE,iBAAA;YAtHDC,GAAG,EAAC,MAAM;YA1K9BC,UAAA,EA0KwCzD,KAAA,CAAA0D,KAAK;YA1K7C,uBAAAvD,MAAA,SAAAA,MAAA,OAAAC,MAAA,IA0KwCJ,KAAA,CAAA0D,KAAK,GAAAtD,MAAA;;YA1K7CC,OAAA,EAAAC,QAAA,CA2Kc,MA+FQ,CA/FRf,YAAA,CA+FQqB,gBAAA;cA1QtBP,OAAA,EAAAC,QAAA,CA4KgB,MAWQ,CAXRf,YAAA,CAWQsB,gBAAA;gBAXDC,IAAI,EAAC,IAAI;gBAACC,EAAE,EAAC;;gBA5KpCV,OAAA,EAAAC,QAAA,CA6KkB,MASgB,CAThBf,YAAA,CASgBoE,uBAAA;kBAtLlCF,UAAA,EA8K6BzD,KAAA,CAAAsB,IAAI,CAACK,IAAI;kBA9KtC,uBAAAxB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA8K6BJ,KAAA,CAAAsB,IAAI,CAACK,IAAI,GAAAvB,MAAA;kBAClBwD,KAAK,EAAC,IAAI;kBACTC,KAAK,EAAE7D,KAAA,CAAA8D,SAAS;kBACjBrD,OAAO,EAAC,UAAU;kBACjBsD,QAAQ,GAAG/D,KAAA,CAAAC,QAAQ;kBACnBN,KAAK,EAAEK,KAAA,CAAAC,QAAQ;kBAChB,oBAAkB,EAAC,aAAa;kBAChC+D,QAAQ,EAAR;;gBArLpBxD,CAAA;kBAyLgBjB,YAAA,CAUQsB,gBAAA;gBAVDC,IAAI,EAAC,IAAI;gBAACC,EAAE,EAAC;;gBAzLpCV,OAAA,EAAAC,QAAA,CA0LkB,MAQgB,CARhBf,YAAA,CAQgBoE,uBAAA;kBAlMlCF,UAAA,EA2L6BzD,KAAA,CAAAsB,IAAI,CAAC4B,KAAK;kBA3LvC,uBAAA/C,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA2L6BJ,KAAA,CAAAsB,IAAI,CAAC4B,KAAK,GAAA9C,MAAA;kBACnBwD,KAAK,EAAC,MAAM;kBACXC,KAAK,EAAE7D,KAAA,CAAAiE,UAAU;kBAClBxD,OAAO,EAAC,UAAU;kBAClBsD,QAAQ,EAAR,EAAQ;kBACRG,QAAQ,EAAR,EAAQ;kBACR,oBAAkB,EAAC;;gBAjMvC1D,CAAA;kBAqMgBjB,YAAA,CAUQsB,gBAAA;gBAVDC,IAAI,EAAC,IAAI;gBAACC,EAAE,EAAC;;gBArMpCV,OAAA,EAAAC,QAAA,CAsMkB,MAQgB,CARhBf,YAAA,CAQgBoE,uBAAA;kBA9MlCF,UAAA,EAuM6BzD,KAAA,CAAAsB,IAAI,CAAC6B,KAAK;kBAvMvC,uBAAAhD,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAuM6BJ,KAAA,CAAAsB,IAAI,CAAC6B,KAAK,GAAA/C,MAAA;kBACnBwD,KAAK,EAAC,MAAM;kBACXC,KAAK,EAAE7D,KAAA,CAAAmE,UAAU;kBAClB1D,OAAO,EAAC,UAAU;kBACjBsD,QAAQ,GAAG/D,KAAA,CAAAC,QAAQ;kBACnBN,KAAK,EAAEK,KAAA,CAAAC,QAAQ;kBAChB,oBAAkB,EAAC;;gBA7MvCO,CAAA;kBAiNgBjB,YAAA,CAUQsB,gBAAA;gBAVDC,IAAI,EAAC,IAAI;gBAACC,EAAE,EAAC;;gBAjNpCV,OAAA,EAAAC,QAAA,CAkNkB,MAQY,CARZf,YAAA,CAQY6E,mBAAA;kBA1N9BX,UAAA,EAmN6BzD,KAAA,CAAAsB,IAAI,CAAC8B,UAAU;kBAnN5C,uBAAAjD,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAmN6BJ,KAAA,CAAAsB,IAAI,CAAC8B,UAAU,GAAAhD,MAAA;kBACvBiE,KAAK,EAAErE,KAAA,CAAAsE,kBAAkB;kBAC1BV,KAAK,EAAC,MAAM;kBACZnD,OAAO,EAAC,UAAU;kBACjBsD,QAAQ,GAAG/D,KAAA,CAAAC,QAAQ;kBACnBN,KAAK,EAAEK,KAAA,CAAAC,QAAQ;kBAChB,oBAAkB,EAAC;;gBAzNvCO,CAAA;kBA6NgBjB,YAAA,CASQsB,gBAAA;gBATDC,IAAI,EAAC,IAAI;gBAACC,EAAE,EAAC;;gBA7NpCV,OAAA,EAAAC,QAAA,CA8NkB,MAOgB,CAPhBf,YAAA,CAOgBoE,uBAAA;kBArOlCF,UAAA,EA+N6BzD,KAAA,CAAAsB,IAAI,CAACO,QAAQ;kBA/N1C,uBAAA1B,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA+N6BJ,KAAA,CAAAsB,IAAI,CAACO,QAAQ,GAAAzB,MAAA;kBACtBwD,KAAK,EAAC,IAAI;kBACVnD,OAAO,EAAC,UAAU;kBACjBsD,QAAQ,GAAG/D,KAAA,CAAAC,QAAQ;kBACnBN,KAAK,EAAEK,KAAA,CAAAC,QAAQ;kBAChB,oBAAkB,EAAC;;gBApOvCO,CAAA;kBAwOgBjB,YAAA,CASQsB,gBAAA;gBATDC,IAAI,EAAC,IAAI;gBAACC,EAAE,EAAC;;gBAxOpCV,OAAA,EAAAC,QAAA,CAyOkB,MAOgB,CAPhBf,YAAA,CAOgBoE,uBAAA;kBAhPlCF,UAAA,EA0O6BzD,KAAA,CAAAsB,IAAI,CAACiD,QAAQ;kBA1O1C,uBAAApE,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA0O6BJ,KAAA,CAAAsB,IAAI,CAACiD,QAAQ,GAAAnE,MAAA;kBACtBwD,KAAK,EAAC,MAAM;kBACZnD,OAAO,EAAC,UAAU;kBAClBsD,QAAQ,EAAR,EAAQ;kBACRG,QAAQ,EAAR,EAAQ;kBACR,oBAAkB,EAAC;;gBA/OvC1D,CAAA;kBAmPgBjB,YAAA,CASQsB,gBAAA;gBATDC,IAAI,EAAC;cAAI;gBAnPhCT,OAAA,EAAAC,QAAA,CAoPkB,MAOgB,CAPhBf,YAAA,CAOgBoE,uBAAA;kBA3PlCF,UAAA,EAqP6BzD,KAAA,CAAAsB,IAAI,CAAC+B,OAAO;kBArPzC,uBAAAlD,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAqP6BJ,KAAA,CAAAsB,IAAI,CAAC+B,OAAO,GAAAjD,MAAA;kBACrBwD,KAAK,EAAC,MAAM;kBACZnD,OAAO,EAAC,UAAU;kBACjBsD,QAAQ,GAAG/D,KAAA,CAAAC,QAAQ;kBACnBN,KAAK,EAAEK,KAAA,CAAAC,QAAQ;kBAChB,oBAAkB,EAAC;;gBA1PvCO,CAAA;kBA8PgBjB,YAAA,CAWQsB,gBAAA;gBAXDC,IAAI,EAAC;cAAI;gBA9PhCT,OAAA,EAAAC,QAAA,CA+PkB,MASc,CATdf,YAAA,CASciF,qBAAA;kBAxQhCf,UAAA,EAgQ6BzD,KAAA,CAAAsB,IAAI,CAACmD,GAAG;kBAhQrC,uBAAAtE,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAgQ6BJ,KAAA,CAAAsB,IAAI,CAACmD,GAAG,GAAArE,MAAA;kBACjBwD,KAAK,EAAC,MAAM;kBACZnD,OAAO,EAAC,UAAU;kBACjBsD,QAAQ,GAAG/D,KAAA,CAAAC,QAAQ;kBACnBN,KAAK,EAAEK,KAAA,CAAAC,QAAQ;kBAChB,oBAAkB,EAAC,kBAAkB;kBACrC,WAAS,EAAT,EAAS;kBACTyE,IAAI,EAAC;;gBAvQzBlE,CAAA;;cAAAA,CAAA;gBA4Q2BR,KAAA,CAAAC,QAAQ,I,cAArBgC,YAAA,CAmBQrB,gBAAA;cA/RtB7B,GAAA;YAAA;cAAAsB,OAAA,EAAAC,QAAA,CA6QgB,MAiBQ,CAjBRf,YAAA,CAiBQsB,gBAAA;gBAjBDC,IAAI,EAAC,IAAI;gBAAChC,KAAK,EAAC;;gBA7QvCuB,OAAA,EAAAC,QAAA,CA8QkB,MAMQ,CANRf,YAAA,CAMQQ,gBAAA;kBALNJ,KAAK,EAAC,MAAM;kBACZc,OAAO,EAAC,UAAU;kBACjBP,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEJ,KAAA,CAAAC,QAAQ;;kBAjRpCI,OAAA,EAAAC,QAAA,CAkRmB,MAEDH,MAAA,SAAAA,MAAA,QApRlBN,gBAAA,CAkRmB,MAED,E;kBApRlBW,CAAA;kBAAAG,EAAA;oBAqRkBpB,YAAA,CAQQQ,gBAAA;kBAPNJ,KAAK,EAAC,iBAAiB;kBACvBb,KAAK,EAAC,qBAAqB;kBAC1BoF,QAAQ,GAAGlE,KAAA,CAAA0D,KAAK;kBAChBxD,OAAK,EAAEwC,QAAA,CAAAiC;;kBAzR5BtE,OAAA,EAAAC,QAAA,CA2RoB,MAA+C,CAA/Cf,YAAA,CAA+CC,iBAAA;oBAAvCuC,KAAK,EAAL,EAAK;oBAACtC,IAAI,EAAC;kDA3RvCI,gBAAA,CA2RmE,QAEjD,G;kBA7RlBW,CAAA;kBAAAG,EAAA;;gBAAAH,CAAA;;cAAAA,CAAA;kBAAAtB,mBAAA,e;YAAAsB,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;UAoSQjB,YAAA,CAwGSyB,iBAAA;QA5YjBX,OAAA,EAAAC,QAAA,CAqSU,MAae,CAbff,YAAA,CAaeqD,uBAAA;UAlTzBvC,OAAA,EAAAC,QAAA,CAsSY,MAWM,CAXNnB,mBAAA,CAWM,OAXNyF,WAWM,G,4BAVJzF,mBAAA,CAAiB,cAAX,MAAI,sBACVI,YAAA,CAQQQ,gBAAA;YAPNJ,KAAK,EAAC,SAAS;YACfc,OAAO,EAAC,MAAM;YACdf,IAAI,EAAC,OAAO;YACZ,cAAY,EAAC,gBAAgB;YAC5BQ,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEJ,KAAA,CAAAU,wBAAwB;;YA7ShDL,OAAA,EAAAC,QAAA,CA8Se,MAEDH,MAAA,SAAAA,MAAA,QAhTdN,gBAAA,CA8Se,SAED,E;YAhTdW,CAAA;YAAAG,EAAA;;UAAAH,CAAA;YAmTUjB,YAAA,CAwFcyC,sBAAA;UA3YxB3B,OAAA,EAAAC,QAAA,CAoTY,MA4BQ,CA5BRf,YAAA,CA4BQqB,gBAAA;YAhVpBP,OAAA,EAAAC,QAAA,CAqTc,MAKQ,CALRf,YAAA,CAKQsB,gBAAA;cALDC,IAAI,EAAC,GAAG;cAACC,EAAE,EAAC;;cArTjCV,OAAA,EAAAC,QAAA,CAsTgB,MAGM,CAHNnB,mBAAA,CAGM,OAHN0F,WAGM,GAFJ1F,mBAAA,CAAgE,OAAhE2F,WAAgE,EAAAvE,gBAAA,CAAvBP,KAAA,CAAAuC,KAAK,CAACC,QAAQ,kB,4BACvDrD,mBAAA,CAAyD;gBAApDL,KAAK,EAAC;cAAmC,GAAC,MAAI,qB;cAxTrE0B,CAAA;gBA4TcjB,YAAA,CAKQsB,gBAAA;cALDC,IAAI,EAAC,GAAG;cAACC,EAAE,EAAC;;cA5TjCV,OAAA,EAAAC,QAAA,CA6TgB,MAGM,CAHNnB,mBAAA,CAGM,OAHN4F,WAGM,GAFJ5F,mBAAA,CAA6D,OAA7D6F,WAA6D,EAAAzE,gBAAA,CAApBP,KAAA,CAAAuC,KAAK,CAAC0C,KAAK,kB,4BACpD9F,mBAAA,CAAyD;gBAApDL,KAAK,EAAC;cAAmC,GAAC,MAAI,qB;cA/TrE0B,CAAA;gBAmUcjB,YAAA,CAKQsB,gBAAA;cALDC,IAAI,EAAC,GAAG;cAACC,EAAE,EAAC;;cAnUjCV,OAAA,EAAAC,QAAA,CAoUgB,MAGM,CAHNnB,mBAAA,CAGM,OAHN+F,WAGM,GAFJ/F,mBAAA,CAAgE,OAAhEgG,WAAgE,EAAA5E,gBAAA,CAAvBP,KAAA,CAAAuC,KAAK,CAAC6C,QAAQ,kB,4BACvDjG,mBAAA,CAAyD;gBAApDL,KAAK,EAAC;cAAmC,GAAC,MAAI,qB;cAtUrE0B,CAAA;gBA0UcjB,YAAA,CAKQsB,gBAAA;cALDC,IAAI,EAAC,GAAG;cAACC,EAAE,EAAC;;cA1UjCV,OAAA,EAAAC,QAAA,CA2UgB,MAGM,CAHNnB,mBAAA,CAGM,OAHNkG,WAGM,GAFJlG,mBAAA,CAAiE,OAAjEmG,WAAiE,EAAA/E,gBAAA,CAAxBP,KAAA,CAAAuC,KAAK,CAACgD,SAAS,kB,4BACxDpG,mBAAA,CAAyD;gBAApDL,KAAK,EAAC;cAAmC,GAAC,MAAI,qB;cA7UrE0B,CAAA;;YAAAA,CAAA;cAkVYjB,YAAA,CAAoCiG,oBAAA;YAAzB1G,KAAK,EAAC;UAAM,IAEvBK,mBAAA,CAWM,OAXNsG,WAWM,GAVJtG,mBAAA,CAGM,OAHNuG,WAGM,G,4BAFJvG,mBAAA,CAAoC;YAA/BL,KAAK,EAAC;UAAa,GAAC,OAAK,sBAC9BK,mBAAA,CAA+E,OAA/EwG,WAA+E,EAAApF,gBAAA,CAAlCP,KAAA,CAAAuC,KAAK,CAACqD,kBAAkB,IAAG,GAAC,gB,GAE3ErG,YAAA,CAKqBsG,4BAAA;YAJlB,aAAW,EAAE7F,KAAA,CAAAuC,KAAK,CAACqD,kBAAkB;YACtCjG,KAAK,EAAC,SAAS;YACfmG,MAAM,EAAC,GAAG;YACVC,OAAO,EAAP;sDAIJ5G,mBAAA,CAWM,OAXN6G,WAWM,GAVJ7G,mBAAA,CAGM,OAHN8G,WAGM,G,4BAFJ9G,mBAAA,CAAoC;YAA/BL,KAAK,EAAC;UAAa,GAAC,OAAK,sBAC9BK,mBAAA,CAAuE,OAAvE+G,WAAuE,EAAA3F,gBAAA,CAA1BP,KAAA,CAAAuC,KAAK,CAAC4D,UAAU,IAAG,GAAC,gB,GAEnE5G,YAAA,CAKqBsG,4BAAA;YAJlB,aAAW,EAAE7F,KAAA,CAAAuC,KAAK,CAAC4D,UAAU;YAC9BxG,KAAK,EAAC,MAAM;YACZmG,MAAM,EAAC,GAAG;YACVC,OAAO,EAAP;sDAIJ7G,mBAAA,aAAgB,EAChBC,mBAAA,CAWM,OAXNiH,WAWM,GAVJjH,mBAAA,CAGM,OAHNkH,WAGM,G,4BAFJlH,mBAAA,CAAoC;YAA/BL,KAAK,EAAC;UAAa,GAAC,OAAK,sBAC9BK,mBAAA,CAAyE,OAAzEmH,WAAyE,EAAA/F,gBAAA,CAA5BmC,QAAA,CAAAC,kBAAkB,IAAG,GAAC,gB,GAErEpD,YAAA,CAKqBsG,4BAAA;YAJlB,aAAW,EAAEnD,QAAA,CAAAC,kBAAkB;YAC/BhD,KAAK,EAAE+C,QAAA,CAAA6D,kBAAkB,CAAC7D,QAAA,CAAAC,kBAAkB;YAC7CmD,MAAM,EAAC,GAAG;YACVC,OAAO,EAAP;+DAIJ7G,mBAAA,WAAc,EACHc,KAAA,CAAAwG,YAAY,CAACC,MAAM,Q,cAA9BzH,mBAAA,CAaM,OA1YlB0H,WAAA,GA8XcnH,YAAA,CAAoCiG,oBAAA;YAAzB1G,KAAK,EAAC;UAAM,I,4BACvBK,mBAAA,CAA6C;YAAxCL,KAAK,EAAC;UAAsB,GAAC,OAAK,uB,kBACvCE,mBAAA,CASS2H,SAAA,QAzYvBC,WAAA,CAiY+B5G,KAAA,CAAAwG,YAAY,EAApBK,IAAI;iCADb5E,YAAA,CASSH,iBAAA;cAPN/C,GAAG,EAAE8H,IAAI,CAACC,EAAE;cACZnH,KAAK,EAAEkH,IAAI,CAACE,QAAQ,wBAAwBF,IAAI,CAACE,QAAQ;cAC1DrH,IAAI,EAAC,OAAO;cACZZ,KAAK,EAAC,WAAW;cAChBoB,OAAK,EAAAE,MAAA,IAAEsC,QAAA,CAAAsE,iBAAiB,CAACH,IAAI;;cAtY9CxG,OAAA,EAAAC,QAAA,CAwYgB,MAAgB,CAxYhCT,gBAAA,CAAAU,gBAAA,CAwYmBsG,IAAI,CAACI,KAAK,iB;cAxY7BzG,CAAA;;8CAAAtB,mBAAA,e;UAAAsB,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;MAgZItB,mBAAA,aAAgB,EAChBK,YAAA,CAsCW2H,mBAAA;IAvbfzD,UAAA,EAiZuBzD,KAAA,CAAAmC,gBAAgB;IAjZvC,uBAAAhC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAiZuBJ,KAAA,CAAAmC,gBAAgB,GAAA/B,MAAA;IAAE,WAAS,EAAC;;IAjZnDC,OAAA,EAAAC,QAAA,CAkZM,MAoCS,CApCTf,YAAA,CAoCSyB,iBAAA;MAtbfX,OAAA,EAAAC,QAAA,CAmZQ,MAAiC,CAAjCf,YAAA,CAAiCqD,uBAAA;QAnZzCvC,OAAA,EAAAC,QAAA,CAmZsB,MAAIH,MAAA,SAAAA,MAAA,QAnZ1BN,gBAAA,CAmZsB,MAAI,E;QAnZ1BW,CAAA;QAAAG,EAAA;UAoZQpB,YAAA,CAecyC,sBAAA;QAnatB3B,OAAA,EAAAC,QAAA,CAqZU,MAOgB,CAPhBf,YAAA,CAOgB4H,uBAAA;UA5Z1B1D,UAAA,EAsZqBzD,KAAA,CAAAoH,UAAU;UAtZ/B,uBAAAjH,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAsZqBJ,KAAA,CAAAoH,UAAU,GAAAhH,MAAA;UACnBwD,KAAK,EAAC,MAAM;UACZyD,MAAM,EAAC,SAAS;UAChB,WAAS,EAAT,EAAS;UACT,iBAAe,EAAC,IAAI;UACpB5G,OAAO,EAAC;iDAGCT,KAAA,CAAAsH,aAAa,I,cAAxBtI,mBAAA,CAIM,OAJNuI,WAIM,GAHJhI,YAAA,CAEW4B,mBAAA;UAFDzB,IAAI,EAAC;QAAK;UA/ZhCW,OAAA,EAAAC,QAAA,CAgac,MAAyD,CAAzDf,YAAA,CAAyD6B,gBAAA;YAAjDC,GAAG,EAAErB,KAAA,CAAAsH,aAAa;YAAE9F,GAAG,EAAC;;UAha9ChB,CAAA;gBAAAtB,mBAAA,e;QAAAsB,CAAA;UAoaQjB,YAAA,CAiBiBiI,yBAAA;QArbzBnH,OAAA,EAAAC,QAAA,CAqaU,MAAqB,CAArBf,YAAA,CAAqB+D,mBAAA,GACrB/D,YAAA,CAMQQ,gBAAA;UALNJ,KAAK,EAAC,eAAe;UACrBc,OAAO,EAAC,MAAM;UACbP,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEJ,KAAA,CAAAmC,gBAAgB;;UAzapC9B,OAAA,EAAAC,QAAA,CA0aW,MAEDH,MAAA,SAAAA,MAAA,QA5aVN,gBAAA,CA0aW,MAED,E;UA5aVW,CAAA;UAAAG,EAAA;YA6aUpB,YAAA,CAOQQ,gBAAA;UANNJ,KAAK,EAAC,SAAS;UACfc,OAAO,EAAC,MAAM;UACbyD,QAAQ,GAAGlE,KAAA,CAAAoH,UAAU;UACrBlH,OAAK,EAAEwC,QAAA,CAAA+E;;UAjbpBpH,OAAA,EAAAC,QAAA,CAkbW,MAEDH,MAAA,SAAAA,MAAA,QApbVN,gBAAA,CAkbW,MAED,E;UApbVW,CAAA;UAAAG,EAAA;;QAAAH,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;qCAybItB,mBAAA,cAAiB,EACjBK,YAAA,CAuIW2H,mBAAA;IAjkBfzD,UAAA,EA0buBzD,KAAA,CAAAU,wBAAwB;IA1b/C,uBAAAP,MAAA,SAAAA,MAAA,OAAAC,MAAA,IA0buBJ,KAAA,CAAAU,wBAAwB,GAAAN,MAAA;IAAE,WAAS,EAAC;;IA1b3DC,OAAA,EAAAC,QAAA,CA2bM,MAqIS,CArITf,YAAA,CAqISyB,iBAAA;MAhkBfX,OAAA,EAAAC,QAAA,CA4bQ,MAGe,CAHff,YAAA,CAGeqD,uBAAA;QAHD9D,KAAK,EAAC;MAAqB;QA5bjDuB,OAAA,EAAAC,QAAA,CA6bU,MAAoD,CAApDf,YAAA,CAAoDC,iBAAA;UAA5CC,IAAI,EAAC,gBAAgB;UAACX,KAAK,EAAC;wCA7b9Ce,gBAAA,CA6b8D,WAEtD,G;QA/bRW,CAAA;QAAAG,EAAA;UAicQpB,YAAA,CA4GcyC,sBAAA;QA7iBtB3B,OAAA,EAAAC,QAAA,CAkcU,MA0GQ,CA1GRf,YAAA,CA0GQqB,gBAAA;UA5iBlBP,OAAA,EAAAC,QAAA,CAmcY,MAAgB,CAAhBpB,mBAAA,aAAgB,EAChBK,YAAA,CAyCQsB,gBAAA;YAzCDC,IAAI,EAAC,IAAI;YAACC,EAAE,EAAC;;YApchCV,OAAA,EAAAC,QAAA,CAqcc,MAuCS,CAvCTf,YAAA,CAuCSyB,iBAAA;cAvCDP,OAAO,EAAC;YAAU;cArcxCJ,OAAA,EAAAC,QAAA,CAscgB,MAAiD,CAAjDf,YAAA,CAAiDqD,uBAAA;gBAAnC9D,KAAK,EAAC;cAAS;gBAtc7CuB,OAAA,EAAAC,QAAA,CAsc8C,MAAIH,MAAA,SAAAA,MAAA,QAtclDN,gBAAA,CAsc8C,MAAI,E;gBAtclDW,CAAA;gBAAAG,EAAA;kBAucgBpB,YAAA,CAoCcyC,sBAAA;gBA3e9B3B,OAAA,EAAAC,QAAA,CAwckB,MAUM,CAVNnB,mBAAA,CAUM,OAVNuI,WAUM,GATJvI,mBAAA,CAGM,OAHNwI,WAGM,G,4BAFJxI,mBAAA,CAAmB,cAAb,QAAM,sBACZA,mBAAA,CAAkF,QAAlFyI,WAAkF,EAAArH,gBAAA,CAAhDP,KAAA,CAAA6H,eAAe,CAACC,mBAAmB,IAAG,KAAG,gB,GAE7EvI,YAAA,CAIqBsG,4BAAA;kBAHlB,aAAW,EAAE7F,KAAA,CAAA6H,eAAe,CAACC,mBAAmB;kBACjDnI,KAAK,EAAC,SAAS;kBACfmG,MAAM,EAAC;4DAIX3G,mBAAA,CAUM,OAVN4I,WAUM,GATJ5I,mBAAA,CAGM,OAHN6I,WAGM,G,4BAFJ7I,mBAAA,CAAmB,cAAb,QAAM,sBACZA,mBAAA,CAA6E,QAA7E8I,WAA6E,EAAA1H,gBAAA,CAA3CP,KAAA,CAAA6H,eAAe,CAACK,eAAe,IAAG,IAAE,gB,GAExE3I,YAAA,CAIqBsG,4BAAA;kBAHlB,aAAW,EAAEsC,IAAI,CAACC,GAAG,UAAUpI,KAAA,CAAA6H,eAAe,CAACK,eAAe;kBAC/DvI,KAAK,EAAC,MAAM;kBACZmG,MAAM,EAAC;4DAIX3G,mBAAA,CAUM,OAVNkJ,WAUM,GATJlJ,mBAAA,CAGM,OAHNmJ,WAGM,G,4BAFJnJ,mBAAA,CAAoB,cAAd,SAAO,sBACbA,mBAAA,CAA2E,QAA3EoJ,WAA2E,EAAAhI,gBAAA,CAAzCP,KAAA,CAAA6H,eAAe,CAACW,cAAc,IAAG,GAAC,gB,GAEtEjJ,YAAA,CAIqBsG,4BAAA;kBAHlB,aAAW,EAAE7F,KAAA,CAAA6H,eAAe,CAACW,cAAc;kBAC5C7I,KAAK,EAAC,SAAS;kBACfmG,MAAM,EAAC;;gBAxe7BtF,CAAA;;cAAAA,CAAA;;YAAAA,CAAA;cA+eYtB,mBAAA,UAAa,EACbK,YAAA,CAyBQsB,gBAAA;YAzBDC,IAAI,EAAC,IAAI;YAACC,EAAE,EAAC;;YAhfhCV,OAAA,EAAAC,QAAA,CAifc,MAuBS,CAvBTf,YAAA,CAuBSyB,iBAAA;cAvBDP,OAAO,EAAC;YAAU;cAjfxCJ,OAAA,EAAAC,QAAA,CAkfgB,MAAiD,CAAjDf,YAAA,CAAiDqD,uBAAA;gBAAnC9D,KAAK,EAAC;cAAS;gBAlf7CuB,OAAA,EAAAC,QAAA,CAkf8C,MAAIH,MAAA,SAAAA,MAAA,QAlflDN,gBAAA,CAkf8C,MAAI,E;gBAlflDW,CAAA;gBAAAG,EAAA;kBAmfgBpB,YAAA,CAoBcyC,sBAAA;gBAvgB9B3B,OAAA,EAAAC,QAAA,CAofkB,MAkBS,CAlBTf,YAAA,CAkBSsD,iBAAA;kBAlBD4F,OAAO,EAAC;gBAAS;kBApf3CpI,OAAA,EAAAC,QAAA,CAsfsB,MAA6C,E,kBAD/CtB,mBAAA,CAgBc2H,SAAA,QArgBlCC,WAAA,CAsf2C5G,KAAA,CAAA0I,uBAAuB,EAArCC,UAAU;yCADnB1G,YAAA,CAgBca,sBAAA;sBAdX/D,GAAG,EAAE4J,UAAU,CAAC7B,EAAE;sBAClB,cAAY,EAAE6B,UAAU,CAAClJ,IAAI;sBAC7BwH,KAAK,EAAE0B,UAAU,CAAC1B,KAAK;sBACvB2B,QAAQ,EAAED,UAAU,CAACE,WAAW;sBAChC3I,OAAK,EAAAE,MAAA,IAAEsC,QAAA,CAAAoG,eAAe,CAACH,UAAU;;sBAEjBI,MAAM,EAAAzI,QAAA,CACrB,MAKS,CALTf,YAAA,CAKSuC,iBAAA;wBAJNnC,KAAK,EAAEgJ,UAAU,CAACK,MAAM,0BAA0BL,UAAU,CAACK,MAAM;wBACpEtJ,IAAI,EAAC;;wBAhgB/BW,OAAA,EAAAC,QAAA,CAkgB0B,MAA2F,CAlgBrHT,gBAAA,CAAAU,gBAAA,CAkgB6BoI,UAAU,CAACK,MAAM,sBAAsBL,UAAU,CAACK,MAAM,8C;wBAlgBrFxI,CAAA;;sBAAAA,CAAA;;;kBAAAA,CAAA;;gBAAAA,CAAA;;cAAAA,CAAA;;YAAAA,CAAA;cA2gBYtB,mBAAA,WAAc,EACdK,YAAA,CA+BQsB,gBAAA;YA/BDC,IAAI,EAAC;UAAI;YA5gB5BT,OAAA,EAAAC,QAAA,CA6gBc,MA6BS,CA7BTf,YAAA,CA6BSyB,iBAAA;cA7BDP,OAAO,EAAC;YAAU;cA7gBxCJ,OAAA,EAAAC,QAAA,CA8gBgB,MAAkD,CAAlDf,YAAA,CAAkDqD,uBAAA;gBAApC9D,KAAK,EAAC;cAAS;gBA9gB7CuB,OAAA,EAAAC,QAAA,CA8gB8C,MAAKH,MAAA,SAAAA,MAAA,QA9gBnDN,gBAAA,CA8gB8C,OAAK,E;gBA9gBnDW,CAAA;gBAAAG,EAAA;kBA+gBgBpB,YAAA,CA0BcyC,sBAAA;gBAziB9B3B,OAAA,EAAAC,QAAA,CAghBkB,MAwBQ,CAxBRf,YAAA,CAwBQqB,gBAAA;kBAxiB1BP,OAAA,EAAAC,QAAA,CAkhBsB,MAAqC,E,kBADvCtB,mBAAA,CAsBQ2H,SAAA,QAviB5BC,WAAA,CAkhByC5G,KAAA,CAAAiJ,iBAAiB,EAA7BC,QAAQ;yCADjBjH,YAAA,CAsBQpB,gBAAA;sBApBL9B,GAAG,EAAEmK,QAAQ,CAACpC,EAAE;sBACjBhG,IAAI,EAAC,IAAI;sBACTC,EAAE,EAAC;;sBArhBzBV,OAAA,EAAAC,QAAA,CAuhBsB,MAeS,CAfTf,YAAA,CAeSyB,iBAAA;wBAdPP,OAAO,EAAC,UAAU;wBAClB3B,KAAK,EAAC,wBAAwB;wBAC7BoB,OAAK,EAAAE,MAAA,IAAEsC,QAAA,CAAAyG,qBAAqB,CAACD,QAAQ;;wBA1hB9D7I,OAAA,EAAAC,QAAA,CA4hBwB,MASc,CATdf,YAAA,CAScyC,sBAAA;0BATDlD,KAAK,EAAC;wBAAa;0BA5hBxDuB,OAAA,EAAAC,QAAA,CA6hB0B,MAKU,CALVf,YAAA,CAKUC,iBAAA;4BAJPC,IAAI,EAAEyJ,QAAQ,CAACzJ,IAAI;4BACpBC,IAAI,EAAC,IAAI;4BACRC,KAAK,EAAEuJ,QAAQ,CAACvJ,KAAK;4BACtBb,KAAK,EAAC;sEAERK,mBAAA,CAAmD,OAAnDiK,WAAmD,EAAA7I,gBAAA,CAAtB2I,QAAQ,CAACvH,IAAI,kBAC1CxC,mBAAA,CAA+E,OAA/EkK,WAA+E,EAAA9I,gBAAA,CAA7B2I,QAAQ,CAACL,WAAW,iB;0BApiBhGrI,CAAA;;wBAAAA,CAAA;;sBAAAA,CAAA;;;kBAAAA,CAAA;;gBAAAA,CAAA;;cAAAA,CAAA;;YAAAA,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;UA+iBQjB,YAAA,CAgBiBiI,yBAAA;QA/jBzBnH,OAAA,EAAAC,QAAA,CAgjBU,MAAqB,CAArBf,YAAA,CAAqB+D,mBAAA,GACrB/D,YAAA,CAMQQ,gBAAA;UALNJ,KAAK,EAAC,eAAe;UACrBc,OAAO,EAAC,MAAM;UACbP,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEJ,KAAA,CAAAU,wBAAwB;;UApjB5CL,OAAA,EAAAC,QAAA,CAqjBW,MAEDH,MAAA,SAAAA,MAAA,QAvjBVN,gBAAA,CAqjBW,MAED,E;UAvjBVW,CAAA;UAAAG,EAAA;YAwjBUpB,YAAA,CAMQQ,gBAAA;UALNJ,KAAK,EAAC,SAAS;UACfc,OAAO,EAAC,MAAM;UACbP,OAAK,EAAEwC,QAAA,CAAA4G;;UA3jBpBjJ,OAAA,EAAAC,QAAA,CA4jBW,MAEDH,MAAA,SAAAA,MAAA,QA9jBVN,gBAAA,CA4jBW,QAED,E;UA9jBVW,CAAA;UAAAG,EAAA;;QAAAH,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;qCAmkBItB,mBAAA,YAAe,EACfK,YAAA,CAcagK,qBAAA;IAllBjB9F,UAAA,EAqkBezD,KAAA,CAAAwJ,YAAY;IArkB3B,uBAAArJ,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAqkBeJ,KAAA,CAAAwJ,YAAY,GAAApJ,MAAA;IACrBT,KAAK,EAAC;;IAIW8J,OAAO,EAAAnJ,QAAA,CACtB,MAKQ,CALRf,YAAA,CAKQQ,gBAAA;MAJNU,OAAO,EAAC,MAAM;MACbP,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEJ,KAAA,CAAAwJ,YAAY;;MA7kB9BnJ,OAAA,EAAAC,QAAA,CA8kBS,MAEDH,MAAA,SAAAA,MAAA,QAhlBRN,gBAAA,CA8kBS,MAED,E;MAhlBRW,CAAA;MAAAG,EAAA;;IAAAN,OAAA,EAAAC,QAAA,CAwkBM,MAAkB,CAxkBxBT,gBAAA,CAAAU,gBAAA,CAwkBSP,KAAA,CAAA0J,YAAY,IAAG,GAElB,gB;IA1kBNlJ,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}