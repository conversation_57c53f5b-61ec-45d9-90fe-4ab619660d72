{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"d-flex align-center justify-space-between mb-4\"\n};\nconst _hoisted_2 = {\n  class: \"text-h5 mb-1\"\n};\nconst _hoisted_3 = {\n  class: \"text-body-1 text-medium-emphasis\"\n};\nconst _hoisted_4 = {\n  class: \"d-flex align-center mb-3\"\n};\nconst _hoisted_5 = {\n  class: \"text-body-1\"\n};\nconst _hoisted_6 = {\n  class: \"d-flex align-center mb-3\"\n};\nconst _hoisted_7 = {\n  class: \"text-body-1\"\n};\nconst _hoisted_8 = {\n  class: \"d-flex align-center mb-3\"\n};\nconst _hoisted_9 = {\n  class: \"text-body-1\"\n};\nconst _hoisted_10 = {\n  class: \"d-flex align-center\"\n};\nconst _hoisted_11 = {\n  class: \"text-body-1\"\n};\nconst _hoisted_12 = {\n  class: \"d-flex align-center justify-space-between\"\n};\nconst _hoisted_13 = {\n  class: \"text-center\"\n};\nconst _hoisted_14 = {\n  class: \"text-h4 font-weight-bold\"\n};\nconst _hoisted_15 = {\n  class: \"text-center\"\n};\nconst _hoisted_16 = {\n  class: \"text-h4 font-weight-bold\"\n};\nconst _hoisted_17 = {\n  class: \"text-center\"\n};\nconst _hoisted_18 = {\n  class: \"text-h4 font-weight-bold\"\n};\nconst _hoisted_19 = {\n  class: \"text-center\"\n};\nconst _hoisted_20 = {\n  class: \"text-h4 font-weight-bold\"\n};\nconst _hoisted_21 = {\n  class: \"mb-4\"\n};\nconst _hoisted_22 = {\n  class: \"d-flex align-center justify-space-between mb-2\"\n};\nconst _hoisted_23 = {\n  class: \"text-body-1 font-weight-bold\"\n};\nconst _hoisted_24 = {\n  class: \"mb-4\"\n};\nconst _hoisted_25 = {\n  class: \"d-flex align-center justify-space-between mb-2\"\n};\nconst _hoisted_26 = {\n  class: \"text-body-1 font-weight-bold\"\n};\nconst _hoisted_27 = {\n  class: \"mb-4\"\n};\nconst _hoisted_28 = {\n  class: \"d-flex align-center justify-space-between mb-2\"\n};\nconst _hoisted_29 = {\n  class: \"text-body-1 font-weight-bold\"\n};\nconst _hoisted_30 = {\n  key: 0\n};\nconst _hoisted_31 = {\n  key: 0,\n  class: \"text-center mt-4\"\n};\nconst _hoisted_32 = {\n  class: \"mb-4\"\n};\nconst _hoisted_33 = {\n  class: \"d-flex justify-space-between mb-2\"\n};\nconst _hoisted_34 = {\n  class: \"font-weight-bold\"\n};\nconst _hoisted_35 = {\n  class: \"mb-4\"\n};\nconst _hoisted_36 = {\n  class: \"d-flex justify-space-between mb-2\"\n};\nconst _hoisted_37 = {\n  class: \"font-weight-bold\"\n};\nconst _hoisted_38 = {\n  class: \"mb-4\"\n};\nconst _hoisted_39 = {\n  class: \"d-flex justify-space-between mb-2\"\n};\nconst _hoisted_40 = {\n  class: \"font-weight-bold\"\n};\nconst _hoisted_41 = {\n  class: \"text-h6 mb-1\"\n};\nconst _hoisted_42 = {\n  class: \"text-caption text-medium-emphasis\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_v_btn = _resolveComponent(\"v-btn\");\n  const _component_v_col = _resolveComponent(\"v-col\");\n  const _component_v_row = _resolveComponent(\"v-row\");\n  const _component_v_img = _resolveComponent(\"v-img\");\n  const _component_v_avatar = _resolveComponent(\"v-avatar\");\n  const _component_v_divider = _resolveComponent(\"v-divider\");\n  const _component_v_card_text = _resolveComponent(\"v-card-text\");\n  const _component_v_card = _resolveComponent(\"v-card\");\n  const _component_v_card_title = _resolveComponent(\"v-card-title\");\n  const _component_v_icon = _resolveComponent(\"v-icon\");\n  const _component_v_text_field = _resolveComponent(\"v-text-field\");\n  const _component_v_select = _resolveComponent(\"v-select\");\n  const _component_v_textarea = _resolveComponent(\"v-textarea\");\n  const _component_v_form = _resolveComponent(\"v-form\");\n  const _component_v_progress_linear = _resolveComponent(\"v-progress-linear\");\n  const _component_v_chip = _resolveComponent(\"v-chip\");\n  const _component_v_file_input = _resolveComponent(\"v-file-input\");\n  const _component_v_spacer = _resolveComponent(\"v-spacer\");\n  const _component_v_card_actions = _resolveComponent(\"v-card-actions\");\n  const _component_v_dialog = _resolveComponent(\"v-dialog\");\n  const _component_v_list_item = _resolveComponent(\"v-list-item\");\n  const _component_v_list = _resolveComponent(\"v-list\");\n  const _component_v_snackbar = _resolveComponent(\"v-snackbar\");\n  const _component_v_container = _resolveComponent(\"v-container\");\n  return _openBlock(), _createBlock(_component_v_container, null, {\n    default: _withCtx(() => [_createVNode(_component_v_row, null, {\n      default: _withCtx(() => [_createVNode(_component_v_col, {\n        cols: \"12\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_1, [_cache[19] || (_cache[19] = _createElementVNode(\"div\", null, [_createElementVNode(\"h1\", {\n          class: \"text-h4\"\n        }, \"个人资料\"), _createElementVNode(\"p\", {\n          class: \"text-subtitle-1 text-medium-emphasis\"\n        }, \" 查看和编辑您的个人信息 \")], -1 /* HOISTED */)), _createElementVNode(\"div\", null, [_createVNode(_component_v_btn, {\n          color: \"primary\",\n          \"prepend-icon\": \"mdi-pencil\",\n          onClick: _cache[0] || (_cache[0] = $event => $data.editMode = !$data.editMode)\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($data.editMode ? '取消编辑' : '编辑资料'), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        })])])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_v_row, null, {\n      default: _withCtx(() => [_createVNode(_component_v_col, {\n        cols: \"12\",\n        md: \"4\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_v_card, {\n          class: \"mb-4\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_card_text, {\n            class: \"text-center\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_avatar, {\n              size: \"150\",\n              class: \"mb-4\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_v_img, {\n                src: $data.user.avatar,\n                alt: \"用户头像\"\n              }, null, 8 /* PROPS */, [\"src\"])]),\n              _: 1 /* STABLE */\n            }), _createElementVNode(\"h2\", _hoisted_2, _toDisplayString($data.user.name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_3, _toDisplayString($data.user.position), 1 /* TEXT */), _createVNode(_component_v_divider, {\n              class: \"my-4\"\n            }), $data.editMode ? (_openBlock(), _createBlock(_component_v_btn, {\n              key: 0,\n              color: \"primary\",\n              variant: \"text\",\n              block: \"\",\n              \"prepend-icon\": \"mdi-camera\",\n              onClick: _cache[1] || (_cache[1] = $event => $data.showUploadDialog = true)\n            }, {\n              default: _withCtx(() => _cache[20] || (_cache[20] = [_createTextVNode(\" 更换头像 \")])),\n              _: 1 /* STABLE */,\n              __: [20]\n            })) : _createCommentVNode(\"v-if\", true)]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_v_card, null, {\n          default: _withCtx(() => [_createVNode(_component_v_card_title, null, {\n            default: _withCtx(() => _cache[21] || (_cache[21] = [_createTextVNode(\"联系方式\")])),\n            _: 1 /* STABLE */,\n            __: [21]\n          }), _createVNode(_component_v_card_text, null, {\n            default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_v_icon, {\n              icon: \"mdi-email-outline\",\n              class: \"me-3\"\n            }), _createElementVNode(\"div\", null, [_cache[22] || (_cache[22] = _createElementVNode(\"div\", {\n              class: \"text-caption text-medium-emphasis\"\n            }, \"邮箱\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_5, _toDisplayString($data.user.email), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_v_icon, {\n              icon: \"mdi-phone-outline\",\n              class: \"me-3\"\n            }), _createElementVNode(\"div\", null, [_cache[23] || (_cache[23] = _createElementVNode(\"div\", {\n              class: \"text-caption text-medium-emphasis\"\n            }, \"手机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_7, _toDisplayString($data.user.phone), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_v_icon, {\n              icon: \"mdi-office-building-outline\",\n              class: \"me-3\"\n            }), _createElementVNode(\"div\", null, [_cache[24] || (_cache[24] = _createElementVNode(\"div\", {\n              class: \"text-caption text-medium-emphasis\"\n            }, \"部门\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_9, _toDisplayString($data.user.department), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_v_icon, {\n              icon: \"mdi-map-marker-outline\",\n              class: \"me-3\"\n            }), _createElementVNode(\"div\", null, [_cache[25] || (_cache[25] = _createElementVNode(\"div\", {\n              class: \"text-caption text-medium-emphasis\"\n            }, \"地址\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_11, _toDisplayString($data.user.address), 1 /* TEXT */)])])]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_v_col, {\n        cols: \"12\",\n        md: \"8\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_v_card, {\n          class: \"mb-4\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_card_title, null, {\n            default: _withCtx(() => _cache[26] || (_cache[26] = [_createTextVNode(\"个人资料\")])),\n            _: 1 /* STABLE */,\n            __: [26]\n          }), _createVNode(_component_v_card_text, null, {\n            default: _withCtx(() => [_createVNode(_component_v_form, {\n              ref: \"form\",\n              modelValue: $data.valid,\n              \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $data.valid = $event)\n            }, {\n              default: _withCtx(() => [_createVNode(_component_v_row, null, {\n                default: _withCtx(() => [_createVNode(_component_v_col, {\n                  cols: \"12\",\n                  md: \"6\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_v_text_field, {\n                    modelValue: $data.user.name,\n                    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $data.user.name = $event),\n                    label: \"姓名\",\n                    rules: $data.nameRules,\n                    variant: \"outlined\",\n                    readonly: !$data.editMode,\n                    required: \"\"\n                  }, null, 8 /* PROPS */, [\"modelValue\", \"rules\", \"readonly\"])]),\n                  _: 1 /* STABLE */\n                }), _createVNode(_component_v_col, {\n                  cols: \"12\",\n                  md: \"6\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_v_text_field, {\n                    modelValue: $data.user.email,\n                    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $data.user.email = $event),\n                    label: \"邮箱\",\n                    rules: $data.emailRules,\n                    variant: \"outlined\",\n                    readonly: \"\",\n                    disabled: \"\"\n                  }, null, 8 /* PROPS */, [\"modelValue\", \"rules\"])]),\n                  _: 1 /* STABLE */\n                }), _createVNode(_component_v_col, {\n                  cols: \"12\",\n                  md: \"6\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_v_text_field, {\n                    modelValue: $data.user.phone,\n                    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $data.user.phone = $event),\n                    label: \"手机号码\",\n                    rules: $data.phoneRules,\n                    variant: \"outlined\",\n                    readonly: !$data.editMode\n                  }, null, 8 /* PROPS */, [\"modelValue\", \"rules\", \"readonly\"])]),\n                  _: 1 /* STABLE */\n                }), _createVNode(_component_v_col, {\n                  cols: \"12\",\n                  md: \"6\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_v_select, {\n                    modelValue: $data.user.department,\n                    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.user.department = $event),\n                    items: $data.departments,\n                    label: \"部门\",\n                    variant: \"outlined\",\n                    readonly: !$data.editMode\n                  }, null, 8 /* PROPS */, [\"modelValue\", \"items\", \"readonly\"])]),\n                  _: 1 /* STABLE */\n                }), _createVNode(_component_v_col, {\n                  cols: \"12\",\n                  md: \"6\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_v_text_field, {\n                    modelValue: $data.user.position,\n                    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.user.position = $event),\n                    label: \"职位\",\n                    variant: \"outlined\",\n                    readonly: !$data.editMode\n                  }, null, 8 /* PROPS */, [\"modelValue\", \"readonly\"])]),\n                  _: 1 /* STABLE */\n                }), _createVNode(_component_v_col, {\n                  cols: \"12\",\n                  md: \"6\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_v_text_field, {\n                    modelValue: $data.user.joinDate,\n                    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.user.joinDate = $event),\n                    label: \"入职日期\",\n                    variant: \"outlined\",\n                    readonly: \"\",\n                    disabled: \"\"\n                  }, null, 8 /* PROPS */, [\"modelValue\"])]),\n                  _: 1 /* STABLE */\n                }), _createVNode(_component_v_col, {\n                  cols: \"12\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_v_text_field, {\n                    modelValue: $data.user.address,\n                    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.user.address = $event),\n                    label: \"地址\",\n                    variant: \"outlined\",\n                    readonly: !$data.editMode\n                  }, null, 8 /* PROPS */, [\"modelValue\", \"readonly\"])]),\n                  _: 1 /* STABLE */\n                }), _createVNode(_component_v_col, {\n                  cols: \"12\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_v_textarea, {\n                    modelValue: $data.user.bio,\n                    \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.user.bio = $event),\n                    label: \"个人简介\",\n                    variant: \"outlined\",\n                    readonly: !$data.editMode,\n                    \"auto-grow\": \"\",\n                    rows: \"3\"\n                  }, null, 8 /* PROPS */, [\"modelValue\", \"readonly\"])]),\n                  _: 1 /* STABLE */\n                })]),\n                _: 1 /* STABLE */\n              }), $data.editMode ? (_openBlock(), _createBlock(_component_v_row, {\n                key: 0\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_col, {\n                  cols: \"12\",\n                  class: \"d-flex justify-end\"\n                }, {\n                  default: _withCtx(() => [_createVNode(_component_v_btn, {\n                    color: \"primary\",\n                    disabled: !$data.valid,\n                    onClick: $options.saveProfile\n                  }, {\n                    default: _withCtx(() => _cache[27] || (_cache[27] = [_createTextVNode(\" 保存资料 \")])),\n                    _: 1 /* STABLE */,\n                    __: [27]\n                  }, 8 /* PROPS */, [\"disabled\", \"onClick\"])]),\n                  _: 1 /* STABLE */\n                })]),\n                _: 1 /* STABLE */\n              })) : _createCommentVNode(\"v-if\", true)]),\n              _: 1 /* STABLE */\n            }, 8 /* PROPS */, [\"modelValue\"])]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_v_card, null, {\n          default: _withCtx(() => [_createVNode(_component_v_card_title, null, {\n            default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_12, [_cache[29] || (_cache[29] = _createElementVNode(\"span\", null, \"工作统计\", -1 /* HOISTED */)), _createVNode(_component_v_btn, {\n              color: \"primary\",\n              variant: \"text\",\n              size: \"small\",\n              \"prepend-icon\": \"mdi-chart-line\",\n              onClick: _cache[11] || (_cache[11] = $event => $data.showWorkflowOptimization = true)\n            }, {\n              default: _withCtx(() => _cache[28] || (_cache[28] = [_createTextVNode(\" 工作流优化 \")])),\n              _: 1 /* STABLE */,\n              __: [28]\n            })])]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_v_card_text, null, {\n            default: _withCtx(() => [_createVNode(_component_v_row, null, {\n              default: _withCtx(() => [_createVNode(_component_v_col, {\n                cols: \"6\",\n                md: \"3\"\n              }, {\n                default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, _toDisplayString($data.stats.projects), 1 /* TEXT */), _cache[30] || (_cache[30] = _createElementVNode(\"div\", {\n                  class: \"text-caption text-medium-emphasis\"\n                }, \"参与项目\", -1 /* HOISTED */))])]),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_v_col, {\n                cols: \"6\",\n                md: \"3\"\n              }, {\n                default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, _toDisplayString($data.stats.tasks), 1 /* TEXT */), _cache[31] || (_cache[31] = _createElementVNode(\"div\", {\n                  class: \"text-caption text-medium-emphasis\"\n                }, \"完成任务\", -1 /* HOISTED */))])]),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_v_col, {\n                cols: \"6\",\n                md: \"3\"\n              }, {\n                default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, _toDisplayString($data.stats.meetings), 1 /* TEXT */), _cache[32] || (_cache[32] = _createElementVNode(\"div\", {\n                  class: \"text-caption text-medium-emphasis\"\n                }, \"参加会议\", -1 /* HOISTED */))])]),\n                _: 1 /* STABLE */\n              }), _createVNode(_component_v_col, {\n                cols: \"6\",\n                md: \"3\"\n              }, {\n                default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, _toDisplayString($data.stats.documents), 1 /* TEXT */), _cache[33] || (_cache[33] = _createElementVNode(\"div\", {\n                  class: \"text-caption text-medium-emphasis\"\n                }, \"提交文档\", -1 /* HOISTED */))])]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_v_divider, {\n              class: \"my-4\"\n            }), _createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_cache[34] || (_cache[34] = _createElementVNode(\"div\", {\n              class: \"text-body-1\"\n            }, \"任务完成率\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_23, _toDisplayString($data.stats.taskCompletionRate) + \"%\", 1 /* TEXT */)]), _createVNode(_component_v_progress_linear, {\n              \"model-value\": $data.stats.taskCompletionRate,\n              color: \"success\",\n              height: \"8\",\n              rounded: \"\"\n            }, null, 8 /* PROPS */, [\"model-value\"])]), _createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"div\", _hoisted_25, [_cache[35] || (_cache[35] = _createElementVNode(\"div\", {\n              class: \"text-body-1\"\n            }, \"按时完成率\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_26, _toDisplayString($data.stats.onTimeRate) + \"%\", 1 /* TEXT */)]), _createVNode(_component_v_progress_linear, {\n              \"model-value\": $data.stats.onTimeRate,\n              color: \"info\",\n              height: \"8\",\n              rounded: \"\"\n            }, null, 8 /* PROPS */, [\"model-value\"])]), _createCommentVNode(\" 工作流效率指标 \"), _createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"div\", _hoisted_28, [_cache[36] || (_cache[36] = _createElementVNode(\"div\", {\n              class: \"text-body-1\"\n            }, \"工作流效率\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_29, _toDisplayString($options.workflowEfficiency) + \"%\", 1 /* TEXT */)]), _createVNode(_component_v_progress_linear, {\n              \"model-value\": $options.workflowEfficiency,\n              color: $options.getEfficiencyColor($options.workflowEfficiency),\n              height: \"8\",\n              rounded: \"\"\n            }, null, 8 /* PROPS */, [\"model-value\", \"color\"])]), _createCommentVNode(\" 待处理事项 \"), $data.pendingItems.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_30, [_createVNode(_component_v_divider, {\n              class: \"my-4\"\n            }), _cache[37] || (_cache[37] = _createElementVNode(\"div\", {\n              class: \"text-subtitle-2 mb-2\"\n            }, \"待处理事项\", -1 /* HOISTED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.pendingItems, item => {\n              return _openBlock(), _createBlock(_component_v_chip, {\n                key: item.id,\n                color: item.priority === 'high' ? 'error' : item.priority === 'medium' ? 'warning' : 'info',\n                size: \"small\",\n                class: \"me-2 mb-2\",\n                onClick: $event => $options.handlePendingItem(item)\n              }, {\n                default: _withCtx(() => [_createTextVNode(_toDisplayString(item.title), 1 /* TEXT */)]),\n                _: 2 /* DYNAMIC */\n              }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"color\", \"onClick\"]);\n            }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true)]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 上传头像对话框 \"), _createVNode(_component_v_dialog, {\n      modelValue: $data.showUploadDialog,\n      \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $data.showUploadDialog = $event),\n      \"max-width\": \"500\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_v_card, null, {\n        default: _withCtx(() => [_createVNode(_component_v_card_title, null, {\n          default: _withCtx(() => _cache[38] || (_cache[38] = [_createTextVNode(\"上传头像\")])),\n          _: 1 /* STABLE */,\n          __: [38]\n        }), _createVNode(_component_v_card_text, null, {\n          default: _withCtx(() => [_createVNode(_component_v_file_input, {\n            modelValue: $data.avatarFile,\n            \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $data.avatarFile = $event),\n            label: \"选择图片\",\n            accept: \"image/*\",\n            \"show-size\": \"\",\n            \"truncate-length\": \"15\",\n            variant: \"outlined\"\n          }, null, 8 /* PROPS */, [\"modelValue\"]), $data.avatarPreview ? (_openBlock(), _createElementBlock(\"div\", _hoisted_31, [_createVNode(_component_v_avatar, {\n            size: \"150\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_v_img, {\n              src: $data.avatarPreview,\n              alt: \"Avatar Preview\"\n            }, null, 8 /* PROPS */, [\"src\"])]),\n            _: 1 /* STABLE */\n          })])) : _createCommentVNode(\"v-if\", true)]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_v_card_actions, null, {\n          default: _withCtx(() => [_createVNode(_component_v_spacer), _createVNode(_component_v_btn, {\n            color: \"grey-darken-1\",\n            variant: \"text\",\n            onClick: _cache[13] || (_cache[13] = $event => $data.showUploadDialog = false)\n          }, {\n            default: _withCtx(() => _cache[39] || (_cache[39] = [_createTextVNode(\" 取消 \")])),\n            _: 1 /* STABLE */,\n            __: [39]\n          }), _createVNode(_component_v_btn, {\n            color: \"primary\",\n            variant: \"text\",\n            disabled: !$data.avatarFile,\n            onClick: $options.uploadAvatar\n          }, {\n            default: _withCtx(() => _cache[40] || (_cache[40] = [_createTextVNode(\" 上传 \")])),\n            _: 1 /* STABLE */,\n            __: [40]\n          }, 8 /* PROPS */, [\"disabled\", \"onClick\"])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 工作流优化对话框 \"), _createVNode(_component_v_dialog, {\n      modelValue: $data.showWorkflowOptimization,\n      \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $data.showWorkflowOptimization = $event),\n      \"max-width\": \"1200\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_v_card, null, {\n        default: _withCtx(() => [_createVNode(_component_v_card_title, {\n          class: \"d-flex align-center\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_icon, {\n            icon: \"mdi-chart-line\",\n            class: \"me-2\"\n          }), _cache[41] || (_cache[41] = _createTextVNode(\" 工作流优化分析 \"))]),\n          _: 1 /* STABLE */,\n          __: [41]\n        }), _createVNode(_component_v_card_text, null, {\n          default: _withCtx(() => [_createVNode(_component_v_row, null, {\n            default: _withCtx(() => [_createCommentVNode(\" 工作流效率分析 \"), _createVNode(_component_v_col, {\n              cols: \"12\",\n              md: \"6\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_v_card, {\n                variant: \"outlined\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_card_title, {\n                  class: \"text-h6\"\n                }, {\n                  default: _withCtx(() => _cache[42] || (_cache[42] = [_createTextVNode(\"效率分析\")])),\n                  _: 1 /* STABLE */,\n                  __: [42]\n                }), _createVNode(_component_v_card_text, null, {\n                  default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, [_cache[43] || (_cache[43] = _createElementVNode(\"span\", null, \"任务处理速度\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_34, _toDisplayString($data.workflowMetrics.taskProcessingSpeed) + \"个/天\", 1 /* TEXT */)]), _createVNode(_component_v_progress_linear, {\n                    \"model-value\": $data.workflowMetrics.taskProcessingSpeed * 10,\n                    color: \"primary\",\n                    height: \"6\"\n                  }, null, 8 /* PROPS */, [\"model-value\"])]), _createElementVNode(\"div\", _hoisted_35, [_createElementVNode(\"div\", _hoisted_36, [_cache[44] || (_cache[44] = _createElementVNode(\"span\", null, \"平均响应时间\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_37, _toDisplayString($data.workflowMetrics.avgResponseTime) + \"小时\", 1 /* TEXT */)]), _createVNode(_component_v_progress_linear, {\n                    \"model-value\": Math.max(0, 100 - $data.workflowMetrics.avgResponseTime * 5),\n                    color: \"info\",\n                    height: \"6\"\n                  }, null, 8 /* PROPS */, [\"model-value\"])]), _createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"div\", _hoisted_39, [_cache[45] || (_cache[45] = _createElementVNode(\"span\", null, \"工作流自动化率\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_40, _toDisplayString($data.workflowMetrics.automationRate) + \"%\", 1 /* TEXT */)]), _createVNode(_component_v_progress_linear, {\n                    \"model-value\": $data.workflowMetrics.automationRate,\n                    color: \"success\",\n                    height: \"6\"\n                  }, null, 8 /* PROPS */, [\"model-value\"])])]),\n                  _: 1 /* STABLE */\n                })]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            }), _createCommentVNode(\" 优化建议 \"), _createVNode(_component_v_col, {\n              cols: \"12\",\n              md: \"6\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_v_card, {\n                variant: \"outlined\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_card_title, {\n                  class: \"text-h6\"\n                }, {\n                  default: _withCtx(() => _cache[46] || (_cache[46] = [_createTextVNode(\"优化建议\")])),\n                  _: 1 /* STABLE */,\n                  __: [46]\n                }), _createVNode(_component_v_card_text, null, {\n                  default: _withCtx(() => [_createVNode(_component_v_list, {\n                    density: \"compact\"\n                  }, {\n                    default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.optimizationSuggestions, suggestion => {\n                      return _openBlock(), _createBlock(_component_v_list_item, {\n                        key: suggestion.id,\n                        \"prepend-icon\": suggestion.icon,\n                        title: suggestion.title,\n                        subtitle: suggestion.description,\n                        onClick: $event => $options.applySuggestion(suggestion)\n                      }, {\n                        append: _withCtx(() => [_createVNode(_component_v_chip, {\n                          color: suggestion.impact === 'high' ? 'success' : suggestion.impact === 'medium' ? 'warning' : 'info',\n                          size: \"small\"\n                        }, {\n                          default: _withCtx(() => [_createTextVNode(_toDisplayString(suggestion.impact === 'high' ? '高效果' : suggestion.impact === 'medium' ? '中效果' : '低效果'), 1 /* TEXT */)]),\n                          _: 2 /* DYNAMIC */\n                        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"color\"])]),\n                        _: 2 /* DYNAMIC */\n                      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"prepend-icon\", \"title\", \"subtitle\", \"onClick\"]);\n                    }), 128 /* KEYED_FRAGMENT */))]),\n                    _: 1 /* STABLE */\n                  })]),\n                  _: 1 /* STABLE */\n                })]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            }), _createCommentVNode(\" 工作流模板 \"), _createVNode(_component_v_col, {\n              cols: \"12\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_v_card, {\n                variant: \"outlined\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_v_card_title, {\n                  class: \"text-h6\"\n                }, {\n                  default: _withCtx(() => _cache[47] || (_cache[47] = [_createTextVNode(\"工作流模板\")])),\n                  _: 1 /* STABLE */,\n                  __: [47]\n                }), _createVNode(_component_v_card_text, null, {\n                  default: _withCtx(() => [_createVNode(_component_v_row, null, {\n                    default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.workflowTemplates, template => {\n                      return _openBlock(), _createBlock(_component_v_col, {\n                        key: template.id,\n                        cols: \"12\",\n                        md: \"4\"\n                      }, {\n                        default: _withCtx(() => [_createVNode(_component_v_card, {\n                          variant: \"outlined\",\n                          class: \"workflow-template-card\",\n                          onClick: $event => $options.applyWorkflowTemplate(template)\n                        }, {\n                          default: _withCtx(() => [_createVNode(_component_v_card_text, {\n                            class: \"text-center\"\n                          }, {\n                            default: _withCtx(() => [_createVNode(_component_v_icon, {\n                              icon: template.icon,\n                              size: \"48\",\n                              color: template.color,\n                              class: \"mb-2\"\n                            }, null, 8 /* PROPS */, [\"icon\", \"color\"]), _createElementVNode(\"div\", _hoisted_41, _toDisplayString(template.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_42, _toDisplayString(template.description), 1 /* TEXT */)]),\n                            _: 2 /* DYNAMIC */\n                          }, 1024 /* DYNAMIC_SLOTS */)]),\n                          _: 2 /* DYNAMIC */\n                        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n                        _: 2 /* DYNAMIC */\n                      }, 1024 /* DYNAMIC_SLOTS */);\n                    }), 128 /* KEYED_FRAGMENT */))]),\n                    _: 1 /* STABLE */\n                  })]),\n                  _: 1 /* STABLE */\n                })]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_v_card_actions, null, {\n          default: _withCtx(() => [_createVNode(_component_v_spacer), _createVNode(_component_v_btn, {\n            color: \"grey-darken-1\",\n            variant: \"text\",\n            onClick: _cache[15] || (_cache[15] = $event => $data.showWorkflowOptimization = false)\n          }, {\n            default: _withCtx(() => _cache[48] || (_cache[48] = [_createTextVNode(\" 关闭 \")])),\n            _: 1 /* STABLE */,\n            __: [48]\n          }), _createVNode(_component_v_btn, {\n            color: \"primary\",\n            variant: \"text\",\n            onClick: $options.exportWorkflowReport\n          }, {\n            default: _withCtx(() => _cache[49] || (_cache[49] = [_createTextVNode(\" 导出报告 \")])),\n            _: 1 /* STABLE */,\n            __: [49]\n          }, 8 /* PROPS */, [\"onClick\"])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 操作成功提示 \"), _createVNode(_component_v_snackbar, {\n      modelValue: $data.showSnackbar,\n      \"onUpdate:modelValue\": _cache[18] || (_cache[18] = $event => $data.showSnackbar = $event),\n      color: \"success\"\n    }, {\n      actions: _withCtx(() => [_createVNode(_component_v_btn, {\n        variant: \"text\",\n        onClick: _cache[17] || (_cache[17] = $event => $data.showSnackbar = false)\n      }, {\n        default: _withCtx(() => _cache[50] || (_cache[50] = [_createTextVNode(\" 关闭 \")])),\n        _: 1 /* STABLE */,\n        __: [50]\n      })]),\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($data.snackbarText) + \" \", 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])]),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["class", "key", "_createBlock", "_component_v_container", "default", "_withCtx", "_createVNode", "_component_v_row", "_component_v_col", "cols", "_createElementVNode", "_hoisted_1", "_component_v_btn", "color", "onClick", "_cache", "$event", "$data", "editMode", "_createTextVNode", "_toDisplayString", "_", "md", "_component_v_card", "_component_v_card_text", "_component_v_avatar", "size", "_component_v_img", "src", "user", "avatar", "alt", "_hoisted_2", "name", "_hoisted_3", "position", "_component_v_divider", "variant", "block", "showUploadDialog", "__", "_createCommentVNode", "_component_v_card_title", "_hoisted_4", "_component_v_icon", "icon", "_hoisted_5", "email", "_hoisted_6", "_hoisted_7", "phone", "_hoisted_8", "_hoisted_9", "department", "_hoisted_10", "_hoisted_11", "address", "_component_v_form", "ref", "modelValue", "valid", "_component_v_text_field", "label", "rules", "nameRules", "readonly", "required", "emailRules", "disabled", "phoneRules", "_component_v_select", "items", "departments", "joinDate", "_component_v_textarea", "bio", "rows", "$options", "saveProfile", "_hoisted_12", "showWorkflowOptimization", "_hoisted_13", "_hoisted_14", "stats", "projects", "_hoisted_15", "_hoisted_16", "tasks", "_hoisted_17", "_hoisted_18", "meetings", "_hoisted_19", "_hoisted_20", "documents", "_hoisted_21", "_hoisted_22", "_hoisted_23", "taskCompletionRate", "_component_v_progress_linear", "height", "rounded", "_hoisted_24", "_hoisted_25", "_hoisted_26", "onTimeRate", "_hoisted_27", "_hoisted_28", "_hoisted_29", "workflowEfficiency", "getEfficiencyColor", "pendingItems", "length", "_createElementBlock", "_hoisted_30", "_Fragment", "_renderList", "item", "_component_v_chip", "id", "priority", "handlePendingItem", "title", "_component_v_dialog", "_component_v_file_input", "avatar<PERSON>ile", "accept", "avatarPreview", "_hoisted_31", "_component_v_card_actions", "_component_v_spacer", "uploadAvatar", "_hoisted_32", "_hoisted_33", "_hoisted_34", "workflowMetrics", "taskProcessingSpeed", "_hoisted_35", "_hoisted_36", "_hoisted_37", "avgResponseTime", "Math", "max", "_hoisted_38", "_hoisted_39", "_hoisted_40", "automationRate", "_component_v_list", "density", "optimizationSuggestions", "suggestion", "_component_v_list_item", "subtitle", "description", "applySuggestion", "append", "impact", "workflowTemplates", "template", "applyWorkflowTemplate", "_hoisted_41", "_hoisted_42", "exportWorkflowReport", "_component_v_snackbar", "showSnackbar", "actions", "snackbarText"], "sources": ["/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/技术测试/项目实施进度管理/project-management/frontend/src/views/Profile.vue"], "sourcesContent": ["<template>\n  <v-container>\n    <v-row>\n      <v-col cols=\"12\">\n        <div class=\"d-flex align-center justify-space-between mb-4\">\n          <div>\n            <h1 class=\"text-h4\">个人资料</h1>\n            <p class=\"text-subtitle-1 text-medium-emphasis\">\n              查看和编辑您的个人信息\n            </p>\n          </div>\n          <div>\n            <v-btn\n              color=\"primary\"\n              prepend-icon=\"mdi-pencil\"\n              @click=\"editMode = !editMode\"\n            >\n              {{ editMode ? '取消编辑' : '编辑资料' }}\n            </v-btn>\n          </div>\n        </div>\n      </v-col>\n    </v-row>\n\n    <v-row>\n      <v-col cols=\"12\" md=\"4\">\n        <v-card class=\"mb-4\">\n          <v-card-text class=\"text-center\">\n            <v-avatar size=\"150\" class=\"mb-4\">\n              <v-img :src=\"user.avatar\" alt=\"用户头像\"></v-img>\n            </v-avatar>\n\n            <h2 class=\"text-h5 mb-1\">{{ user.name }}</h2>\n            <p class=\"text-body-1 text-medium-emphasis\">{{ user.position }}</p>\n\n            <v-divider class=\"my-4\"></v-divider>\n\n            <v-btn\n              v-if=\"editMode\"\n              color=\"primary\"\n              variant=\"text\"\n              block\n              prepend-icon=\"mdi-camera\"\n              @click=\"showUploadDialog = true\"\n            >\n              更换头像\n            </v-btn>\n          </v-card-text>\n        </v-card>\n\n        <v-card>\n          <v-card-title>联系方式</v-card-title>\n          <v-card-text>\n            <div class=\"d-flex align-center mb-3\">\n              <v-icon icon=\"mdi-email-outline\" class=\"me-3\"></v-icon>\n              <div>\n                <div class=\"text-caption text-medium-emphasis\">邮箱</div>\n                <div class=\"text-body-1\">{{ user.email }}</div>\n              </div>\n            </div>\n\n            <div class=\"d-flex align-center mb-3\">\n              <v-icon icon=\"mdi-phone-outline\" class=\"me-3\"></v-icon>\n              <div>\n                <div class=\"text-caption text-medium-emphasis\">手机</div>\n                <div class=\"text-body-1\">{{ user.phone }}</div>\n              </div>\n            </div>\n\n            <div class=\"d-flex align-center mb-3\">\n              <v-icon icon=\"mdi-office-building-outline\" class=\"me-3\"></v-icon>\n              <div>\n                <div class=\"text-caption text-medium-emphasis\">部门</div>\n                <div class=\"text-body-1\">{{ user.department }}</div>\n              </div>\n            </div>\n\n            <div class=\"d-flex align-center\">\n              <v-icon icon=\"mdi-map-marker-outline\" class=\"me-3\"></v-icon>\n              <div>\n                <div class=\"text-caption text-medium-emphasis\">地址</div>\n                <div class=\"text-body-1\">{{ user.address }}</div>\n              </div>\n            </div>\n          </v-card-text>\n        </v-card>\n      </v-col>\n\n      <v-col cols=\"12\" md=\"8\">\n        <v-card class=\"mb-4\">\n          <v-card-title>个人资料</v-card-title>\n          <v-card-text>\n            <v-form ref=\"form\" v-model=\"valid\">\n              <v-row>\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.name\"\n                    label=\"姓名\"\n                    :rules=\"nameRules\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                    required\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.email\"\n                    label=\"邮箱\"\n                    :rules=\"emailRules\"\n                    variant=\"outlined\"\n                    readonly\n                    disabled\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.phone\"\n                    label=\"手机号码\"\n                    :rules=\"phoneRules\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-select\n                    v-model=\"user.department\"\n                    :items=\"departments\"\n                    label=\"部门\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                  ></v-select>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.position\"\n                    label=\"职位\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\" md=\"6\">\n                  <v-text-field\n                    v-model=\"user.joinDate\"\n                    label=\"入职日期\"\n                    variant=\"outlined\"\n                    readonly\n                    disabled\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\">\n                  <v-text-field\n                    v-model=\"user.address\"\n                    label=\"地址\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                  ></v-text-field>\n                </v-col>\n\n                <v-col cols=\"12\">\n                  <v-textarea\n                    v-model=\"user.bio\"\n                    label=\"个人简介\"\n                    variant=\"outlined\"\n                    :readonly=\"!editMode\"\n                    auto-grow\n                    rows=\"3\"\n                  ></v-textarea>\n                </v-col>\n              </v-row>\n\n              <v-row v-if=\"editMode\">\n                <v-col cols=\"12\" class=\"d-flex justify-end\">\n                  <v-btn\n                    color=\"primary\"\n                    :disabled=\"!valid\"\n                    @click=\"saveProfile\"\n                  >\n                    保存资料\n                  </v-btn>\n                </v-col>\n              </v-row>\n            </v-form>\n          </v-card-text>\n        </v-card>\n\n        <v-card>\n          <v-card-title>\n            <div class=\"d-flex align-center justify-space-between\">\n              <span>工作统计</span>\n              <v-btn\n                color=\"primary\"\n                variant=\"text\"\n                size=\"small\"\n                prepend-icon=\"mdi-chart-line\"\n                @click=\"showWorkflowOptimization = true\"\n              >\n                工作流优化\n              </v-btn>\n            </div>\n          </v-card-title>\n          <v-card-text>\n            <v-row>\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.projects }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">参与项目</div>\n                </div>\n              </v-col>\n\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.tasks }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">完成任务</div>\n                </div>\n              </v-col>\n\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.meetings }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">参加会议</div>\n                </div>\n              </v-col>\n\n              <v-col cols=\"6\" md=\"3\">\n                <div class=\"text-center\">\n                  <div class=\"text-h4 font-weight-bold\">{{ stats.documents }}</div>\n                  <div class=\"text-caption text-medium-emphasis\">提交文档</div>\n                </div>\n              </v-col>\n            </v-row>\n\n            <v-divider class=\"my-4\"></v-divider>\n\n            <div class=\"mb-4\">\n              <div class=\"d-flex align-center justify-space-between mb-2\">\n                <div class=\"text-body-1\">任务完成率</div>\n                <div class=\"text-body-1 font-weight-bold\">{{ stats.taskCompletionRate }}%</div>\n              </div>\n              <v-progress-linear\n                :model-value=\"stats.taskCompletionRate\"\n                color=\"success\"\n                height=\"8\"\n                rounded\n              ></v-progress-linear>\n            </div>\n\n            <div class=\"mb-4\">\n              <div class=\"d-flex align-center justify-space-between mb-2\">\n                <div class=\"text-body-1\">按时完成率</div>\n                <div class=\"text-body-1 font-weight-bold\">{{ stats.onTimeRate }}%</div>\n              </div>\n              <v-progress-linear\n                :model-value=\"stats.onTimeRate\"\n                color=\"info\"\n                height=\"8\"\n                rounded\n              ></v-progress-linear>\n            </div>\n\n            <!-- 工作流效率指标 -->\n            <div class=\"mb-4\">\n              <div class=\"d-flex align-center justify-space-between mb-2\">\n                <div class=\"text-body-1\">工作流效率</div>\n                <div class=\"text-body-1 font-weight-bold\">{{ workflowEfficiency }}%</div>\n              </div>\n              <v-progress-linear\n                :model-value=\"workflowEfficiency\"\n                :color=\"getEfficiencyColor(workflowEfficiency)\"\n                height=\"8\"\n                rounded\n              ></v-progress-linear>\n            </div>\n\n            <!-- 待处理事项 -->\n            <div v-if=\"pendingItems.length > 0\">\n              <v-divider class=\"my-4\"></v-divider>\n              <div class=\"text-subtitle-2 mb-2\">待处理事项</div>\n              <v-chip\n                v-for=\"item in pendingItems\"\n                :key=\"item.id\"\n                :color=\"item.priority === 'high' ? 'error' : item.priority === 'medium' ? 'warning' : 'info'\"\n                size=\"small\"\n                class=\"me-2 mb-2\"\n                @click=\"handlePendingItem(item)\"\n              >\n                {{ item.title }}\n              </v-chip>\n            </div>\n          </v-card-text>\n        </v-card>\n      </v-col>\n    </v-row>\n\n    <!-- 上传头像对话框 -->\n    <v-dialog v-model=\"showUploadDialog\" max-width=\"500\">\n      <v-card>\n        <v-card-title>上传头像</v-card-title>\n        <v-card-text>\n          <v-file-input\n            v-model=\"avatarFile\"\n            label=\"选择图片\"\n            accept=\"image/*\"\n            show-size\n            truncate-length=\"15\"\n            variant=\"outlined\"\n          ></v-file-input>\n\n          <div v-if=\"avatarPreview\" class=\"text-center mt-4\">\n            <v-avatar size=\"150\">\n              <v-img :src=\"avatarPreview\" alt=\"Avatar Preview\"></v-img>\n            </v-avatar>\n          </div>\n        </v-card-text>\n        <v-card-actions>\n          <v-spacer></v-spacer>\n          <v-btn\n            color=\"grey-darken-1\"\n            variant=\"text\"\n            @click=\"showUploadDialog = false\"\n          >\n            取消\n          </v-btn>\n          <v-btn\n            color=\"primary\"\n            variant=\"text\"\n            :disabled=\"!avatarFile\"\n            @click=\"uploadAvatar\"\n          >\n            上传\n          </v-btn>\n        </v-card-actions>\n      </v-card>\n    </v-dialog>\n\n    <!-- 工作流优化对话框 -->\n    <v-dialog v-model=\"showWorkflowOptimization\" max-width=\"1200\">\n      <v-card>\n        <v-card-title class=\"d-flex align-center\">\n          <v-icon icon=\"mdi-chart-line\" class=\"me-2\"></v-icon>\n          工作流优化分析\n        </v-card-title>\n\n        <v-card-text>\n          <v-row>\n            <!-- 工作流效率分析 -->\n            <v-col cols=\"12\" md=\"6\">\n              <v-card variant=\"outlined\">\n                <v-card-title class=\"text-h6\">效率分析</v-card-title>\n                <v-card-text>\n                  <div class=\"mb-4\">\n                    <div class=\"d-flex justify-space-between mb-2\">\n                      <span>任务处理速度</span>\n                      <span class=\"font-weight-bold\">{{ workflowMetrics.taskProcessingSpeed }}个/天</span>\n                    </div>\n                    <v-progress-linear\n                      :model-value=\"workflowMetrics.taskProcessingSpeed * 10\"\n                      color=\"primary\"\n                      height=\"6\"\n                    ></v-progress-linear>\n                  </div>\n\n                  <div class=\"mb-4\">\n                    <div class=\"d-flex justify-space-between mb-2\">\n                      <span>平均响应时间</span>\n                      <span class=\"font-weight-bold\">{{ workflowMetrics.avgResponseTime }}小时</span>\n                    </div>\n                    <v-progress-linear\n                      :model-value=\"Math.max(0, 100 - workflowMetrics.avgResponseTime * 5)\"\n                      color=\"info\"\n                      height=\"6\"\n                    ></v-progress-linear>\n                  </div>\n\n                  <div class=\"mb-4\">\n                    <div class=\"d-flex justify-space-between mb-2\">\n                      <span>工作流自动化率</span>\n                      <span class=\"font-weight-bold\">{{ workflowMetrics.automationRate }}%</span>\n                    </div>\n                    <v-progress-linear\n                      :model-value=\"workflowMetrics.automationRate\"\n                      color=\"success\"\n                      height=\"6\"\n                    ></v-progress-linear>\n                  </div>\n                </v-card-text>\n              </v-card>\n            </v-col>\n\n            <!-- 优化建议 -->\n            <v-col cols=\"12\" md=\"6\">\n              <v-card variant=\"outlined\">\n                <v-card-title class=\"text-h6\">优化建议</v-card-title>\n                <v-card-text>\n                  <v-list density=\"compact\">\n                    <v-list-item\n                      v-for=\"suggestion in optimizationSuggestions\"\n                      :key=\"suggestion.id\"\n                      :prepend-icon=\"suggestion.icon\"\n                      :title=\"suggestion.title\"\n                      :subtitle=\"suggestion.description\"\n                      @click=\"applySuggestion(suggestion)\"\n                    >\n                      <template v-slot:append>\n                        <v-chip\n                          :color=\"suggestion.impact === 'high' ? 'success' : suggestion.impact === 'medium' ? 'warning' : 'info'\"\n                          size=\"small\"\n                        >\n                          {{ suggestion.impact === 'high' ? '高效果' : suggestion.impact === 'medium' ? '中效果' : '低效果' }}\n                        </v-chip>\n                      </template>\n                    </v-list-item>\n                  </v-list>\n                </v-card-text>\n              </v-card>\n            </v-col>\n\n            <!-- 工作流模板 -->\n            <v-col cols=\"12\">\n              <v-card variant=\"outlined\">\n                <v-card-title class=\"text-h6\">工作流模板</v-card-title>\n                <v-card-text>\n                  <v-row>\n                    <v-col\n                      v-for=\"template in workflowTemplates\"\n                      :key=\"template.id\"\n                      cols=\"12\"\n                      md=\"4\"\n                    >\n                      <v-card\n                        variant=\"outlined\"\n                        class=\"workflow-template-card\"\n                        @click=\"applyWorkflowTemplate(template)\"\n                      >\n                        <v-card-text class=\"text-center\">\n                          <v-icon\n                            :icon=\"template.icon\"\n                            size=\"48\"\n                            :color=\"template.color\"\n                            class=\"mb-2\"\n                          ></v-icon>\n                          <div class=\"text-h6 mb-1\">{{ template.name }}</div>\n                          <div class=\"text-caption text-medium-emphasis\">{{ template.description }}</div>\n                        </v-card-text>\n                      </v-card>\n                    </v-col>\n                  </v-row>\n                </v-card-text>\n              </v-card>\n            </v-col>\n          </v-row>\n        </v-card-text>\n\n        <v-card-actions>\n          <v-spacer></v-spacer>\n          <v-btn\n            color=\"grey-darken-1\"\n            variant=\"text\"\n            @click=\"showWorkflowOptimization = false\"\n          >\n            关闭\n          </v-btn>\n          <v-btn\n            color=\"primary\"\n            variant=\"text\"\n            @click=\"exportWorkflowReport\"\n          >\n            导出报告\n          </v-btn>\n        </v-card-actions>\n      </v-card>\n    </v-dialog>\n\n    <!-- 操作成功提示 -->\n    <v-snackbar\n      v-model=\"showSnackbar\"\n      color=\"success\"\n    >\n      {{ snackbarText }}\n\n      <template v-slot:actions>\n        <v-btn\n          variant=\"text\"\n          @click=\"showSnackbar = false\"\n        >\n          关闭\n        </v-btn>\n      </template>\n    </v-snackbar>\n  </v-container>\n</template>\n\n<script>\nexport default {\n  name: 'ProfileView',\n  data() {\n    return {\n      editMode: false,\n      valid: true,\n      showUploadDialog: false,\n      avatarFile: null,\n      avatarPreview: null,\n      showSnackbar: false,\n      snackbarText: '',\n      showWorkflowOptimization: false,\n\n      nameRules: [\n        v => !!v || '姓名不能为空',\n        v => v.length <= 20 || '姓名不能超过20个字符'\n      ],\n      emailRules: [\n        v => !!v || '邮箱不能为空',\n        v => /.+@.+\\..+/.test(v) || '邮箱格式不正确'\n      ],\n      phoneRules: [\n        v => !!v || '手机号码不能为空',\n        v => /^1[3-9]\\d{9}$/.test(v) || '手机号码格式不正确'\n      ],\n\n      departments: ['技术部', '市场部', '销售部', '人力资源部', '财务部'],\n\n      user: {\n        name: '张三',\n        email: '<EMAIL>',\n        phone: '13800138000',\n        department: '技术部',\n        position: '项目经理',\n        joinDate: '2023-01-15',\n        address: '上海市浦东新区张江高科技园区',\n        bio: '拥有8年项目管理经验，专注于软件开发领域。擅长敏捷开发方法，曾成功带领团队完成多个大型项目。',\n        avatar: 'https://ui-avatars.com/api/?name=张三&background=random'\n      },\n\n      stats: {\n        projects: 12,\n        tasks: 156,\n        meetings: 48,\n        documents: 32,\n        taskCompletionRate: 92,\n        onTimeRate: 88\n      },\n\n      // 工作流优化相关数据\n      workflowMetrics: {\n        taskProcessingSpeed: 8.5,\n        avgResponseTime: 2.3,\n        automationRate: 75\n      },\n\n      pendingItems: [\n        { id: 1, title: '项目A审批', priority: 'high', type: 'approval' },\n        { id: 2, title: '任务B验收', priority: 'medium', type: 'review' },\n        { id: 3, title: '会议记录确认', priority: 'low', type: 'confirmation' }\n      ],\n\n      optimizationSuggestions: [\n        {\n          id: 1,\n          title: '启用任务自动分配',\n          description: '根据团队成员工作负载自动分配新任务',\n          icon: 'mdi-account-multiple-plus',\n          impact: 'high',\n          action: 'enable_auto_assignment'\n        },\n        {\n          id: 2,\n          title: '设置状态自动流转',\n          description: '任务完成后自动触发下一阶段',\n          icon: 'mdi-arrow-right-circle',\n          impact: 'high',\n          action: 'enable_auto_transition'\n        },\n        {\n          id: 3,\n          title: '优化通知频率',\n          description: '减少非关键通知，提高工作专注度',\n          icon: 'mdi-bell-outline',\n          impact: 'medium',\n          action: 'optimize_notifications'\n        },\n        {\n          id: 4,\n          title: '启用智能提醒',\n          description: '基于历史数据预测任务延期风险',\n          icon: 'mdi-brain',\n          impact: 'high',\n          action: 'enable_smart_reminders'\n        }\n      ],\n\n      workflowTemplates: [\n        {\n          id: 1,\n          name: '医疗项目标准流程',\n          description: '适用于医疗项目的标准化工作流',\n          icon: 'mdi-hospital-box',\n          color: 'primary'\n        },\n        {\n          id: 2,\n          name: '敏捷开发流程',\n          description: '快速迭代的敏捷开发工作流',\n          icon: 'mdi-rocket-launch',\n          color: 'success'\n        },\n        {\n          id: 3,\n          name: '审批密集型流程',\n          description: '需要多层审批的严格工作流',\n          icon: 'mdi-shield-check',\n          color: 'warning'\n        }\n      ]\n    }\n  },\n  computed: {\n    workflowEfficiency() {\n      // 基于任务完成率、按时完成率和自动化率计算工作流效率\n      const taskWeight = 0.4\n      const timeWeight = 0.3\n      const autoWeight = 0.3\n\n      return Math.round(\n        this.stats.taskCompletionRate * taskWeight +\n        this.stats.onTimeRate * timeWeight +\n        this.workflowMetrics.automationRate * autoWeight\n      )\n    }\n  },\n  watch: {\n    avatarFile(file) {\n      if (file) {\n        this.createImagePreview(file)\n      } else {\n        this.avatarPreview = null\n      }\n    }\n  },\n  methods: {\n    saveProfile() {\n      // 在实际应用中，这里会发送请求到后端保存个人资料\n      this.editMode = false\n      this.showSuccessMessage('个人资料保存成功')\n    },\n    createImagePreview(file) {\n      const reader = new FileReader()\n      reader.readAsDataURL(file)\n      reader.onload = e => {\n        this.avatarPreview = e.target.result\n      }\n    },\n    uploadAvatar() {\n      // 在实际应用中，这里会发送请求到后端上传头像\n      if (this.avatarPreview) {\n        this.user.avatar = this.avatarPreview\n      }\n      this.showUploadDialog = false\n      this.avatarFile = null\n      this.showSuccessMessage('头像上传成功')\n    },\n    showSuccessMessage(text) {\n      this.snackbarText = text\n      this.showSnackbar = true\n    },\n\n    // 工作流优化相关方法\n    getEfficiencyColor(efficiency) {\n      if (efficiency >= 85) return 'success'\n      if (efficiency >= 70) return 'warning'\n      return 'error'\n    },\n\n    handlePendingItem(item) {\n      // 处理待办事项\n      switch (item.type) {\n        case 'approval':\n          this.$router.push('/projects')\n          break\n        case 'review':\n          this.$router.push('/kanban')\n          break\n        case 'confirmation':\n          this.$router.push('/meetings')\n          break\n      }\n      this.showSuccessMessage(`正在处理：${item.title}`)\n    },\n\n    applySuggestion(suggestion) {\n      // 应用优化建议\n      switch (suggestion.action) {\n        case 'enable_auto_assignment':\n          this.enableAutoAssignment()\n          break\n        case 'enable_auto_transition':\n          this.enableAutoTransition()\n          break\n        case 'optimize_notifications':\n          this.optimizeNotifications()\n          break\n        case 'enable_smart_reminders':\n          this.enableSmartReminders()\n          break\n      }\n      this.showSuccessMessage(`已应用优化建议：${suggestion.title}`)\n    },\n\n    applyWorkflowTemplate(template) {\n      // 应用工作流模板\n      this.showSuccessMessage(`正在应用工作流模板：${template.name}`)\n      // 这里可以调用API来应用模板\n    },\n\n    exportWorkflowReport() {\n      // 导出工作流报告\n      const reportData = {\n        user: this.user.name,\n        date: new Date().toLocaleDateString(),\n        efficiency: this.workflowEfficiency,\n        metrics: this.workflowMetrics,\n        suggestions: this.optimizationSuggestions.length\n      }\n\n      // 模拟导出功能\n      console.log('导出工作流报告:', reportData)\n      this.showSuccessMessage('工作流报告已导出')\n    },\n\n    // 优化功能实现\n    enableAutoAssignment() {\n      // 启用自动分配功能\n      this.workflowMetrics.automationRate = Math.min(100, this.workflowMetrics.automationRate + 10)\n    },\n\n    enableAutoTransition() {\n      // 启用自动流转功能\n      this.workflowMetrics.automationRate = Math.min(100, this.workflowMetrics.automationRate + 15)\n      this.workflowMetrics.avgResponseTime = Math.max(0.5, this.workflowMetrics.avgResponseTime - 0.5)\n    },\n\n    optimizeNotifications() {\n      // 优化通知设置\n      this.workflowMetrics.avgResponseTime = Math.max(0.5, this.workflowMetrics.avgResponseTime - 0.3)\n    },\n\n    enableSmartReminders() {\n      // 启用智能提醒\n      this.workflowMetrics.taskProcessingSpeed = Math.min(15, this.workflowMetrics.taskProcessingSpeed + 1.5)\n      this.stats.onTimeRate = Math.min(100, this.stats.onTimeRate + 5)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.workflow-template-card {\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.workflow-template-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.workflow-template-card .v-card-text {\n  padding: 24px;\n}\n\n/* 工作流效率指标样式 */\n.v-progress-linear {\n  border-radius: 4px;\n}\n\n/* 待处理事项样式 */\n.v-chip {\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.v-chip:hover {\n  transform: scale(1.05);\n}\n\n/* 优化建议列表样式 */\n.v-list-item {\n  border-radius: 8px;\n  margin-bottom: 8px;\n  transition: background-color 0.2s ease;\n}\n\n.v-list-item:hover {\n  background-color: rgba(0, 0, 0, 0.04);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .workflow-template-card .v-card-text {\n    padding: 16px;\n  }\n\n  .v-icon {\n    font-size: 36px !important;\n  }\n}\n</style>\n"], "mappings": ";;EAIaA,KAAK,EAAC;AAAgD;;EA4BnDA,KAAK,EAAC;AAAc;;EACrBA,KAAK,EAAC;AAAkC;;EAoBtCA,KAAK,EAAC;AAA0B;;EAI5BA,KAAK,EAAC;AAAa;;EAIvBA,KAAK,EAAC;AAA0B;;EAI5BA,KAAK,EAAC;AAAa;;EAIvBA,KAAK,EAAC;AAA0B;;EAI5BA,KAAK,EAAC;AAAa;;EAIvBA,KAAK,EAAC;AAAqB;;EAIvBA,KAAK,EAAC;AAAa;;EAgHvBA,KAAK,EAAC;AAA2C;;EAgB7CA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAA0B;;EAMlCA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAA0B;;EAMlCA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAA0B;;EAMlCA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAA0B;;EAQtCA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAgD;;EAEpDA,KAAK,EAAC;AAA8B;;EAUxCA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAgD;;EAEpDA,KAAK,EAAC;AAA8B;;EAWxCA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAgD;;EAEpDA,KAAK,EAAC;AAA8B;;EA7QzDC,GAAA;AAAA;;EAAAA,GAAA;EAyToCD,KAAK,EAAC;;;EA0CnBA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAmC;;EAEtCA,KAAK,EAAC;AAAkB;;EAS7BA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAmC;;EAEtCA,KAAK,EAAC;AAAkB;;EAS7BA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAmC;;EAEtCA,KAAK,EAAC;AAAkB;;EAgErBA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;uBA9btEE,YAAA,CA6ecC,sBAAA;IA9ehBC,OAAA,EAAAC,QAAA,CAEI,MAoBQ,CApBRC,YAAA,CAoBQC,gBAAA;MAtBZH,OAAA,EAAAC,QAAA,CAGM,MAkBQ,CAlBRC,YAAA,CAkBQE,gBAAA;QAlBDC,IAAI,EAAC;MAAI;QAHtBL,OAAA,EAAAC,QAAA,CAIQ,MAgBM,CAhBNK,mBAAA,CAgBM,OAhBNC,UAgBM,G,4BAfJD,mBAAA,CAKM,cAJJA,mBAAA,CAA6B;UAAzBV,KAAK,EAAC;QAAS,GAAC,MAAI,GACxBU,mBAAA,CAEI;UAFDV,KAAK,EAAC;QAAsC,GAAC,eAEhD,E,sBAEFU,mBAAA,CAQM,cAPJJ,YAAA,CAMQM,gBAAA;UALNC,KAAK,EAAC,SAAS;UACf,cAAY,EAAC,YAAY;UACxBC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,KAAA,CAAAC,QAAQ,IAAID,KAAA,CAAAC,QAAQ;;UAf1Cd,OAAA,EAAAC,QAAA,CAiBc,MAAgC,CAjB9Cc,gBAAA,CAAAC,gBAAA,CAiBiBH,KAAA,CAAAC,QAAQ,mC;UAjBzBG,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;QAwBIf,YAAA,CAiRQC,gBAAA;MAzSZH,OAAA,EAAAC,QAAA,CAyBM,MA6DQ,CA7DRC,YAAA,CA6DQE,gBAAA;QA7DDC,IAAI,EAAC,IAAI;QAACa,EAAE,EAAC;;QAzB1BlB,OAAA,EAAAC,QAAA,CA0BQ,MAsBS,CAtBTC,YAAA,CAsBSiB,iBAAA;UAtBDvB,KAAK,EAAC;QAAM;UA1B5BI,OAAA,EAAAC,QAAA,CA2BU,MAoBc,CApBdC,YAAA,CAoBckB,sBAAA;YApBDxB,KAAK,EAAC;UAAa;YA3B1CI,OAAA,EAAAC,QAAA,CA4BY,MAEW,CAFXC,YAAA,CAEWmB,mBAAA;cAFDC,IAAI,EAAC,KAAK;cAAC1B,KAAK,EAAC;;cA5BvCI,OAAA,EAAAC,QAAA,CA6Bc,MAA6C,CAA7CC,YAAA,CAA6CqB,gBAAA;gBAArCC,GAAG,EAAEX,KAAA,CAAAY,IAAI,CAACC,MAAM;gBAAEC,GAAG,EAAC;;cA7B5CV,CAAA;gBAgCYX,mBAAA,CAA6C,MAA7CsB,UAA6C,EAAAZ,gBAAA,CAAjBH,KAAA,CAAAY,IAAI,CAACI,IAAI,kBACrCvB,mBAAA,CAAmE,KAAnEwB,UAAmE,EAAAd,gBAAA,CAApBH,KAAA,CAAAY,IAAI,CAACM,QAAQ,kBAE5D7B,YAAA,CAAoC8B,oBAAA;cAAzBpC,KAAK,EAAC;YAAM,IAGfiB,KAAA,CAAAC,QAAQ,I,cADhBhB,YAAA,CASQU,gBAAA;cA9CpBX,GAAA;cAuCcY,KAAK,EAAC,SAAS;cACfwB,OAAO,EAAC,MAAM;cACdC,KAAK,EAAL,EAAK;cACL,cAAY,EAAC,YAAY;cACxBxB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,KAAA,CAAAsB,gBAAgB;;cA3CtCnC,OAAA,EAAAC,QAAA,CA4Ca,MAEDU,MAAA,SAAAA,MAAA,QA9CZI,gBAAA,CA4Ca,QAED,E;cA9CZE,CAAA;cAAAmB,EAAA;kBAAAC,mBAAA,e;YAAApB,CAAA;;UAAAA,CAAA;YAkDQf,YAAA,CAmCSiB,iBAAA;UArFjBnB,OAAA,EAAAC,QAAA,CAmDU,MAAiC,CAAjCC,YAAA,CAAiCoC,uBAAA;YAnD3CtC,OAAA,EAAAC,QAAA,CAmDwB,MAAIU,MAAA,SAAAA,MAAA,QAnD5BI,gBAAA,CAmDwB,MAAI,E;YAnD5BE,CAAA;YAAAmB,EAAA;cAoDUlC,YAAA,CAgCckB,sBAAA;YApFxBpB,OAAA,EAAAC,QAAA,CAqDY,MAMM,CANNK,mBAAA,CAMM,OANNiC,UAMM,GALJrC,YAAA,CAAuDsC,iBAAA;cAA/CC,IAAI,EAAC,mBAAmB;cAAC7C,KAAK,EAAC;gBACvCU,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAuD;cAAlDV,KAAK,EAAC;YAAmC,GAAC,IAAE,sBACjDU,mBAAA,CAA+C,OAA/CoC,UAA+C,EAAA1B,gBAAA,CAAnBH,KAAA,CAAAY,IAAI,CAACkB,KAAK,iB,KAI1CrC,mBAAA,CAMM,OANNsC,UAMM,GALJ1C,YAAA,CAAuDsC,iBAAA;cAA/CC,IAAI,EAAC,mBAAmB;cAAC7C,KAAK,EAAC;gBACvCU,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAuD;cAAlDV,KAAK,EAAC;YAAmC,GAAC,IAAE,sBACjDU,mBAAA,CAA+C,OAA/CuC,UAA+C,EAAA7B,gBAAA,CAAnBH,KAAA,CAAAY,IAAI,CAACqB,KAAK,iB,KAI1CxC,mBAAA,CAMM,OANNyC,UAMM,GALJ7C,YAAA,CAAiEsC,iBAAA;cAAzDC,IAAI,EAAC,6BAA6B;cAAC7C,KAAK,EAAC;gBACjDU,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAuD;cAAlDV,KAAK,EAAC;YAAmC,GAAC,IAAE,sBACjDU,mBAAA,CAAoD,OAApD0C,UAAoD,EAAAhC,gBAAA,CAAxBH,KAAA,CAAAY,IAAI,CAACwB,UAAU,iB,KAI/C3C,mBAAA,CAMM,OANN4C,WAMM,GALJhD,YAAA,CAA4DsC,iBAAA;cAApDC,IAAI,EAAC,wBAAwB;cAAC7C,KAAK,EAAC;gBAC5CU,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAuD;cAAlDV,KAAK,EAAC;YAAmC,GAAC,IAAE,sBACjDU,mBAAA,CAAiD,OAAjD6C,WAAiD,EAAAnC,gBAAA,CAArBH,KAAA,CAAAY,IAAI,CAAC2B,OAAO,iB;YAjFxDnC,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;UAwFMf,YAAA,CAgNQE,gBAAA;QAhNDC,IAAI,EAAC,IAAI;QAACa,EAAE,EAAC;;QAxF1BlB,OAAA,EAAAC,QAAA,CAyFQ,MAoGS,CApGTC,YAAA,CAoGSiB,iBAAA;UApGDvB,KAAK,EAAC;QAAM;UAzF5BI,OAAA,EAAAC,QAAA,CA0FU,MAAiC,CAAjCC,YAAA,CAAiCoC,uBAAA;YA1F3CtC,OAAA,EAAAC,QAAA,CA0FwB,MAAIU,MAAA,SAAAA,MAAA,QA1F5BI,gBAAA,CA0FwB,MAAI,E;YA1F5BE,CAAA;YAAAmB,EAAA;cA2FUlC,YAAA,CAiGckB,sBAAA;YA5LxBpB,OAAA,EAAAC,QAAA,CA4FY,MA+FS,CA/FTC,YAAA,CA+FSmD,iBAAA;cA/FDC,GAAG,EAAC,MAAM;cA5F9BC,UAAA,EA4FwC1C,KAAA,CAAA2C,KAAK;cA5F7C,uBAAA7C,MAAA,SAAAA,MAAA,OAAAC,MAAA,IA4FwCC,KAAA,CAAA2C,KAAK,GAAA5C,MAAA;;cA5F7CZ,OAAA,EAAAC,QAAA,CA6Fc,MAiFQ,CAjFRC,YAAA,CAiFQC,gBAAA;gBA9KtBH,OAAA,EAAAC,QAAA,CA8FgB,MASQ,CATRC,YAAA,CASQE,gBAAA;kBATDC,IAAI,EAAC,IAAI;kBAACa,EAAE,EAAC;;kBA9FpClB,OAAA,EAAAC,QAAA,CA+FkB,MAOgB,CAPhBC,YAAA,CAOgBuD,uBAAA;oBAtGlCF,UAAA,EAgG6B1C,KAAA,CAAAY,IAAI,CAACI,IAAI;oBAhGtC,uBAAAlB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAgG6BC,KAAA,CAAAY,IAAI,CAACI,IAAI,GAAAjB,MAAA;oBAClB8C,KAAK,EAAC,IAAI;oBACTC,KAAK,EAAE9C,KAAA,CAAA+C,SAAS;oBACjB3B,OAAO,EAAC,UAAU;oBACjB4B,QAAQ,GAAGhD,KAAA,CAAAC,QAAQ;oBACpBgD,QAAQ,EAAR;;kBArGpB7C,CAAA;oBAyGgBf,YAAA,CASQE,gBAAA;kBATDC,IAAI,EAAC,IAAI;kBAACa,EAAE,EAAC;;kBAzGpClB,OAAA,EAAAC,QAAA,CA0GkB,MAOgB,CAPhBC,YAAA,CAOgBuD,uBAAA;oBAjHlCF,UAAA,EA2G6B1C,KAAA,CAAAY,IAAI,CAACkB,KAAK;oBA3GvC,uBAAAhC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA2G6BC,KAAA,CAAAY,IAAI,CAACkB,KAAK,GAAA/B,MAAA;oBACnB8C,KAAK,EAAC,IAAI;oBACTC,KAAK,EAAE9C,KAAA,CAAAkD,UAAU;oBAClB9B,OAAO,EAAC,UAAU;oBAClB4B,QAAQ,EAAR,EAAQ;oBACRG,QAAQ,EAAR;;kBAhHpB/C,CAAA;oBAoHgBf,YAAA,CAQQE,gBAAA;kBARDC,IAAI,EAAC,IAAI;kBAACa,EAAE,EAAC;;kBApHpClB,OAAA,EAAAC,QAAA,CAqHkB,MAMgB,CANhBC,YAAA,CAMgBuD,uBAAA;oBA3HlCF,UAAA,EAsH6B1C,KAAA,CAAAY,IAAI,CAACqB,KAAK;oBAtHvC,uBAAAnC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAsH6BC,KAAA,CAAAY,IAAI,CAACqB,KAAK,GAAAlC,MAAA;oBACnB8C,KAAK,EAAC,MAAM;oBACXC,KAAK,EAAE9C,KAAA,CAAAoD,UAAU;oBAClBhC,OAAO,EAAC,UAAU;oBACjB4B,QAAQ,GAAGhD,KAAA,CAAAC;;kBA1HhCG,CAAA;oBA8HgBf,YAAA,CAQQE,gBAAA;kBARDC,IAAI,EAAC,IAAI;kBAACa,EAAE,EAAC;;kBA9HpClB,OAAA,EAAAC,QAAA,CA+HkB,MAMY,CANZC,YAAA,CAMYgE,mBAAA;oBArI9BX,UAAA,EAgI6B1C,KAAA,CAAAY,IAAI,CAACwB,UAAU;oBAhI5C,uBAAAtC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAgI6BC,KAAA,CAAAY,IAAI,CAACwB,UAAU,GAAArC,MAAA;oBACvBuD,KAAK,EAAEtD,KAAA,CAAAuD,WAAW;oBACnBV,KAAK,EAAC,IAAI;oBACVzB,OAAO,EAAC,UAAU;oBACjB4B,QAAQ,GAAGhD,KAAA,CAAAC;;kBApIhCG,CAAA;oBAwIgBf,YAAA,CAOQE,gBAAA;kBAPDC,IAAI,EAAC,IAAI;kBAACa,EAAE,EAAC;;kBAxIpClB,OAAA,EAAAC,QAAA,CAyIkB,MAKgB,CALhBC,YAAA,CAKgBuD,uBAAA;oBA9IlCF,UAAA,EA0I6B1C,KAAA,CAAAY,IAAI,CAACM,QAAQ;oBA1I1C,uBAAApB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA0I6BC,KAAA,CAAAY,IAAI,CAACM,QAAQ,GAAAnB,MAAA;oBACtB8C,KAAK,EAAC,IAAI;oBACVzB,OAAO,EAAC,UAAU;oBACjB4B,QAAQ,GAAGhD,KAAA,CAAAC;;kBA7IhCG,CAAA;oBAiJgBf,YAAA,CAQQE,gBAAA;kBARDC,IAAI,EAAC,IAAI;kBAACa,EAAE,EAAC;;kBAjJpClB,OAAA,EAAAC,QAAA,CAkJkB,MAMgB,CANhBC,YAAA,CAMgBuD,uBAAA;oBAxJlCF,UAAA,EAmJ6B1C,KAAA,CAAAY,IAAI,CAAC4C,QAAQ;oBAnJ1C,uBAAA1D,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAmJ6BC,KAAA,CAAAY,IAAI,CAAC4C,QAAQ,GAAAzD,MAAA;oBACtB8C,KAAK,EAAC,MAAM;oBACZzB,OAAO,EAAC,UAAU;oBAClB4B,QAAQ,EAAR,EAAQ;oBACRG,QAAQ,EAAR;;kBAvJpB/C,CAAA;oBA2JgBf,YAAA,CAOQE,gBAAA;kBAPDC,IAAI,EAAC;gBAAI;kBA3JhCL,OAAA,EAAAC,QAAA,CA4JkB,MAKgB,CALhBC,YAAA,CAKgBuD,uBAAA;oBAjKlCF,UAAA,EA6J6B1C,KAAA,CAAAY,IAAI,CAAC2B,OAAO;oBA7JzC,uBAAAzC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA6J6BC,KAAA,CAAAY,IAAI,CAAC2B,OAAO,GAAAxC,MAAA;oBACrB8C,KAAK,EAAC,IAAI;oBACVzB,OAAO,EAAC,UAAU;oBACjB4B,QAAQ,GAAGhD,KAAA,CAAAC;;kBAhKhCG,CAAA;oBAoKgBf,YAAA,CASQE,gBAAA;kBATDC,IAAI,EAAC;gBAAI;kBApKhCL,OAAA,EAAAC,QAAA,CAqKkB,MAOc,CAPdC,YAAA,CAOcoE,qBAAA;oBA5KhCf,UAAA,EAsK6B1C,KAAA,CAAAY,IAAI,CAAC8C,GAAG;oBAtKrC,uBAAA5D,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAsK6BC,KAAA,CAAAY,IAAI,CAAC8C,GAAG,GAAA3D,MAAA;oBACjB8C,KAAK,EAAC,MAAM;oBACZzB,OAAO,EAAC,UAAU;oBACjB4B,QAAQ,GAAGhD,KAAA,CAAAC,QAAQ;oBACpB,WAAS,EAAT,EAAS;oBACT0D,IAAI,EAAC;;kBA3KzBvD,CAAA;;gBAAAA,CAAA;kBAgL2BJ,KAAA,CAAAC,QAAQ,I,cAArBhB,YAAA,CAUQK,gBAAA;gBA1LtBN,GAAA;cAAA;gBAAAG,OAAA,EAAAC,QAAA,CAiLgB,MAQQ,CARRC,YAAA,CAQQE,gBAAA;kBARDC,IAAI,EAAC,IAAI;kBAACT,KAAK,EAAC;;kBAjLvCI,OAAA,EAAAC,QAAA,CAkLkB,MAMQ,CANRC,YAAA,CAMQM,gBAAA;oBALNC,KAAK,EAAC,SAAS;oBACduD,QAAQ,GAAGnD,KAAA,CAAA2C,KAAK;oBAChB9C,OAAK,EAAE+D,QAAA,CAAAC;;oBArL5B1E,OAAA,EAAAC,QAAA,CAsLmB,MAEDU,MAAA,SAAAA,MAAA,QAxLlBI,gBAAA,CAsLmB,QAED,E;oBAxLlBE,CAAA;oBAAAmB,EAAA;;kBAAAnB,CAAA;;gBAAAA,CAAA;oBAAAoB,mBAAA,e;cAAApB,CAAA;;YAAAA,CAAA;;UAAAA,CAAA;YA+LQf,YAAA,CAwGSiB,iBAAA;UAvSjBnB,OAAA,EAAAC,QAAA,CAgMU,MAae,CAbfC,YAAA,CAaeoC,uBAAA;YA7MzBtC,OAAA,EAAAC,QAAA,CAiMY,MAWM,CAXNK,mBAAA,CAWM,OAXNqE,WAWM,G,4BAVJrE,mBAAA,CAAiB,cAAX,MAAI,sBACVJ,YAAA,CAQQM,gBAAA;cAPNC,KAAK,EAAC,SAAS;cACfwB,OAAO,EAAC,MAAM;cACdX,IAAI,EAAC,OAAO;cACZ,cAAY,EAAC,gBAAgB;cAC5BZ,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,KAAA,CAAA+D,wBAAwB;;cAxMhD5E,OAAA,EAAAC,QAAA,CAyMe,MAEDU,MAAA,SAAAA,MAAA,QA3MdI,gBAAA,CAyMe,SAED,E;cA3MdE,CAAA;cAAAmB,EAAA;;YAAAnB,CAAA;cA8MUf,YAAA,CAwFckB,sBAAA;YAtSxBpB,OAAA,EAAAC,QAAA,CA+MY,MA4BQ,CA5BRC,YAAA,CA4BQC,gBAAA;cA3OpBH,OAAA,EAAAC,QAAA,CAgNc,MAKQ,CALRC,YAAA,CAKQE,gBAAA;gBALDC,IAAI,EAAC,GAAG;gBAACa,EAAE,EAAC;;gBAhNjClB,OAAA,EAAAC,QAAA,CAiNgB,MAGM,CAHNK,mBAAA,CAGM,OAHNuE,WAGM,GAFJvE,mBAAA,CAAgE,OAAhEwE,WAAgE,EAAA9D,gBAAA,CAAvBH,KAAA,CAAAkE,KAAK,CAACC,QAAQ,kB,4BACvD1E,mBAAA,CAAyD;kBAApDV,KAAK,EAAC;gBAAmC,GAAC,MAAI,qB;gBAnNrEqB,CAAA;kBAuNcf,YAAA,CAKQE,gBAAA;gBALDC,IAAI,EAAC,GAAG;gBAACa,EAAE,EAAC;;gBAvNjClB,OAAA,EAAAC,QAAA,CAwNgB,MAGM,CAHNK,mBAAA,CAGM,OAHN2E,WAGM,GAFJ3E,mBAAA,CAA6D,OAA7D4E,WAA6D,EAAAlE,gBAAA,CAApBH,KAAA,CAAAkE,KAAK,CAACI,KAAK,kB,4BACpD7E,mBAAA,CAAyD;kBAApDV,KAAK,EAAC;gBAAmC,GAAC,MAAI,qB;gBA1NrEqB,CAAA;kBA8Ncf,YAAA,CAKQE,gBAAA;gBALDC,IAAI,EAAC,GAAG;gBAACa,EAAE,EAAC;;gBA9NjClB,OAAA,EAAAC,QAAA,CA+NgB,MAGM,CAHNK,mBAAA,CAGM,OAHN8E,WAGM,GAFJ9E,mBAAA,CAAgE,OAAhE+E,WAAgE,EAAArE,gBAAA,CAAvBH,KAAA,CAAAkE,KAAK,CAACO,QAAQ,kB,4BACvDhF,mBAAA,CAAyD;kBAApDV,KAAK,EAAC;gBAAmC,GAAC,MAAI,qB;gBAjOrEqB,CAAA;kBAqOcf,YAAA,CAKQE,gBAAA;gBALDC,IAAI,EAAC,GAAG;gBAACa,EAAE,EAAC;;gBArOjClB,OAAA,EAAAC,QAAA,CAsOgB,MAGM,CAHNK,mBAAA,CAGM,OAHNiF,WAGM,GAFJjF,mBAAA,CAAiE,OAAjEkF,WAAiE,EAAAxE,gBAAA,CAAxBH,KAAA,CAAAkE,KAAK,CAACU,SAAS,kB,4BACxDnF,mBAAA,CAAyD;kBAApDV,KAAK,EAAC;gBAAmC,GAAC,MAAI,qB;gBAxOrEqB,CAAA;;cAAAA,CAAA;gBA6OYf,YAAA,CAAoC8B,oBAAA;cAAzBpC,KAAK,EAAC;YAAM,IAEvBU,mBAAA,CAWM,OAXNoF,WAWM,GAVJpF,mBAAA,CAGM,OAHNqF,WAGM,G,4BAFJrF,mBAAA,CAAoC;cAA/BV,KAAK,EAAC;YAAa,GAAC,OAAK,sBAC9BU,mBAAA,CAA+E,OAA/EsF,WAA+E,EAAA5E,gBAAA,CAAlCH,KAAA,CAAAkE,KAAK,CAACc,kBAAkB,IAAG,GAAC,gB,GAE3E3F,YAAA,CAKqB4F,4BAAA;cAJlB,aAAW,EAAEjF,KAAA,CAAAkE,KAAK,CAACc,kBAAkB;cACtCpF,KAAK,EAAC,SAAS;cACfsF,MAAM,EAAC,GAAG;cACVC,OAAO,EAAP;wDAIJ1F,mBAAA,CAWM,OAXN2F,WAWM,GAVJ3F,mBAAA,CAGM,OAHN4F,WAGM,G,4BAFJ5F,mBAAA,CAAoC;cAA/BV,KAAK,EAAC;YAAa,GAAC,OAAK,sBAC9BU,mBAAA,CAAuE,OAAvE6F,WAAuE,EAAAnF,gBAAA,CAA1BH,KAAA,CAAAkE,KAAK,CAACqB,UAAU,IAAG,GAAC,gB,GAEnElG,YAAA,CAKqB4F,4BAAA;cAJlB,aAAW,EAAEjF,KAAA,CAAAkE,KAAK,CAACqB,UAAU;cAC9B3F,KAAK,EAAC,MAAM;cACZsF,MAAM,EAAC,GAAG;cACVC,OAAO,EAAP;wDAIJ3D,mBAAA,aAAgB,EAChB/B,mBAAA,CAWM,OAXN+F,WAWM,GAVJ/F,mBAAA,CAGM,OAHNgG,WAGM,G,4BAFJhG,mBAAA,CAAoC;cAA/BV,KAAK,EAAC;YAAa,GAAC,OAAK,sBAC9BU,mBAAA,CAAyE,OAAzEiG,WAAyE,EAAAvF,gBAAA,CAA5ByD,QAAA,CAAA+B,kBAAkB,IAAG,GAAC,gB,GAErEtG,YAAA,CAKqB4F,4BAAA;cAJlB,aAAW,EAAErB,QAAA,CAAA+B,kBAAkB;cAC/B/F,KAAK,EAAEgE,QAAA,CAAAgC,kBAAkB,CAAChC,QAAA,CAAA+B,kBAAkB;cAC7CT,MAAM,EAAC,GAAG;cACVC,OAAO,EAAP;iEAIJ3D,mBAAA,WAAc,EACHxB,KAAA,CAAA6F,YAAY,CAACC,MAAM,Q,cAA9BC,mBAAA,CAaM,OArSlBC,WAAA,GAyRc3G,YAAA,CAAoC8B,oBAAA;cAAzBpC,KAAK,EAAC;YAAM,I,4BACvBU,mBAAA,CAA6C;cAAxCV,KAAK,EAAC;YAAsB,GAAC,OAAK,uB,kBACvCgH,mBAAA,CASSE,SAAA,QApSvBC,WAAA,CA4R+BlG,KAAA,CAAA6F,YAAY,EAApBM,IAAI;mCADblH,YAAA,CASSmH,iBAAA;gBAPNpH,GAAG,EAAEmH,IAAI,CAACE,EAAE;gBACZzG,KAAK,EAAEuG,IAAI,CAACG,QAAQ,wBAAwBH,IAAI,CAACG,QAAQ;gBAC1D7F,IAAI,EAAC,OAAO;gBACZ1B,KAAK,EAAC,WAAW;gBAChBc,OAAK,EAAAE,MAAA,IAAE6D,QAAA,CAAA2C,iBAAiB,CAACJ,IAAI;;gBAjS9ChH,OAAA,EAAAC,QAAA,CAmSgB,MAAgB,CAnShCc,gBAAA,CAAAC,gBAAA,CAmSmBgG,IAAI,CAACK,KAAK,iB;gBAnS7BpG,CAAA;;gDAAAoB,mBAAA,e;YAAApB,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;QA2SIoB,mBAAA,aAAgB,EAChBnC,YAAA,CAsCWoH,mBAAA;MAlVf/D,UAAA,EA4SuB1C,KAAA,CAAAsB,gBAAgB;MA5SvC,uBAAAxB,MAAA,SAAAA,MAAA,OAAAC,MAAA,IA4SuBC,KAAA,CAAAsB,gBAAgB,GAAAvB,MAAA;MAAE,WAAS,EAAC;;MA5SnDZ,OAAA,EAAAC,QAAA,CA6SM,MAoCS,CApCTC,YAAA,CAoCSiB,iBAAA;QAjVfnB,OAAA,EAAAC,QAAA,CA8SQ,MAAiC,CAAjCC,YAAA,CAAiCoC,uBAAA;UA9SzCtC,OAAA,EAAAC,QAAA,CA8SsB,MAAIU,MAAA,SAAAA,MAAA,QA9S1BI,gBAAA,CA8SsB,MAAI,E;UA9S1BE,CAAA;UAAAmB,EAAA;YA+SQlC,YAAA,CAeckB,sBAAA;UA9TtBpB,OAAA,EAAAC,QAAA,CAgTU,MAOgB,CAPhBC,YAAA,CAOgBqH,uBAAA;YAvT1BhE,UAAA,EAiTqB1C,KAAA,CAAA2G,UAAU;YAjT/B,uBAAA7G,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAiTqBC,KAAA,CAAA2G,UAAU,GAAA5G,MAAA;YACnB8C,KAAK,EAAC,MAAM;YACZ+D,MAAM,EAAC,SAAS;YAChB,WAAS,EAAT,EAAS;YACT,iBAAe,EAAC,IAAI;YACpBxF,OAAO,EAAC;mDAGCpB,KAAA,CAAA6G,aAAa,I,cAAxBd,mBAAA,CAIM,OAJNe,WAIM,GAHJzH,YAAA,CAEWmB,mBAAA;YAFDC,IAAI,EAAC;UAAK;YA1ThCtB,OAAA,EAAAC,QAAA,CA2Tc,MAAyD,CAAzDC,YAAA,CAAyDqB,gBAAA;cAAjDC,GAAG,EAAEX,KAAA,CAAA6G,aAAa;cAAE/F,GAAG,EAAC;;YA3T9CV,CAAA;kBAAAoB,mBAAA,e;UAAApB,CAAA;YA+TQf,YAAA,CAiBiB0H,yBAAA;UAhVzB5H,OAAA,EAAAC,QAAA,CAgUU,MAAqB,CAArBC,YAAA,CAAqB2H,mBAAA,GACrB3H,YAAA,CAMQM,gBAAA;YALNC,KAAK,EAAC,eAAe;YACrBwB,OAAO,EAAC,MAAM;YACbvB,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,KAAA,CAAAsB,gBAAgB;;YApUpCnC,OAAA,EAAAC,QAAA,CAqUW,MAEDU,MAAA,SAAAA,MAAA,QAvUVI,gBAAA,CAqUW,MAED,E;YAvUVE,CAAA;YAAAmB,EAAA;cAwUUlC,YAAA,CAOQM,gBAAA;YANNC,KAAK,EAAC,SAAS;YACfwB,OAAO,EAAC,MAAM;YACb+B,QAAQ,GAAGnD,KAAA,CAAA2G,UAAU;YACrB9G,OAAK,EAAE+D,QAAA,CAAAqD;;YA5UpB9H,OAAA,EAAAC,QAAA,CA6UW,MAEDU,MAAA,SAAAA,MAAA,QA/UVI,gBAAA,CA6UW,MAED,E;YA/UVE,CAAA;YAAAmB,EAAA;;UAAAnB,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;uCAoVIoB,mBAAA,cAAiB,EACjBnC,YAAA,CAuIWoH,mBAAA;MA5df/D,UAAA,EAqVuB1C,KAAA,CAAA+D,wBAAwB;MArV/C,uBAAAjE,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAqVuBC,KAAA,CAAA+D,wBAAwB,GAAAhE,MAAA;MAAE,WAAS,EAAC;;MArV3DZ,OAAA,EAAAC,QAAA,CAsVM,MAqIS,CArITC,YAAA,CAqISiB,iBAAA;QA3dfnB,OAAA,EAAAC,QAAA,CAuVQ,MAGe,CAHfC,YAAA,CAGeoC,uBAAA;UAHD1C,KAAK,EAAC;QAAqB;UAvVjDI,OAAA,EAAAC,QAAA,CAwVU,MAAoD,CAApDC,YAAA,CAAoDsC,iBAAA;YAA5CC,IAAI,EAAC,gBAAgB;YAAC7C,KAAK,EAAC;0CAxV9CmB,gBAAA,CAwV8D,WAEtD,G;UA1VRE,CAAA;UAAAmB,EAAA;YA4VQlC,YAAA,CA4GckB,sBAAA;UAxctBpB,OAAA,EAAAC,QAAA,CA6VU,MA0GQ,CA1GRC,YAAA,CA0GQC,gBAAA;YAvclBH,OAAA,EAAAC,QAAA,CA8VY,MAAgB,CAAhBoC,mBAAA,aAAgB,EAChBnC,YAAA,CAyCQE,gBAAA;cAzCDC,IAAI,EAAC,IAAI;cAACa,EAAE,EAAC;;cA/VhClB,OAAA,EAAAC,QAAA,CAgWc,MAuCS,CAvCTC,YAAA,CAuCSiB,iBAAA;gBAvCDc,OAAO,EAAC;cAAU;gBAhWxCjC,OAAA,EAAAC,QAAA,CAiWgB,MAAiD,CAAjDC,YAAA,CAAiDoC,uBAAA;kBAAnC1C,KAAK,EAAC;gBAAS;kBAjW7CI,OAAA,EAAAC,QAAA,CAiW8C,MAAIU,MAAA,SAAAA,MAAA,QAjWlDI,gBAAA,CAiW8C,MAAI,E;kBAjWlDE,CAAA;kBAAAmB,EAAA;oBAkWgBlC,YAAA,CAoCckB,sBAAA;kBAtY9BpB,OAAA,EAAAC,QAAA,CAmWkB,MAUM,CAVNK,mBAAA,CAUM,OAVNyH,WAUM,GATJzH,mBAAA,CAGM,OAHN0H,WAGM,G,4BAFJ1H,mBAAA,CAAmB,cAAb,QAAM,sBACZA,mBAAA,CAAkF,QAAlF2H,WAAkF,EAAAjH,gBAAA,CAAhDH,KAAA,CAAAqH,eAAe,CAACC,mBAAmB,IAAG,KAAG,gB,GAE7EjI,YAAA,CAIqB4F,4BAAA;oBAHlB,aAAW,EAAEjF,KAAA,CAAAqH,eAAe,CAACC,mBAAmB;oBACjD1H,KAAK,EAAC,SAAS;oBACfsF,MAAM,EAAC;8DAIXzF,mBAAA,CAUM,OAVN8H,WAUM,GATJ9H,mBAAA,CAGM,OAHN+H,WAGM,G,4BAFJ/H,mBAAA,CAAmB,cAAb,QAAM,sBACZA,mBAAA,CAA6E,QAA7EgI,WAA6E,EAAAtH,gBAAA,CAA3CH,KAAA,CAAAqH,eAAe,CAACK,eAAe,IAAG,IAAE,gB,GAExErI,YAAA,CAIqB4F,4BAAA;oBAHlB,aAAW,EAAE0C,IAAI,CAACC,GAAG,UAAU5H,KAAA,CAAAqH,eAAe,CAACK,eAAe;oBAC/D9H,KAAK,EAAC,MAAM;oBACZsF,MAAM,EAAC;8DAIXzF,mBAAA,CAUM,OAVNoI,WAUM,GATJpI,mBAAA,CAGM,OAHNqI,WAGM,G,4BAFJrI,mBAAA,CAAoB,cAAd,SAAO,sBACbA,mBAAA,CAA2E,QAA3EsI,WAA2E,EAAA5H,gBAAA,CAAzCH,KAAA,CAAAqH,eAAe,CAACW,cAAc,IAAG,GAAC,gB,GAEtE3I,YAAA,CAIqB4F,4BAAA;oBAHlB,aAAW,EAAEjF,KAAA,CAAAqH,eAAe,CAACW,cAAc;oBAC5CpI,KAAK,EAAC,SAAS;oBACfsF,MAAM,EAAC;;kBAnY7B9E,CAAA;;gBAAAA,CAAA;;cAAAA,CAAA;gBA0YYoB,mBAAA,UAAa,EACbnC,YAAA,CAyBQE,gBAAA;cAzBDC,IAAI,EAAC,IAAI;cAACa,EAAE,EAAC;;cA3YhClB,OAAA,EAAAC,QAAA,CA4Yc,MAuBS,CAvBTC,YAAA,CAuBSiB,iBAAA;gBAvBDc,OAAO,EAAC;cAAU;gBA5YxCjC,OAAA,EAAAC,QAAA,CA6YgB,MAAiD,CAAjDC,YAAA,CAAiDoC,uBAAA;kBAAnC1C,KAAK,EAAC;gBAAS;kBA7Y7CI,OAAA,EAAAC,QAAA,CA6Y8C,MAAIU,MAAA,SAAAA,MAAA,QA7YlDI,gBAAA,CA6Y8C,MAAI,E;kBA7YlDE,CAAA;kBAAAmB,EAAA;oBA8YgBlC,YAAA,CAoBckB,sBAAA;kBAla9BpB,OAAA,EAAAC,QAAA,CA+YkB,MAkBS,CAlBTC,YAAA,CAkBS4I,iBAAA;oBAlBDC,OAAO,EAAC;kBAAS;oBA/Y3C/I,OAAA,EAAAC,QAAA,CAiZsB,MAA6C,E,kBAD/C2G,mBAAA,CAgBcE,SAAA,QAhalCC,WAAA,CAiZ2ClG,KAAA,CAAAmI,uBAAuB,EAArCC,UAAU;2CADnBnJ,YAAA,CAgBcoJ,sBAAA;wBAdXrJ,GAAG,EAAEoJ,UAAU,CAAC/B,EAAE;wBAClB,cAAY,EAAE+B,UAAU,CAACxG,IAAI;wBAC7B4E,KAAK,EAAE4B,UAAU,CAAC5B,KAAK;wBACvB8B,QAAQ,EAAEF,UAAU,CAACG,WAAW;wBAChC1I,OAAK,EAAAE,MAAA,IAAE6D,QAAA,CAAA4E,eAAe,CAACJ,UAAU;;wBAEjBK,MAAM,EAAArJ,QAAA,CACrB,MAKS,CALTC,YAAA,CAKS+G,iBAAA;0BAJNxG,KAAK,EAAEwI,UAAU,CAACM,MAAM,0BAA0BN,UAAU,CAACM,MAAM;0BACpEjI,IAAI,EAAC;;0BA3Z/BtB,OAAA,EAAAC,QAAA,CA6Z0B,MAA2F,CA7ZrHc,gBAAA,CAAAC,gBAAA,CA6Z6BiI,UAAU,CAACM,MAAM,sBAAsBN,UAAU,CAACM,MAAM,8C;0BA7ZrFtI,CAAA;;wBAAAA,CAAA;;;oBAAAA,CAAA;;kBAAAA,CAAA;;gBAAAA,CAAA;;cAAAA,CAAA;gBAsaYoB,mBAAA,WAAc,EACdnC,YAAA,CA+BQE,gBAAA;cA/BDC,IAAI,EAAC;YAAI;cAva5BL,OAAA,EAAAC,QAAA,CAwac,MA6BS,CA7BTC,YAAA,CA6BSiB,iBAAA;gBA7BDc,OAAO,EAAC;cAAU;gBAxaxCjC,OAAA,EAAAC,QAAA,CAyagB,MAAkD,CAAlDC,YAAA,CAAkDoC,uBAAA;kBAApC1C,KAAK,EAAC;gBAAS;kBAza7CI,OAAA,EAAAC,QAAA,CAya8C,MAAKU,MAAA,SAAAA,MAAA,QAzanDI,gBAAA,CAya8C,OAAK,E;kBAzanDE,CAAA;kBAAAmB,EAAA;oBA0agBlC,YAAA,CA0BckB,sBAAA;kBApc9BpB,OAAA,EAAAC,QAAA,CA2akB,MAwBQ,CAxBRC,YAAA,CAwBQC,gBAAA;oBAnc1BH,OAAA,EAAAC,QAAA,CA6asB,MAAqC,E,kBADvC2G,mBAAA,CAsBQE,SAAA,QAlc5BC,WAAA,CA6ayClG,KAAA,CAAA2I,iBAAiB,EAA7BC,QAAQ;2CADjB3J,YAAA,CAsBQM,gBAAA;wBApBLP,GAAG,EAAE4J,QAAQ,CAACvC,EAAE;wBACjB7G,IAAI,EAAC,IAAI;wBACTa,EAAE,EAAC;;wBAhbzBlB,OAAA,EAAAC,QAAA,CAkbsB,MAeS,CAfTC,YAAA,CAeSiB,iBAAA;0BAdPc,OAAO,EAAC,UAAU;0BAClBrC,KAAK,EAAC,wBAAwB;0BAC7Bc,OAAK,EAAAE,MAAA,IAAE6D,QAAA,CAAAiF,qBAAqB,CAACD,QAAQ;;0BArb9DzJ,OAAA,EAAAC,QAAA,CAubwB,MASc,CATdC,YAAA,CASckB,sBAAA;4BATDxB,KAAK,EAAC;0BAAa;4BAvbxDI,OAAA,EAAAC,QAAA,CAwb0B,MAKU,CALVC,YAAA,CAKUsC,iBAAA;8BAJPC,IAAI,EAAEgH,QAAQ,CAAChH,IAAI;8BACpBnB,IAAI,EAAC,IAAI;8BACRb,KAAK,EAAEgJ,QAAQ,CAAChJ,KAAK;8BACtBb,KAAK,EAAC;wEAERU,mBAAA,CAAmD,OAAnDqJ,WAAmD,EAAA3I,gBAAA,CAAtByI,QAAQ,CAAC5H,IAAI,kBAC1CvB,mBAAA,CAA+E,OAA/EsJ,WAA+E,EAAA5I,gBAAA,CAA7ByI,QAAQ,CAACL,WAAW,iB;4BA/bhGnI,CAAA;;0BAAAA,CAAA;;wBAAAA,CAAA;;;oBAAAA,CAAA;;kBAAAA,CAAA;;gBAAAA,CAAA;;cAAAA,CAAA;;YAAAA,CAAA;;UAAAA,CAAA;YA0cQf,YAAA,CAgBiB0H,yBAAA;UA1dzB5H,OAAA,EAAAC,QAAA,CA2cU,MAAqB,CAArBC,YAAA,CAAqB2H,mBAAA,GACrB3H,YAAA,CAMQM,gBAAA;YALNC,KAAK,EAAC,eAAe;YACrBwB,OAAO,EAAC,MAAM;YACbvB,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,KAAA,CAAA+D,wBAAwB;;YA/c5C5E,OAAA,EAAAC,QAAA,CAgdW,MAEDU,MAAA,SAAAA,MAAA,QAldVI,gBAAA,CAgdW,MAED,E;YAldVE,CAAA;YAAAmB,EAAA;cAmdUlC,YAAA,CAMQM,gBAAA;YALNC,KAAK,EAAC,SAAS;YACfwB,OAAO,EAAC,MAAM;YACbvB,OAAK,EAAE+D,QAAA,CAAAoF;;YAtdpB7J,OAAA,EAAAC,QAAA,CAudW,MAEDU,MAAA,SAAAA,MAAA,QAzdVI,gBAAA,CAudW,QAED,E;YAzdVE,CAAA;YAAAmB,EAAA;;UAAAnB,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;uCA8dIoB,mBAAA,YAAe,EACfnC,YAAA,CAca4J,qBAAA;MA7ejBvG,UAAA,EAgee1C,KAAA,CAAAkJ,YAAY;MAhe3B,uBAAApJ,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAgeeC,KAAA,CAAAkJ,YAAY,GAAAnJ,MAAA;MACrBH,KAAK,EAAC;;MAIWuJ,OAAO,EAAA/J,QAAA,CACtB,MAKQ,CALRC,YAAA,CAKQM,gBAAA;QAJNyB,OAAO,EAAC,MAAM;QACbvB,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,KAAA,CAAAkJ,YAAY;;QAxe9B/J,OAAA,EAAAC,QAAA,CAyeS,MAEDU,MAAA,SAAAA,MAAA,QA3eRI,gBAAA,CAyeS,MAED,E;QA3eRE,CAAA;QAAAmB,EAAA;;MAAApC,OAAA,EAAAC,QAAA,CAmeM,MAAkB,CAnexBc,gBAAA,CAAAC,gBAAA,CAmeSH,KAAA,CAAAoJ,YAAY,IAAG,GAElB,gB;MAreNhJ,CAAA;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}