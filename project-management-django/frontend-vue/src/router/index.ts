/**
 * Vue Router 配置
 */

import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 路由组件
const Dashboard = () => import('@/views/Dashboard.vue')
const Login = () => import('@/views/Login.vue')
const Projects = () => import('@/views/Projects.vue')
const ProjectCreate = () => import('@/views/ProjectCreate.vue')
const ProjectDetail = () => import('@/views/ProjectDetail.vue')
const Tasks = () => import('@/views/Tasks.vue')
const TaskList = () => import('@/views/TaskList.vue')
const TaskCreate = () => import('@/views/TaskCreate.vue')
const TaskDetail = () => import('@/views/TaskDetail.vue')
const Teams = () => import('@/views/Teams.vue')
const Users = () => import('@/views/Users.vue')
const Settings = () => import('@/views/Settings.vue')
const Reports = () => import('@/views/Reports.vue')

// 路由配置
const routes = [
  {
    path: '/',
    name: 'Dashboard',
    component: Dashboard,
    meta: {
      requiresAuth: true,
      title: '仪表板'
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      requiresAuth: false,
      title: '登录'
    }
  },
  {
    path: '/projects',
    name: 'Projects',
    component: Projects,
    meta: {
      requiresAuth: true,
      title: '项目管理'
    }
  },
  {
    path: '/projects/create',
    name: 'ProjectCreate',
    component: ProjectCreate,
    meta: {
      requiresAuth: true,
      title: '创建项目'
    }
  },
  {
    path: '/projects/:id',
    name: 'ProjectDetail',
    component: ProjectDetail,
    meta: {
      requiresAuth: true,
      title: '项目详情'
    }
  },
  {
    path: '/projects/:projectId/tasks/:taskId',
    name: 'TaskDetail',
    component: TaskDetail,
    meta: {
      requiresAuth: true,
      title: '任务详情'
    }
  },
  {
    path: '/tasks',
    name: 'Tasks',
    component: TaskList,
    meta: {
      requiresAuth: true,
      title: '任务管理'
    }
  },
  {
    path: '/tasks/create',
    name: 'TaskCreate',
    component: TaskCreate,
    meta: {
      requiresAuth: true,
      title: '创建任务'
    }
  },
  {
    path: '/tasks/:id',
    name: 'TaskDetailPage',
    component: TaskDetail,
    meta: {
      requiresAuth: true,
      title: '任务详情'
    }
  },
  {
    path: '/teams',
    name: 'Teams',
    component: Teams,
    meta: {
      requiresAuth: true,
      title: '团队管理'
    }
  },
  {
    path: '/users',
    name: 'Users',
    component: Users,
    meta: {
      requiresAuth: true,
      title: '用户管理',
      requiresRole: ['admin', 'manager']
    }
  },
  {
    path: '/reports',
    name: 'Reports',
    component: Reports,
    meta: {
      requiresAuth: true,
      title: '报表分析'
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings,
    meta: {
      requiresAuth: true,
      title: '系统设置'
    }
  },
  {
    path: '/dashboard',
    redirect: '/'
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面未找到'
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 项目管理系统`
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    // 检查用户是否已登录
    if (!userStore.isAuthenticated) {
      // 尝试从本地存储恢复登录状态
      const restored = await userStore.checkAuth()
      if (!restored) {
        next({
          name: 'Login',
          query: { redirect: to.fullPath }
        })
        return
      }
    }

    // 检查角色权限
    if (to.meta.requiresRole) {
      const userRole = userStore.currentUser?.role
      const requiredRoles = to.meta.requiresRole as string[]

      if (!userRole || !requiredRoles.includes(userRole)) {
        next({
          name: 'Dashboard',
          query: { error: 'insufficient_permissions' }
        })
        return
      }
    }
  } else {
    // 如果已登录用户访问登录页，重定向到仪表板
    if (to.name === 'Login' && userStore.isAuthenticated) {
      next({ name: 'Dashboard' })
      return
    }
  }

  next()
})

// 路由错误处理
router.onError((error) => {
  console.error('Router error:', error)
})

export default router
