/**
 * 团队管理相关类型定义
 */

// 团队成员接口
export interface TeamMember {
  id: string
  user_id: string
  name: string
  email: string
  avatar?: string
  role: string
  department: string
  position: string
  phone?: string
  skills: string[]
  status: 'active' | 'inactive' | 'on_leave'
  join_date: string
  projects: string[]
  current_tasks: number
  completed_tasks: number
  workload: number // 工作负载百分比
  performance_score: number
  created_at: string
  updated_at: string
}

// 团队接口
export interface Team {
  id: string
  name: string
  description: string
  leader_id: string
  leader_name: string
  department: string
  members: TeamMember[]
  projects: string[]
  status: 'active' | 'inactive'
  created_at: string
  updated_at: string
}

// 部门接口
export interface Department {
  id: string
  name: string
  description: string
  manager_id: string
  manager_name: string
  teams: Team[]
  member_count: number
  budget: number
  status: 'active' | 'inactive'
  created_at: string
  updated_at: string
}

// 团队统计接口
export interface TeamStats {
  total_members: number
  active_members: number
  total_teams: number
  total_departments: number
  average_workload: number
  top_performers: TeamMember[]
  workload_distribution: {
    low: number
    medium: number
    high: number
    overloaded: number
  }
}

// 团队成员创建请求
export interface TeamMemberCreateRequest {
  name: string
  email: string
  role: string
  department: string
  position: string
  phone?: string
  skills: string[]
  projects?: string[]
}

// 团队成员更新请求
export interface TeamMemberUpdateRequest {
  name?: string
  email?: string
  role?: string
  department?: string
  position?: string
  phone?: string
  skills?: string[]
  status?: 'active' | 'inactive' | 'on_leave'
  projects?: string[]
}

// 团队创建请求
export interface TeamCreateRequest {
  name: string
  description: string
  leader_id: string
  department: string
  member_ids?: string[]
}

// 团队更新请求
export interface TeamUpdateRequest {
  name?: string
  description?: string
  leader_id?: string
  department?: string
  status?: 'active' | 'inactive'
}

// 团队筛选器
export interface TeamFilter {
  department?: string
  role?: string
  status?: string
  skills?: string[]
  workload_range?: [number, number]
}

// 分页信息
export interface TeamPagination {
  page: number
  pageSize: number
  total: number
  hasNext: boolean
  hasPrevious: boolean
}

// API响应类型
export interface TeamListResponse {
  results: Team[]
  count: number
  next: string | null
  previous: string | null
}

export interface TeamMemberListResponse {
  results: TeamMember[]
  count: number
  next: string | null
  previous: string | null
}
