/**
 * 系统设置相关类型定义
 */

// 用户个人设置
export interface UserSettings {
  id: string
  user_id: string
  // 个人信息
  display_name: string
  email: string
  phone?: string
  avatar?: string
  bio?: string
  department: string
  position: string
  
  // 界面设置
  theme: 'light' | 'dark' | 'auto'
  language: 'zh-CN' | 'en-US'
  timezone: string
  date_format: string
  time_format: '12h' | '24h'
  
  // 通知设置
  email_notifications: boolean
  push_notifications: boolean
  task_reminders: boolean
  project_updates: boolean
  team_mentions: boolean
  
  // 工作偏好
  default_project_view: 'list' | 'grid' | 'kanban'
  default_task_view: 'list' | 'grid'
  items_per_page: number
  auto_save: boolean
  
  // 隐私设置
  profile_visibility: 'public' | 'team' | 'private'
  activity_visibility: boolean
  
  created_at: string
  updated_at: string
}

// 系统配置
export interface SystemConfig {
  id: string
  
  // 基本信息
  system_name: string
  system_logo?: string
  system_description: string
  company_name: string
  company_logo?: string
  
  // 安全设置
  password_policy: {
    min_length: number
    require_uppercase: boolean
    require_lowercase: boolean
    require_numbers: boolean
    require_symbols: boolean
    expiry_days: number
  }
  
  session_timeout: number // 分钟
  max_login_attempts: number
  account_lockout_duration: number // 分钟
  
  // 功能设置
  features: {
    project_management: boolean
    task_management: boolean
    team_management: boolean
    reports: boolean
    file_upload: boolean
    comments: boolean
    time_tracking: boolean
  }
  
  // 邮件设置
  email_config: {
    smtp_host: string
    smtp_port: number
    smtp_username: string
    smtp_password: string
    from_email: string
    from_name: string
  }
  
  // 文件上传设置
  file_upload: {
    max_file_size: number // MB
    allowed_extensions: string[]
    storage_path: string
  }
  
  // 备份设置
  backup_config: {
    auto_backup: boolean
    backup_frequency: 'daily' | 'weekly' | 'monthly'
    backup_retention: number // 天数
    backup_path: string
  }
  
  created_at: string
  updated_at: string
}

// 通知设置
export interface NotificationSettings {
  id: string
  user_id: string
  
  // 邮件通知
  email_enabled: boolean
  email_frequency: 'immediate' | 'daily' | 'weekly'
  
  // 推送通知
  push_enabled: boolean
  
  // 通知类型
  notifications: {
    task_assigned: boolean
    task_due: boolean
    task_completed: boolean
    project_created: boolean
    project_updated: boolean
    team_invitation: boolean
    mention: boolean
    comment: boolean
  }
  
  // 免打扰时间
  quiet_hours: {
    enabled: boolean
    start_time: string
    end_time: string
    timezone: string
  }
  
  created_at: string
  updated_at: string
}

// 主题设置
export interface ThemeSettings {
  id: string
  name: string
  display_name: string
  
  // 颜色配置
  colors: {
    primary: string
    secondary: string
    accent: string
    success: string
    warning: string
    error: string
    info: string
    background: string
    surface: string
    text: string
  }
  
  // 医疗元素配置
  medical_theme: {
    enabled: boolean
    primary_color: string // 医疗蓝
    secondary_color: string // 健康绿
    accent_color: string // 关怀橙
    icons: 'medical' | 'standard'
    animations: boolean
  }
  
  // 字体设置
  typography: {
    font_family: string
    font_size: 'small' | 'medium' | 'large'
    line_height: number
  }
  
  // 布局设置
  layout: {
    sidebar_width: number
    header_height: number
    border_radius: number
    spacing: 'compact' | 'comfortable' | 'spacious'
  }
  
  is_default: boolean
  created_at: string
  updated_at: string
}

// 系统日志
export interface SystemLog {
  id: string
  level: 'info' | 'warning' | 'error' | 'debug'
  category: 'auth' | 'project' | 'task' | 'team' | 'system'
  message: string
  details?: any
  user_id?: string
  ip_address?: string
  user_agent?: string
  created_at: string
}

// 系统统计
export interface SystemStats {
  // 用户统计
  total_users: number
  active_users: number
  new_users_today: number
  
  // 项目统计
  total_projects: number
  active_projects: number
  completed_projects: number
  
  // 任务统计
  total_tasks: number
  completed_tasks: number
  overdue_tasks: number
  
  // 系统资源
  storage_used: number // MB
  storage_total: number // MB
  cpu_usage: number // 百分比
  memory_usage: number // 百分比
  
  // 性能指标
  average_response_time: number // 毫秒
  uptime: number // 秒
  error_rate: number // 百分比
  
  last_updated: string
}

// 设置更新请求
export interface SettingsUpdateRequest {
  user_settings?: Partial<UserSettings>
  notification_settings?: Partial<NotificationSettings>
  theme_settings?: Partial<ThemeSettings>
}

// 系统配置更新请求
export interface SystemConfigUpdateRequest {
  system_name?: string
  system_description?: string
  company_name?: string
  password_policy?: Partial<SystemConfig['password_policy']>
  session_timeout?: number
  features?: Partial<SystemConfig['features']>
  email_config?: Partial<SystemConfig['email_config']>
  file_upload?: Partial<SystemConfig['file_upload']>
  backup_config?: Partial<SystemConfig['backup_config']>
}

// 导入/导出配置
export interface ImportExportConfig {
  export_format: 'json' | 'csv' | 'excel'
  include_user_data: boolean
  include_project_data: boolean
  include_task_data: boolean
  include_team_data: boolean
  include_settings: boolean
  date_range?: {
    start: string
    end: string
  }
}

// 系统维护
export interface MaintenanceConfig {
  enabled: boolean
  start_time: string
  end_time: string
  message: string
  allowed_users: string[]
}

// API密钥管理
export interface ApiKey {
  id: string
  name: string
  key: string
  permissions: string[]
  expires_at?: string
  last_used?: string
  is_active: boolean
  created_by: string
  created_at: string
}

// 集成配置
export interface IntegrationConfig {
  id: string
  name: string
  type: 'webhook' | 'api' | 'oauth'
  config: any
  is_enabled: boolean
  created_at: string
  updated_at: string
}
