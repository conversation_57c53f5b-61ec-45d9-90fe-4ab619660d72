/**
 * 报表分析相关类型定义
 */

// 项目报表数据
export interface ProjectReport {
  id: string
  name: string
  status: string
  progress: number
  budget: number
  spent: number
  start_date: string
  end_date: string
  task_count: number
  completed_task_count: number
  team_member_count: number
  days_remaining: number
  is_overdue: boolean
  efficiency_score: number
}

// 任务报表数据
export interface TaskReport {
  id: string
  title: string
  project_name: string
  status: string
  priority: string
  assignee_name: string
  estimated_hours: number
  actual_hours: number
  progress: number
  start_date: string
  due_date: string
  completed_date?: string
  is_overdue: boolean
  efficiency_ratio: number
}

// 团队绩效报表
export interface TeamPerformanceReport {
  member_id: string
  member_name: string
  department: string
  role: string
  total_tasks: number
  completed_tasks: number
  overdue_tasks: number
  average_completion_time: number
  workload: number
  performance_score: number
  efficiency_trend: number[]
}

// 时间统计报表
export interface TimeReport {
  date: string
  planned_hours: number
  actual_hours: number
  efficiency: number
  projects: {
    project_id: string
    project_name: string
    hours: number
  }[]
}

// 预算报表
export interface BudgetReport {
  project_id: string
  project_name: string
  budget: number
  spent: number
  remaining: number
  utilization_rate: number
  cost_per_task: number
  projected_total: number
  variance: number
}

// 图表数据
export interface ChartData {
  labels: string[]
  datasets: {
    label: string
    data: number[]
    backgroundColor?: string | string[]
    borderColor?: string | string[]
    borderWidth?: number
    fill?: boolean
  }[]
}

// 仪表板统计
export interface DashboardStats {
  total_projects: number
  active_projects: number
  completed_projects: number
  overdue_projects: number
  total_tasks: number
  completed_tasks: number
  overdue_tasks: number
  total_members: number
  active_members: number
  total_budget: number
  spent_budget: number
  average_project_progress: number
  average_team_workload: number
}

// 趋势数据
export interface TrendData {
  period: string
  projects_created: number
  projects_completed: number
  tasks_created: number
  tasks_completed: number
  budget_allocated: number
  budget_spent: number
  team_productivity: number
}

// 报表筛选器
export interface ReportFilter {
  date_range?: {
    start: string
    end: string
  }
  project_ids?: string[]
  department?: string
  status?: string[]
  priority?: string[]
  assignee_ids?: string[]
}

// 报表配置
export interface ReportConfig {
  type: 'project' | 'task' | 'team' | 'time' | 'budget' | 'dashboard'
  title: string
  description: string
  filters: ReportFilter
  chart_type?: 'line' | 'bar' | 'pie' | 'doughnut' | 'area'
  export_format?: 'pdf' | 'excel' | 'csv'
}

// 导出选项
export interface ExportOptions {
  format: 'pdf' | 'excel' | 'csv'
  include_charts: boolean
  include_raw_data: boolean
  date_range?: {
    start: string
    end: string
  }
}

// 报表响应
export interface ReportResponse<T> {
  data: T[]
  summary: {
    total_count: number
    filtered_count: number
    generated_at: string
  }
  charts?: {
    [key: string]: ChartData
  }
}

// 项目状态分布
export interface ProjectStatusDistribution {
  planning: number
  in_progress: number
  completed: number
  paused: number
  cancelled: number
}

// 任务优先级分布
export interface TaskPriorityDistribution {
  low: number
  medium: number
  high: number
  urgent: number
}

// 工作负载分布
export interface WorkloadDistribution {
  underloaded: number  // < 50%
  normal: number       // 50-80%
  overloaded: number   // 80-100%
  critical: number     // > 100%
}

// 效率指标
export interface EfficiencyMetrics {
  project_completion_rate: number
  task_completion_rate: number
  on_time_delivery_rate: number
  budget_utilization_rate: number
  team_productivity_score: number
  average_task_cycle_time: number
}

// 预测数据
export interface ForecastData {
  project_completion_forecast: {
    project_id: string
    project_name: string
    current_progress: number
    predicted_completion_date: string
    confidence_level: number
  }[]
  budget_forecast: {
    project_id: string
    project_name: string
    current_spent: number
    predicted_total_cost: number
    budget_variance: number
  }[]
  resource_demand_forecast: {
    department: string
    current_workload: number
    predicted_workload: number
    recommended_headcount: number
  }[]
}
