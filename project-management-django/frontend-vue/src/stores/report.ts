/**
 * 报表分析 Store
 */

import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { useProjectStore } from './project'
import { useTaskStore } from './task'
import { useTeamStore } from './team'
import type {
  ProjectReport,
  TaskReport,
  TeamPerformanceReport,
  DashboardStats,
  TrendData,
  ChartData,
  ReportFilter,
  ProjectStatusDistribution,
  TaskPriorityDistribution,
  WorkloadDistribution,
  EfficiencyMetrics
} from '@/types/report'

export const useReportStore = defineStore('report', () => {
  // 状态
  const loading = ref(false)
  const error = ref<string | null>(null)
  const filters = ref<ReportFilter>({})
  const dashboardStats = ref<DashboardStats | null>(null)
  const trendData = ref<TrendData[]>([])

  // 依赖的其他stores
  const projectStore = useProjectStore()
  const taskStore = useTaskStore()
  const teamStore = useTeamStore()

  // 计算属性 - 项目报表数据
  const projectReports = computed((): ProjectReport[] => {
    return projectStore.projects.map(project => {
      const projectTasks = taskStore.tasks.filter(task => task.project_id === project.id)
      const completedTasks = projectTasks.filter(task => task.status === 'completed')
      const daysRemaining = Math.ceil((new Date(project.end_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
      
      return {
        id: project.id,
        name: project.name,
        status: project.status,
        progress: project.progress,
        budget: project.budget,
        spent: project.spent,
        start_date: project.start_date,
        end_date: project.end_date,
        task_count: projectTasks.length,
        completed_task_count: completedTasks.length,
        team_member_count: project.team_member_count,
        days_remaining: daysRemaining,
        is_overdue: daysRemaining < 0 && project.status !== 'completed',
        efficiency_score: projectTasks.length > 0 ? Math.round((completedTasks.length / projectTasks.length) * 100) : 0
      }
    })
  })

  // 计算属性 - 任务报表数据
  const taskReports = computed((): TaskReport[] => {
    return taskStore.tasks.map(task => {
      const project = projectStore.projects.find(p => p.id === task.project_id)
      const member = teamStore.members.find(m => m.id === task.assignee_id)
      const isOverdue = new Date(task.due_date) < new Date() && task.status !== 'completed'
      const efficiencyRatio = task.estimated_hours > 0 ? task.actual_hours / task.estimated_hours : 1
      
      return {
        id: task.id,
        title: task.title,
        project_name: project?.name || '未知项目',
        status: task.status,
        priority: task.priority,
        assignee_name: member?.name || '未分配',
        estimated_hours: task.estimated_hours,
        actual_hours: task.actual_hours,
        progress: task.progress,
        start_date: task.start_date,
        due_date: task.due_date,
        completed_date: task.status === 'completed' ? task.updated_at : undefined,
        is_overdue: isOverdue,
        efficiency_ratio: efficiencyRatio
      }
    })
  })

  // 计算属性 - 团队绩效报表
  const teamPerformanceReports = computed((): TeamPerformanceReport[] => {
    return teamStore.members.map(member => {
      const memberTasks = taskStore.tasks.filter(task => task.assignee_id === member.id)
      const completedTasks = memberTasks.filter(task => task.status === 'completed')
      const overdueTasks = memberTasks.filter(task => 
        new Date(task.due_date) < new Date() && task.status !== 'completed'
      )
      
      // 模拟效率趋势数据
      const efficiencyTrend = Array.from({ length: 7 }, (_, i) => 
        Math.round(70 + Math.random() * 30 + (member.performance_score - 80) / 2)
      )
      
      return {
        member_id: member.id,
        member_name: member.name,
        department: member.department,
        role: member.role,
        total_tasks: memberTasks.length,
        completed_tasks: completedTasks.length,
        overdue_tasks: overdueTasks.length,
        average_completion_time: completedTasks.length > 0 ? 
          completedTasks.reduce((sum, task) => sum + task.actual_hours, 0) / completedTasks.length : 0,
        workload: member.workload,
        performance_score: member.performance_score,
        efficiency_trend: efficiencyTrend
      }
    })
  })

  // 计算属性 - 仪表板统计
  const computedDashboardStats = computed((): DashboardStats => {
    const projects = projectStore.projects
    const tasks = taskStore.tasks
    const members = teamStore.members
    
    const activeProjects = projects.filter(p => p.status === 'in_progress')
    const completedProjects = projects.filter(p => p.status === 'completed')
    const overdueProjects = projects.filter(p => {
      const daysRemaining = Math.ceil((new Date(p.end_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
      return daysRemaining < 0 && p.status !== 'completed'
    })
    
    const completedTasks = tasks.filter(t => t.status === 'completed')
    const overdueTasks = tasks.filter(t => 
      new Date(t.due_date) < new Date() && t.status !== 'completed'
    )
    
    const activeMembers = members.filter(m => m.status === 'active')
    const totalBudget = projects.reduce((sum, p) => sum + p.budget, 0)
    const spentBudget = projects.reduce((sum, p) => sum + p.spent, 0)
    
    const avgProgress = projects.length > 0 ? 
      projects.reduce((sum, p) => sum + p.progress, 0) / projects.length : 0
    
    const avgWorkload = members.length > 0 ? 
      members.reduce((sum, m) => sum + m.workload, 0) / members.length : 0
    
    return {
      total_projects: projects.length,
      active_projects: activeProjects.length,
      completed_projects: completedProjects.length,
      overdue_projects: overdueProjects.length,
      total_tasks: tasks.length,
      completed_tasks: completedTasks.length,
      overdue_tasks: overdueTasks.length,
      total_members: members.length,
      active_members: activeMembers.length,
      total_budget: totalBudget,
      spent_budget: spentBudget,
      average_project_progress: Math.round(avgProgress),
      average_team_workload: Math.round(avgWorkload)
    }
  })

  // 计算属性 - 项目状态分布
  const projectStatusDistribution = computed((): ProjectStatusDistribution => {
    const projects = projectStore.projects
    return {
      planning: projects.filter(p => p.status === 'planning').length,
      in_progress: projects.filter(p => p.status === 'in_progress').length,
      completed: projects.filter(p => p.status === 'completed').length,
      paused: projects.filter(p => p.status === 'paused').length,
      cancelled: projects.filter(p => p.status === 'cancelled').length
    }
  })

  // 计算属性 - 任务优先级分布
  const taskPriorityDistribution = computed((): TaskPriorityDistribution => {
    const tasks = taskStore.tasks
    return {
      low: tasks.filter(t => t.priority === 'low').length,
      medium: tasks.filter(t => t.priority === 'medium').length,
      high: tasks.filter(t => t.priority === 'high').length,
      urgent: tasks.filter(t => t.priority === 'urgent').length
    }
  })

  // 计算属性 - 工作负载分布
  const workloadDistribution = computed((): WorkloadDistribution => {
    const members = teamStore.members
    return {
      underloaded: members.filter(m => m.workload < 50).length,
      normal: members.filter(m => m.workload >= 50 && m.workload < 80).length,
      overloaded: members.filter(m => m.workload >= 80 && m.workload <= 100).length,
      critical: members.filter(m => m.workload > 100).length
    }
  })

  // 计算属性 - 效率指标
  const efficiencyMetrics = computed((): EfficiencyMetrics => {
    const projects = projectStore.projects
    const tasks = taskStore.tasks
    
    const completedProjects = projects.filter(p => p.status === 'completed')
    const completedTasks = tasks.filter(t => t.status === 'completed')
    const onTimeTasks = tasks.filter(t => 
      t.status === 'completed' && new Date(t.updated_at) <= new Date(t.due_date)
    )
    
    const totalBudget = projects.reduce((sum, p) => sum + p.budget, 0)
    const spentBudget = projects.reduce((sum, p) => sum + p.spent, 0)
    
    const avgCycleTime = completedTasks.length > 0 ? 
      completedTasks.reduce((sum, t) => sum + t.actual_hours, 0) / completedTasks.length : 0
    
    return {
      project_completion_rate: projects.length > 0 ? (completedProjects.length / projects.length) * 100 : 0,
      task_completion_rate: tasks.length > 0 ? (completedTasks.length / tasks.length) * 100 : 0,
      on_time_delivery_rate: tasks.length > 0 ? (onTimeTasks.length / tasks.length) * 100 : 0,
      budget_utilization_rate: totalBudget > 0 ? (spentBudget / totalBudget) * 100 : 0,
      team_productivity_score: teamStore.averageWorkload,
      average_task_cycle_time: avgCycleTime
    }
  })

  // 方法 - 生成图表数据
  const generateProjectStatusChart = (): ChartData => {
    const distribution = projectStatusDistribution.value
    return {
      labels: ['规划中', '进行中', '已完成', '已暂停', '已取消'],
      datasets: [{
        label: '项目数量',
        data: [
          distribution.planning,
          distribution.in_progress,
          distribution.completed,
          distribution.paused,
          distribution.cancelled
        ],
        backgroundColor: [
          '#2196F3',
          '#FF9800',
          '#4CAF50',
          '#9E9E9E',
          '#F44336'
        ]
      }]
    }
  }

  const generateTaskPriorityChart = (): ChartData => {
    const distribution = taskPriorityDistribution.value
    return {
      labels: ['低', '中', '高', '紧急'],
      datasets: [{
        label: '任务数量',
        data: [
          distribution.low,
          distribution.medium,
          distribution.high,
          distribution.urgent
        ],
        backgroundColor: [
          '#4CAF50',
          '#FF9800',
          '#F44336',
          '#9C27B0'
        ]
      }]
    }
  }

  const generateWorkloadChart = (): ChartData => {
    const distribution = workloadDistribution.value
    return {
      labels: ['负载不足', '正常', '超负荷', '严重超负荷'],
      datasets: [{
        label: '人员数量',
        data: [
          distribution.underloaded,
          distribution.normal,
          distribution.overloaded,
          distribution.critical
        ],
        backgroundColor: [
          '#4CAF50',
          '#2196F3',
          '#FF9800',
          '#F44336'
        ]
      }]
    }
  }

  const generateTrendChart = (): ChartData => {
    // 生成最近7天的趋势数据
    const labels = Array.from({ length: 7 }, (_, i) => {
      const date = new Date()
      date.setDate(date.getDate() - (6 - i))
      return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
    })
    
    return {
      labels,
      datasets: [
        {
          label: '项目进度',
          data: Array.from({ length: 7 }, () => Math.round(60 + Math.random() * 30)),
          borderColor: '#2196F3',
          backgroundColor: 'rgba(33, 150, 243, 0.1)',
          fill: true
        },
        {
          label: '任务完成率',
          data: Array.from({ length: 7 }, () => Math.round(70 + Math.random() * 25)),
          borderColor: '#4CAF50',
          backgroundColor: 'rgba(76, 175, 80, 0.1)',
          fill: true
        }
      ]
    }
  }

  // 方法 - 导出报表
  const exportReport = async (type: string, format: string) => {
    loading.value = true
    try {
      // 模拟导出功能
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 这里应该调用实际的导出API
      const filename = `${type}_report_${new Date().toISOString().split('T')[0]}.${format}`
      
      // 模拟下载
      const link = document.createElement('a')
      link.href = '#'
      link.download = filename
      link.click()
      
      return { success: true, filename }
    } catch (err: any) {
      error.value = err.message || '导出失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    // 状态
    loading: readonly(loading),
    error: readonly(error),
    filters: readonly(filters),
    dashboardStats: readonly(dashboardStats),
    trendData: readonly(trendData),

    // 计算属性
    projectReports,
    taskReports,
    teamPerformanceReports,
    computedDashboardStats,
    projectStatusDistribution,
    taskPriorityDistribution,
    workloadDistribution,
    efficiencyMetrics,

    // 方法
    generateProjectStatusChart,
    generateTaskPriorityChart,
    generateWorkloadChart,
    generateTrendChart,
    exportReport
  }
})
