/**
 * 通知状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 通知类型枚举
export type NotificationType = 'success' | 'error' | 'warning' | 'info'

// 通知接口
export interface Notification {
  id: string
  type: NotificationType
  title: string
  message: string
  duration?: number // 显示时长（毫秒），0表示不自动关闭
  persistent?: boolean // 是否持久显示
  actions?: NotificationAction[]
  timestamp: number
  read?: boolean
}

// 通知操作接口
export interface NotificationAction {
  label: string
  action: () => void
  color?: string
  variant?: 'text' | 'outlined' | 'flat' | 'elevated'
}

// 系统通知接口
export interface SystemNotification {
  id: string
  type: NotificationType
  title: string
  message: string
  category: 'task' | 'project' | 'system' | 'user' | 'team'

  // 关联信息
  related_object_type?: string
  related_object_id?: string
  related_object_name?: string

  // 发送者信息
  sender_id?: string
  sender_name?: string
  sender_avatar?: string

  // 接收者信息
  recipient_id: string

  // 状态
  is_read: boolean
  is_archived: boolean

  // 时间
  created_at: string
  read_at?: string

  // 额外数据
  metadata?: Record<string, any>
}

// 通知设置接口
export interface NotificationSettings {
  // 应用内通知
  show_toast: boolean
  toast_duration: number
  toast_position: 'top' | 'bottom' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'

  // 声音设置
  enable_sound: boolean
  sound_volume: number

  // 桌面通知
  enable_desktop: boolean
  desktop_permission: 'default' | 'granted' | 'denied'

  // 过滤设置
  categories: Record<string, boolean>
  priorities: Record<NotificationType, boolean>
}

export const useNotificationStore = defineStore('notification', () => {
  // 状态
  const notifications = ref<Notification[]>([])
  const systemNotifications = ref<SystemNotification[]>([])
  const settings = ref<NotificationSettings>({
    show_toast: true,
    toast_duration: 5000,
    toast_position: 'top-right',
    enable_sound: true,
    sound_volume: 0.5,
    enable_desktop: false,
    desktop_permission: 'default',
    categories: {
      task: true,
      project: true,
      system: true,
      user: true,
      team: true
    },
    priorities: {
      success: true,
      error: true,
      warning: true,
      info: true
    }
  })

  const loading = ref(false)
  const errorState = ref<string | null>(null)

  // 计算属性
  const unreadCount = computed(() => {
    return systemNotifications.value.filter(n => !n.is_read).length
  })

  const unreadNotifications = computed(() => {
    return systemNotifications.value.filter(n => !n.is_read)
  })

  const recentNotifications = computed(() => {
    return systemNotifications.value
      .filter(n => !n.is_archived)
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, 10)
  })

  const notificationsByCategory = computed(() => {
    return systemNotifications.value.reduce((acc, notification) => {
      const category = notification.category
      if (!acc[category]) {
        acc[category] = []
      }
      acc[category].push(notification)
      return acc
    }, {} as Record<string, SystemNotification[]>)
  })

  // 方法
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // Toast通知方法
  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp'>) => {
    const newNotification: Notification = {
      id: generateId(),
      timestamp: Date.now(),
      duration: 5000,
      ...notification
    }

    notifications.value.push(newNotification)

    // 播放声音
    if (settings.value.enable_sound) {
      playNotificationSound()
    }

    // 显示桌面通知
    if (settings.value.enable_desktop && settings.value.desktop_permission === 'granted') {
      showDesktopNotification(newNotification)
    }

    // 自动移除
    if (newNotification.duration && newNotification.duration > 0) {
      setTimeout(() => {
        removeNotification(newNotification.id)
      }, newNotification.duration)
    }

    return newNotification.id
  }

  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  const clearAllNotifications = () => {
    notifications.value = []
  }

  // 便捷方法
  const success = (title: string, message: string, options?: Partial<Notification>) => {
    return addNotification({
      type: 'success',
      title,
      message,
      ...options
    })
  }

  const errorNotification = (title: string, message: string, options?: Partial<Notification>) => {
    return addNotification({
      type: 'error',
      title,
      message,
      duration: 0, // 错误通知默认不自动关闭
      ...options
    })
  }

  const warning = (title: string, message: string, options?: Partial<Notification>) => {
    return addNotification({
      type: 'warning',
      title,
      message,
      ...options
    })
  }

  const info = (title: string, message: string, options?: Partial<Notification>) => {
    return addNotification({
      type: 'info',
      title,
      message,
      ...options
    })
  }

  // 系统通知方法
  const fetchSystemNotifications = async () => {
    loading.value = true
    try {
      // TODO: 实现API调用
      // const response = await api.get('/notifications/')
      // systemNotifications.value = response.data.results
    } catch (err) {
      console.error('Failed to fetch notifications:', err)
      errorState.value = '获取通知失败'
    } finally {
      loading.value = false
    }
  }

  const markAsRead = async (notificationId: string) => {
    try {
      // TODO: 实现API调用
      // await api.patch(`/notifications/${notificationId}/`, { is_read: true })

      const notification = systemNotifications.value.find(n => n.id === notificationId)
      if (notification) {
        notification.is_read = true
        notification.read_at = new Date().toISOString()
      }
    } catch (err) {
      console.error('Failed to mark notification as read:', err)
    }
  }

  const markAllAsRead = async () => {
    try {
      // TODO: 实现API调用
      // await api.post('/notifications/mark-all-read/')

      systemNotifications.value.forEach(notification => {
        if (!notification.is_read) {
          notification.is_read = true
          notification.read_at = new Date().toISOString()
        }
      })
    } catch (err) {
      console.error('Failed to mark all notifications as read:', err)
    }
  }

  const archiveNotification = async (notificationId: string) => {
    try {
      // TODO: 实现API调用
      // await api.patch(`/notifications/${notificationId}/`, { is_archived: true })

      const notification = systemNotifications.value.find(n => n.id === notificationId)
      if (notification) {
        notification.is_archived = true
      }
    } catch (err) {
      console.error('Failed to archive notification:', err)
    }
  }

  const deleteNotification = async (notificationId: string) => {
    try {
      // TODO: 实现API调用
      // await api.delete(`/notifications/${notificationId}/`)

      const index = systemNotifications.value.findIndex(n => n.id === notificationId)
      if (index > -1) {
        systemNotifications.value.splice(index, 1)
      }
    } catch (err) {
      console.error('Failed to delete notification:', err)
    }
  }

  // 设置方法
  const updateSettings = (newSettings: Partial<NotificationSettings>) => {
    settings.value = { ...settings.value, ...newSettings }
    saveSettings()
  }

  const saveSettings = () => {
    localStorage.setItem('notification-settings', JSON.stringify(settings.value))
  }

  const loadSettings = () => {
    const saved = localStorage.getItem('notification-settings')
    if (saved) {
      try {
        settings.value = { ...settings.value, ...JSON.parse(saved) }
      } catch (err) {
        console.error('Failed to load notification settings:', err)
      }
    }
  }

  // 桌面通知
  const requestDesktopPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      settings.value.desktop_permission = permission
      saveSettings()
      return permission
    }
    return 'denied'
  }

  const showDesktopNotification = (notification: Notification) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        tag: notification.id
      })
    }
  }

  // 声音通知
  const playNotificationSound = () => {
    try {
      const audio = new Audio('/sounds/notification.mp3')
      audio.volume = settings.value.sound_volume
      audio.play().catch(() => {
        // 忽略播放失败的错误
      })
    } catch (err) {
      // 忽略音频播放错误
    }
  }

  // WebSocket连接（实时通知）
  const connectWebSocket = () => {
    // TODO: 实现WebSocket连接
    // const ws = new WebSocket('ws://localhost:8000/ws/notifications/')
    // ws.onmessage = (event) => {
    //   const data = JSON.parse(event.data)
    //   if (data.type === 'notification') {
    //     systemNotifications.value.unshift(data.notification)
    //     addNotification({
    //       type: data.notification.type,
    //       title: data.notification.title,
    //       message: data.notification.message
    //     })
    //   }
    // }
  }

  // 初始化
  const init = () => {
    loadSettings()
    fetchSystemNotifications()
    connectWebSocket()
  }

  return {
    // 状态
    notifications,
    systemNotifications,
    settings,
    loading,
    error: errorState,

    // 计算属性
    unreadCount,
    unreadNotifications,
    recentNotifications,
    notificationsByCategory,

    // Toast通知方法
    addNotification,
    removeNotification,
    clearAllNotifications,
    success,
    error: errorNotification,
    warning,
    info,

    // 系统通知方法
    fetchSystemNotifications,
    markAsRead,
    markAllAsRead,
    archiveNotification,
    deleteNotification,

    // 设置方法
    updateSettings,
    saveSettings,
    loadSettings,

    // 桌面通知
    requestDesktopPermission,
    showDesktopNotification,

    // 其他方法
    playNotificationSound,
    connectWebSocket,
    init
  }
})
