/**
 * 系统设置 Store
 */

import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import type {
  UserSettings,
  SystemConfig,
  NotificationSettings,
  ThemeSettings,
  SystemStats,
  SystemLog,
  SettingsUpdateRequest
} from '@/types/settings'

export const useSettingsStore = defineStore('settings', () => {
  // 状态
  const userSettings = ref<UserSettings | null>(null)
  const systemConfig = ref<SystemConfig | null>(null)
  const notificationSettings = ref<NotificationSettings | null>(null)
  const themeSettings = ref<ThemeSettings | null>(null)
  const systemStats = ref<SystemStats | null>(null)
  const systemLogs = ref<SystemLog[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const currentTheme = computed(() => {
    return themeSettings.value || getDefaultTheme()
  })

  const isDarkMode = computed(() => {
    if (!userSettings.value) return false
    if (userSettings.value.theme === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches
    }
    return userSettings.value.theme === 'dark'
  })

  const isMedicalThemeEnabled = computed(() => {
    return currentTheme.value.medical_theme.enabled
  })

  // 默认主题配置
  const getDefaultTheme = (): ThemeSettings => ({
    id: 'default',
    name: 'medical-google',
    display_name: 'Google医疗主题',
    colors: {
      primary: '#1976D2', // Google蓝
      secondary: '#4CAF50', // 健康绿
      accent: '#FF9800', // 关怀橙
      success: '#4CAF50',
      warning: '#FF9800',
      error: '#F44336',
      info: '#2196F3',
      background: '#FAFAFA',
      surface: '#FFFFFF',
      text: '#212121'
    },
    medical_theme: {
      enabled: true,
      primary_color: '#1976D2', // 医疗蓝
      secondary_color: '#4CAF50', // 健康绿
      accent_color: '#FF9800', // 关怀橙
      icons: 'medical',
      animations: true
    },
    typography: {
      font_family: 'Roboto, sans-serif',
      font_size: 'medium',
      line_height: 1.5
    },
    layout: {
      sidebar_width: 280,
      header_height: 64,
      border_radius: 8,
      spacing: 'comfortable'
    },
    is_default: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  })

  // 操作方法
  const fetchUserSettings = async () => {
    loading.value = true
    error.value = null

    try {
      // 首先尝试从localStorage加载数据
      const savedSettings = localStorage.getItem('user_settings')
      if (savedSettings) {
        userSettings.value = JSON.parse(savedSettings)
        loading.value = false
        return userSettings.value
      }

      // 如果没有保存的数据，使用默认设置
      const defaultSettings: UserSettings = {
        id: 'user-settings-1',
        user_id: '1',
        display_name: '管理员',
        email: '<EMAIL>',
        phone: '13800138000',
        avatar: '',
        bio: '项目管理系统管理员',
        department: '技术部',
        position: '系统管理员',
        theme: 'light',
        language: 'zh-CN',
        timezone: 'Asia/Shanghai',
        date_format: 'YYYY-MM-DD',
        time_format: '24h',
        email_notifications: true,
        push_notifications: true,
        task_reminders: true,
        project_updates: true,
        team_mentions: true,
        default_project_view: 'grid',
        default_task_view: 'list',
        items_per_page: 20,
        auto_save: true,
        profile_visibility: 'team',
        activity_visibility: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      userSettings.value = defaultSettings
      localStorage.setItem('user_settings', JSON.stringify(defaultSettings))
      return defaultSettings
    } catch (err: any) {
      error.value = err.message || '获取用户设置失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchSystemConfig = async () => {
    loading.value = true
    error.value = null

    try {
      const savedConfig = localStorage.getItem('system_config')
      if (savedConfig) {
        systemConfig.value = JSON.parse(savedConfig)
        loading.value = false
        return systemConfig.value
      }

      const defaultConfig: SystemConfig = {
        id: 'system-config-1',
        system_name: '医疗项目管理系统',
        system_logo: '',
        system_description: '专业的医疗项目管理解决方案',
        company_name: '医疗科技有限公司',
        company_logo: '',
        password_policy: {
          min_length: 8,
          require_uppercase: true,
          require_lowercase: true,
          require_numbers: true,
          require_symbols: false,
          expiry_days: 90
        },
        session_timeout: 480, // 8小时
        max_login_attempts: 5,
        account_lockout_duration: 30,
        features: {
          project_management: true,
          task_management: true,
          team_management: true,
          reports: true,
          file_upload: true,
          comments: true,
          time_tracking: true
        },
        email_config: {
          smtp_host: 'smtp.example.com',
          smtp_port: 587,
          smtp_username: '<EMAIL>',
          smtp_password: '',
          from_email: '<EMAIL>',
          from_name: '医疗项目管理系统'
        },
        file_upload: {
          max_file_size: 50,
          allowed_extensions: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx'],
          storage_path: '/uploads'
        },
        backup_config: {
          auto_backup: true,
          backup_frequency: 'daily',
          backup_retention: 30,
          backup_path: '/backups'
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      systemConfig.value = defaultConfig
      localStorage.setItem('system_config', JSON.stringify(defaultConfig))
      return defaultConfig
    } catch (err: any) {
      error.value = err.message || '获取系统配置失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchThemeSettings = async () => {
    loading.value = true
    error.value = null

    try {
      const savedTheme = localStorage.getItem('theme_settings')
      if (savedTheme) {
        themeSettings.value = JSON.parse(savedTheme)
        loading.value = false
        return themeSettings.value
      }

      const defaultTheme = getDefaultTheme()
      themeSettings.value = defaultTheme
      localStorage.setItem('theme_settings', JSON.stringify(defaultTheme))
      return defaultTheme
    } catch (err: any) {
      error.value = err.message || '获取主题设置失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchSystemStats = async () => {
    loading.value = true
    error.value = null

    try {
      // 模拟系统统计数据
      const stats: SystemStats = {
        total_users: 25,
        active_users: 18,
        new_users_today: 2,
        total_projects: 12,
        active_projects: 8,
        completed_projects: 4,
        total_tasks: 156,
        completed_tasks: 89,
        overdue_tasks: 12,
        storage_used: 2048, // 2GB
        storage_total: 10240, // 10GB
        cpu_usage: 35,
        memory_usage: 68,
        average_response_time: 245,
        uptime: 2592000, // 30天
        error_rate: 0.5,
        last_updated: new Date().toISOString()
      }

      systemStats.value = stats
      return stats
    } catch (err: any) {
      error.value = err.message || '获取系统统计失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateUserSettings = async (updates: Partial<UserSettings>) => {
    loading.value = true
    error.value = null

    try {
      if (!userSettings.value) {
        throw new Error('用户设置未加载')
      }

      const updatedSettings = {
        ...userSettings.value,
        ...updates,
        updated_at: new Date().toISOString()
      }

      userSettings.value = updatedSettings
      localStorage.setItem('user_settings', JSON.stringify(updatedSettings))
      return updatedSettings
    } catch (err: any) {
      error.value = err.message || '更新用户设置失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateSystemConfig = async (updates: Partial<SystemConfig>) => {
    loading.value = true
    error.value = null

    try {
      if (!systemConfig.value) {
        throw new Error('系统配置未加载')
      }

      const updatedConfig = {
        ...systemConfig.value,
        ...updates,
        updated_at: new Date().toISOString()
      }

      systemConfig.value = updatedConfig
      localStorage.setItem('system_config', JSON.stringify(updatedConfig))
      return updatedConfig
    } catch (err: any) {
      error.value = err.message || '更新系统配置失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateThemeSettings = async (updates: Partial<ThemeSettings>) => {
    loading.value = true
    error.value = null

    try {
      if (!themeSettings.value) {
        throw new Error('主题设置未加载')
      }

      const updatedTheme = {
        ...themeSettings.value,
        ...updates,
        updated_at: new Date().toISOString()
      }

      themeSettings.value = updatedTheme
      localStorage.setItem('theme_settings', JSON.stringify(updatedTheme))
      
      // 应用主题到页面
      applyTheme(updatedTheme)
      
      return updatedTheme
    } catch (err: any) {
      error.value = err.message || '更新主题设置失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const applyTheme = (theme: ThemeSettings) => {
    const root = document.documentElement
    
    // 应用颜色变量
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value)
    })
    
    // 应用医疗主题
    if (theme.medical_theme.enabled) {
      root.style.setProperty('--medical-primary', theme.medical_theme.primary_color)
      root.style.setProperty('--medical-secondary', theme.medical_theme.secondary_color)
      root.style.setProperty('--medical-accent', theme.medical_theme.accent_color)
      root.classList.add('medical-theme')
    } else {
      root.classList.remove('medical-theme')
    }
    
    // 应用布局设置
    root.style.setProperty('--sidebar-width', `${theme.layout.sidebar_width}px`)
    root.style.setProperty('--header-height', `${theme.layout.header_height}px`)
    root.style.setProperty('--border-radius', `${theme.layout.border_radius}px`)
  }

  const resetToDefaults = async () => {
    loading.value = true
    error.value = null

    try {
      // 清除localStorage中的设置
      localStorage.removeItem('user_settings')
      localStorage.removeItem('system_config')
      localStorage.removeItem('theme_settings')

      // 重新加载默认设置
      await Promise.all([
        fetchUserSettings(),
        fetchSystemConfig(),
        fetchThemeSettings()
      ])

      return true
    } catch (err: any) {
      error.value = err.message || '重置设置失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    // 状态
    userSettings: readonly(userSettings),
    systemConfig: readonly(systemConfig),
    notificationSettings: readonly(notificationSettings),
    themeSettings: readonly(themeSettings),
    systemStats: readonly(systemStats),
    systemLogs: readonly(systemLogs),
    loading: readonly(loading),
    error: readonly(error),

    // 计算属性
    currentTheme,
    isDarkMode,
    isMedicalThemeEnabled,

    // 方法
    fetchUserSettings,
    fetchSystemConfig,
    fetchThemeSettings,
    fetchSystemStats,
    updateUserSettings,
    updateSystemConfig,
    updateThemeSettings,
    applyTheme,
    resetToDefaults
  }
})
