/**
 * 团队管理 Store
 */

import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import type {
  Team,
  TeamMember,
  Department,
  TeamStats,
  TeamCreateRequest,
  TeamUpdateRequest,
  TeamMemberCreateRequest,
  TeamMemberUpdateRequest,
  TeamFilter,
  TeamPagination
} from '@/types/team'

export const useTeamStore = defineStore('team', () => {
  // 状态
  const teams = ref<Team[]>([])
  const members = ref<TeamMember[]>([])
  const departments = ref<Department[]>([])
  const currentTeam = ref<Team | null>(null)
  const currentMember = ref<TeamMember | null>(null)
  const stats = ref<TeamStats | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const pagination = ref<TeamPagination>({
    page: 1,
    pageSize: 20,
    total: 0,
    hasNext: false,
    hasPrevious: false
  })
  const filters = ref<TeamFilter>({})

  // 计算属性
  const activeMembers = computed(() => 
    members.value.filter(member => member.status === 'active')
  )

  const inactiveMembers = computed(() => 
    members.value.filter(member => member.status === 'inactive')
  )

  const onLeaveMembers = computed(() => 
    members.value.filter(member => member.status === 'on_leave')
  )

  const membersByDepartment = computed(() => {
    const grouped: Record<string, TeamMember[]> = {}
    members.value.forEach(member => {
      if (!grouped[member.department]) {
        grouped[member.department] = []
      }
      grouped[member.department].push(member)
    })
    return grouped
  })

  const membersByRole = computed(() => {
    const grouped: Record<string, TeamMember[]> = {}
    members.value.forEach(member => {
      if (!grouped[member.role]) {
        grouped[member.role] = []
      }
      grouped[member.role].push(member)
    })
    return grouped
  })

  const averageWorkload = computed(() => {
    if (members.value.length === 0) return 0
    const totalWorkload = members.value.reduce((sum, member) => sum + member.workload, 0)
    return Math.round(totalWorkload / members.value.length)
  })

  const topPerformers = computed(() => 
    [...members.value]
      .sort((a, b) => b.performance_score - a.performance_score)
      .slice(0, 5)
  )

  // 操作方法
  const fetchTeams = async () => {
    loading.value = true
    error.value = null

    try {
      // 首先尝试从localStorage加载数据
      const savedTeams = localStorage.getItem('teams')
      if (savedTeams) {
        const parsedTeams = JSON.parse(savedTeams)
        teams.value = parsedTeams
        loading.value = false
        return parsedTeams
      }

      // 如果没有保存的数据，使用模拟数据
      const mockTeams: Team[] = [
        {
          id: 'team-1',
          name: '前端开发团队',
          description: '负责前端界面开发和用户体验优化',
          leader_id: 'member-1',
          leader_name: '张前端',
          department: '技术部',
          members: [],
          projects: ['project-1', 'project-2'],
          status: 'active',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: new Date().toISOString()
        },
        {
          id: 'team-2',
          name: '后端开发团队',
          description: '负责后端API开发和数据库设计',
          leader_id: 'member-2',
          leader_name: '李后端',
          department: '技术部',
          members: [],
          projects: ['project-1', 'project-3'],
          status: 'active',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: new Date().toISOString()
        },
        {
          id: 'team-3',
          name: '产品设计团队',
          description: '负责产品设计和用户研究',
          leader_id: 'member-3',
          leader_name: '王设计',
          department: '产品部',
          members: [],
          projects: ['project-2'],
          status: 'active',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: new Date().toISOString()
        }
      ]

      teams.value = mockTeams
      localStorage.setItem('teams', JSON.stringify(mockTeams))
      return mockTeams
    } catch (err: any) {
      error.value = err.message || '获取团队列表失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchMembers = async () => {
    loading.value = true
    error.value = null

    try {
      // 首先尝试从localStorage加载数据
      const savedMembers = localStorage.getItem('team_members')
      if (savedMembers) {
        const parsedMembers = JSON.parse(savedMembers)
        members.value = parsedMembers
        loading.value = false
        return parsedMembers
      }

      // 如果没有保存的数据，使用模拟数据
      const mockMembers: TeamMember[] = [
        {
          id: 'member-1',
          user_id: 'user-1',
          name: '张前端',
          email: '<EMAIL>',
          avatar: '',
          role: '前端开发工程师',
          department: '技术部',
          position: '高级工程师',
          phone: '13800138001',
          skills: ['Vue.js', 'React', 'TypeScript', 'CSS'],
          status: 'active',
          join_date: '2023-06-01',
          projects: ['project-1', 'project-2'],
          current_tasks: 5,
          completed_tasks: 23,
          workload: 85,
          performance_score: 92,
          created_at: '2023-06-01T00:00:00Z',
          updated_at: new Date().toISOString()
        },
        {
          id: 'member-2',
          user_id: 'user-2',
          name: '李后端',
          email: '<EMAIL>',
          avatar: '',
          role: '后端开发工程师',
          department: '技术部',
          position: '资深工程师',
          phone: '13800138002',
          skills: ['Python', 'Django', 'PostgreSQL', 'Redis'],
          status: 'active',
          join_date: '2023-03-15',
          projects: ['project-1', 'project-3'],
          current_tasks: 4,
          completed_tasks: 31,
          workload: 75,
          performance_score: 88,
          created_at: '2023-03-15T00:00:00Z',
          updated_at: new Date().toISOString()
        },
        {
          id: 'member-3',
          user_id: 'user-3',
          name: '王设计',
          email: '<EMAIL>',
          avatar: '',
          role: 'UI/UX设计师',
          department: '产品部',
          position: '设计师',
          phone: '13800138003',
          skills: ['Figma', 'Sketch', 'Photoshop', '用户研究'],
          status: 'active',
          join_date: '2023-08-01',
          projects: ['project-2'],
          current_tasks: 3,
          completed_tasks: 15,
          workload: 60,
          performance_score: 85,
          created_at: '2023-08-01T00:00:00Z',
          updated_at: new Date().toISOString()
        },
        {
          id: 'member-4',
          user_id: 'user-4',
          name: '赵测试',
          email: '<EMAIL>',
          avatar: '',
          role: '测试工程师',
          department: '技术部',
          position: '测试工程师',
          phone: '13800138004',
          skills: ['自动化测试', 'Selenium', 'Jest', '性能测试'],
          status: 'active',
          join_date: '2023-09-01',
          projects: ['project-1', 'project-2', 'project-3'],
          current_tasks: 6,
          completed_tasks: 18,
          workload: 90,
          performance_score: 87,
          created_at: '2023-09-01T00:00:00Z',
          updated_at: new Date().toISOString()
        },
        {
          id: 'member-5',
          user_id: 'user-5',
          name: '钱产品',
          email: '<EMAIL>',
          avatar: '',
          role: '产品经理',
          department: '产品部',
          position: '产品经理',
          phone: '13800138005',
          skills: ['产品设计', '需求分析', 'Axure', '数据分析'],
          status: 'on_leave',
          join_date: '2023-04-01',
          projects: ['project-2'],
          current_tasks: 0,
          completed_tasks: 12,
          workload: 0,
          performance_score: 82,
          created_at: '2023-04-01T00:00:00Z',
          updated_at: new Date().toISOString()
        }
      ]

      members.value = mockMembers
      localStorage.setItem('team_members', JSON.stringify(mockMembers))
      return mockMembers
    } catch (err: any) {
      error.value = err.message || '获取团队成员失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const createMember = async (data: TeamMemberCreateRequest) => {
    loading.value = true
    error.value = null

    try {
      const newMember: TeamMember = {
        id: `member-${Date.now()}`,
        user_id: `user-${Date.now()}`,
        name: data.name,
        email: data.email,
        avatar: '',
        role: data.role,
        department: data.department,
        position: data.position,
        phone: data.phone || '',
        skills: data.skills,
        status: 'active',
        join_date: new Date().toISOString().split('T')[0],
        projects: data.projects || [],
        current_tasks: 0,
        completed_tasks: 0,
        workload: 0,
        performance_score: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      members.value.unshift(newMember)
      localStorage.setItem('team_members', JSON.stringify(members.value))
      return newMember
    } catch (err: any) {
      error.value = err.message || '创建团队成员失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateMember = async (id: string, data: TeamMemberUpdateRequest) => {
    loading.value = true
    error.value = null

    try {
      const index = members.value.findIndex(m => m.id === id)
      if (index !== -1) {
        members.value[index] = {
          ...members.value[index],
          ...data,
          updated_at: new Date().toISOString()
        }

        localStorage.setItem('team_members', JSON.stringify(members.value))
        return members.value[index]
      }
      throw new Error('团队成员不存在')
    } catch (err: any) {
      error.value = err.message || '更新团队成员失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteMember = async (id: string) => {
    loading.value = true
    error.value = null

    try {
      const index = members.value.findIndex(m => m.id === id)
      if (index !== -1) {
        members.value.splice(index, 1)
        localStorage.setItem('team_members', JSON.stringify(members.value))
      }
    } catch (err: any) {
      error.value = err.message || '删除团队成员失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    // 状态
    teams: readonly(teams),
    members: readonly(members),
    departments: readonly(departments),
    currentTeam: readonly(currentTeam),
    currentMember: readonly(currentMember),
    stats: readonly(stats),
    loading: readonly(loading),
    error: readonly(error),
    pagination: readonly(pagination),
    filters: readonly(filters),

    // 计算属性
    activeMembers,
    inactiveMembers,
    onLeaveMembers,
    membersByDepartment,
    membersByRole,
    averageWorkload,
    topPerformers,

    // 方法
    fetchTeams,
    fetchMembers,
    createMember,
    updateMember,
    deleteMember
  }
})
