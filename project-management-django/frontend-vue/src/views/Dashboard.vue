<template>
  <div class="dashboard">
    <!-- 页面标题 -->
    <div class="d-flex align-center justify-space-between mb-6">
      <div>
        <h1 class="text-h4 font-weight-bold">项目管理仪表板</h1>
        <p class="text-subtitle-1 text-medium-emphasis">
          欢迎回来，{{ currentUser?.full_name }}！
        </p>
      </div>

      <div class="d-flex align-center gap-3">
        <v-btn
          color="primary"
          variant="outlined"
          @click="refreshData"
          :loading="loading"
        >
          <v-icon start>mdi-refresh</v-icon>
          刷新数据
        </v-btn>

        <v-btn
          color="primary"
          @click="showCreateProject = true"
        >
          <v-icon start>mdi-plus</v-icon>
          新建项目
        </v-btn>
      </div>
    </div>

    <!-- 快速统计卡片 -->
    <v-row class="mb-6">
      <v-col cols="12" md="3" v-for="stat in quickStats" :key="stat.key">
        <v-card :color="stat.color" variant="tonal" class="h-100">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <div class="text-h4 font-weight-bold">{{ stat.value }}</div>
                <div class="text-subtitle-2">{{ stat.label }}</div>
                <div class="text-caption mt-1" :class="`text-${stat.color}`">
                  {{ stat.change }}
                </div>
              </div>
              <v-icon :color="stat.color" size="48">{{ stat.icon }}</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 主要内容区域 -->
    <v-row>
      <!-- 左侧列 -->
      <v-col cols="12" lg="8">
        <!-- 项目健康度分析 -->
        <div class="mb-6">
          <ProjectHealthChart
            :project="selectedProject"
            :height="400"
          />
        </div>

        <!-- 甘特图 -->
        <div class="mb-6">
          <v-card elevation="2">
            <v-card-title class="d-flex align-center">
              <v-icon class="mr-2" color="primary">mdi-chart-gantt</v-icon>
              <span>项目甘特图</span>
              <v-spacer></v-spacer>
              <v-select
                v-model="selectedProjectId"
                :items="projects"
                item-title="name"
                item-value="id"
                label="选择项目"
                density="compact"
                style="max-width: 200px"
                @update:model-value="onProjectChange"
              ></v-select>
            </v-card-title>

            <v-card-text>
              <GanttChart
                v-if="selectedProjectId"
                :project-id="selectedProjectId"
                :height="500"
              />
              <v-alert
                v-else
                type="info"
                variant="tonal"
                class="text-center"
              >
                请选择一个项目查看甘特图
              </v-alert>
            </v-card-text>
          </v-card>
        </div>
      </v-col>

      <!-- 右侧列 -->
      <v-col cols="12" lg="4">
        <!-- 团队工作量分析 -->
        <div class="mb-6">
          <TeamWorkloadChart
            :project-id="selectedProjectId"
            :height="350"
          />
        </div>

        <!-- 最近活动 -->
        <div class="mb-6">
          <v-card elevation="2">
            <v-card-title class="d-flex align-center">
              <v-icon class="mr-2" color="primary">mdi-timeline</v-icon>
              <span>最近活动</span>
            </v-card-title>

            <v-card-text>
              <v-timeline density="compact" side="end">
                <v-timeline-item
                  v-for="activity in recentActivities"
                  :key="activity.id"
                  :dot-color="getActivityColor(activity.type)"
                  size="small"
                >
                  <template v-slot:icon>
                    <v-icon size="16">{{ getActivityIcon(activity.type) }}</v-icon>
                  </template>

                  <div class="text-caption text-medium-emphasis">
                    {{ formatTime(activity.timestamp) }}
                  </div>
                  <div class="text-body-2">{{ activity.description }}</div>
                  <div class="text-caption text-medium-emphasis">
                    {{ activity.user_name }}
                  </div>
                </v-timeline-item>
              </v-timeline>

              <v-btn
                variant="text"
                block
                class="mt-3"
                @click="showAllActivities"
              >
                查看全部活动
              </v-btn>
            </v-card-text>
          </v-card>
        </div>

        <!-- 待办任务 -->
        <div class="mb-6">
          <v-card elevation="2">
            <v-card-title class="d-flex align-center">
              <v-icon class="mr-2" color="primary">mdi-check-circle</v-icon>
              <span>我的待办任务</span>
              <v-spacer></v-spacer>
              <v-chip color="primary" size="small">{{ myTasks.length }}</v-chip>
            </v-card-title>

            <v-card-text>
              <v-list density="compact">
                <v-list-item
                  v-for="task in myTasks.slice(0, 5)"
                  :key="task.id"
                  @click="openTask(task)"
                >
                  <template v-slot:prepend>
                    <v-checkbox
                      :model-value="task.status === 'completed'"
                      @update:model-value="toggleTaskStatus(task)"
                      density="compact"
                    ></v-checkbox>
                  </template>

                  <v-list-item-title>{{ task.title }}</v-list-item-title>
                  <v-list-item-subtitle>
                    {{ task.project?.name }} •
                    <span :class="`text-${getPriorityColor(task.priority)}`">
                      {{ getPriorityText(task.priority) }}
                    </span>
                  </v-list-item-subtitle>

                  <template v-slot:append>
                    <v-chip
                      :color="getStatusColor(task.status)"
                      size="x-small"
                    >
                      {{ getStatusText(task.status) }}
                    </v-chip>
                  </template>
                </v-list-item>
              </v-list>

              <v-btn
                variant="text"
                block
                class="mt-3"
                @click="showAllTasks"
              >
                查看全部任务
              </v-btn>
            </v-card-text>
          </v-card>
        </div>
      </v-col>
    </v-row>

    <!-- 创建项目对话框 -->
    <v-dialog v-model="showCreateProject" max-width="600px">
      <v-card>
        <v-card-title>创建新项目</v-card-title>
        <v-card-text>
          <!-- 项目创建表单 -->
          <v-form ref="projectForm">
            <v-text-field
              v-model="newProject.name"
              label="项目名称"
              required
            ></v-text-field>
            <v-textarea
              v-model="newProject.description"
              label="项目描述"
              rows="3"
            ></v-textarea>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="showCreateProject = false">取消</v-btn>
          <v-btn color="primary" @click="createProject">创建</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useProjectStore } from '@/stores/project'
import { useUserStore } from '@/stores/user'
import { useNotificationStore } from '@/stores/notification'
import GanttChart from '@/components/GanttChart.vue'
import ProjectHealthChart from '@/components/ProjectHealthChart.vue'
import TeamWorkloadChart from '@/components/TeamWorkloadChart.vue'
import type { Project } from '@/types/project'
import type { Task } from '@/types/task'

// Stores
const projectStore = useProjectStore()
const userStore = useUserStore()
const notificationStore = useNotificationStore()
const router = useRouter()

// Refs
const loading = ref(false)
const selectedProjectId = ref<string>('')
const showCreateProject = ref(false)
const newProject = ref({ name: '', description: '' })

// Computed
const currentUser = computed(() => userStore.currentUser)
const projects = computed(() => projectStore.projects)
const selectedProject = computed(() =>
  projects.value.find(p => p.id === selectedProjectId.value)
)

const myTasks = computed(() => {
  return projectStore.projects
    .flatMap(p => p.tasks || [])
    .filter(t => t.assignee?.id === currentUser.value?.id && t.status !== 'completed')
    .sort((a, b) => {
      const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })
})

const quickStats = computed(() => [
  {
    key: 'projects',
    label: '活跃项目',
    value: projects.value.filter(p => p.status === 'active').length,
    change: '+2 本月',
    color: 'primary',
    icon: 'mdi-folder-multiple'
  },
  {
    key: 'tasks',
    label: '待办任务',
    value: myTasks.value.length,
    change: '-5 本周',
    color: 'warning',
    icon: 'mdi-clipboard-list'
  },
  {
    key: 'completed',
    label: '已完成',
    value: projectStore.projects
      .flatMap(p => p.tasks || [])
      .filter(t => t.status === 'completed').length,
    change: '+12 本周',
    color: 'success',
    icon: 'mdi-check-circle'
  },
  {
    key: 'team',
    label: '团队成员',
    value: userStore.activeUsers.length,
    change: '+1 本月',
    color: 'info',
    icon: 'mdi-account-group'
  }
])

const recentActivities = computed(() => [
  {
    id: '1',
    type: 'task_completed',
    description: '完成了任务"用户认证系统开发"',
    user_name: '王开发',
    timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString()
  },
  {
    id: '2',
    type: 'project_created',
    description: '创建了新项目"移动端应用开发"',
    user_name: '李架构师',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString()
  },
  {
    id: '3',
    type: 'task_assigned',
    description: '分配了任务"甘特图组件集成"',
    user_name: '张项目经理',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString()
  }
])

// Methods
const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      projectStore.fetchProjects(),
      userStore.fetchUsers(),
      userStore.fetchStatistics()
    ])
    notificationStore.success('数据刷新成功', '所有数据已更新到最新状态')
  } catch (error) {
    notificationStore.error('数据刷新失败', '请检查网络连接后重试')
  } finally {
    loading.value = false
  }
}

const onProjectChange = (projectId: string) => {
  selectedProjectId.value = projectId
  if (projectId) {
    projectStore.fetchProject(projectId)
  }
}

const createProject = async () => {
  if (!newProject.value.name) return

  try {
    const project = await projectStore.createProject({
      name: newProject.value.name,
      description: newProject.value.description,
      code: `PRJ-${Date.now()}`,
      status: 'planning',
      priority: 'medium',
      start_date: new Date().toISOString().split('T')[0],
      end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      budget: 0,
      manager_id: '1'
    })

    if (project) {
      showCreateProject.value = false
      newProject.value = { name: '', description: '' }
      notificationStore.success('项目创建成功', `项目"${project.name}"已创建`)
    }
  } catch (error) {
    notificationStore.error('创建失败', '创建项目时发生错误')
  }
}

const toggleTaskStatus = async (task: Task) => {
  const newStatus = task.status === 'completed' ? 'in_progress' : 'completed'
  await projectStore.updateTask(task.id, { status: newStatus })
}

const openTask = (task: Task) => {
  router.push(`/projects/${task.project_id}/tasks/${task.id}`)
}

const showAllTasks = () => {
  router.push('/tasks')
}

const showAllActivities = () => {
  router.push('/activities')
}

// Utility methods
const formatTime = (timestamp: string) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 1000 * 60) return '刚刚'
  if (diff < 1000 * 60 * 60) return `${Math.floor(diff / (1000 * 60))}分钟前`
  if (diff < 1000 * 60 * 60 * 24) return `${Math.floor(diff / (1000 * 60 * 60))}小时前`
  return `${Math.floor(diff / (1000 * 60 * 60 * 24))}天前`
}

const getActivityColor = (type: string) => {
  const colors = {
    task_completed: 'success',
    task_assigned: 'primary',
    project_created: 'info',
    project_updated: 'warning'
  }
  return colors[type as keyof typeof colors] || 'grey'
}

const getActivityIcon = (type: string) => {
  const icons = {
    task_completed: 'mdi-check-circle',
    task_assigned: 'mdi-account-plus',
    project_created: 'mdi-folder-plus',
    project_updated: 'mdi-folder-edit'
  }
  return icons[type as keyof typeof icons] || 'mdi-information'
}

const getPriorityColor = (priority: string) => {
  const colors = {
    urgent: 'error',
    high: 'error',
    medium: 'warning',
    low: 'success'
  }
  return colors[priority as keyof typeof colors] || 'grey'
}

const getPriorityText = (priority: string) => {
  const texts = {
    urgent: '紧急',
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority as keyof typeof texts] || priority
}

const getStatusColor = (status: string) => {
  const colors = {
    todo: 'grey',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'error'
  }
  return colors[status as keyof typeof colors] || 'grey'
}

const getStatusText = (status: string) => {
  const texts = {
    todo: '待办',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status as keyof typeof texts] || status
}

// Lifecycle
onMounted(async () => {
  await refreshData()

  // 自动选择第一个项目
  if (projects.value.length > 0) {
    selectedProjectId.value = projects.value[0].id
  }
})
</script>

<style scoped>
.dashboard {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.gap-3 {
  gap: 12px;
}
</style>
