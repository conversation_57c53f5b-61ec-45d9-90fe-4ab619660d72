<template>
  <v-app>
    <v-main>
      <v-container fluid class="fill-height">
        <v-row justify="center" align="center">
          <v-col cols="12" sm="8" md="6" lg="4">
            <v-card elevation="8" class="pa-6">
              <v-card-title class="text-center text-h4 font-weight-bold mb-6">
                🚀 项目管理系统
              </v-card-title>

              <v-form @submit.prevent="handleLogin">
                <v-text-field
                  v-model="email"
                  label="邮箱"
                  type="email"
                  prepend-inner-icon="mdi-email"
                  variant="outlined"
                  class="mb-4"
                  required
                ></v-text-field>

                <v-text-field
                  v-model="password"
                  label="密码"
                  type="password"
                  prepend-inner-icon="mdi-lock"
                  variant="outlined"
                  class="mb-6"
                  required
                ></v-text-field>

                <v-btn
                  type="submit"
                  color="primary"
                  size="large"
                  block
                  :loading="loading"
                >
                  登录
                </v-btn>
              </v-form>
            </v-card>
          </v-col>
        </v-row>
      </v-container>
    </v-main>
  </v-app>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useNotificationStore } from '@/stores/notification'

const router = useRouter()
const userStore = useUserStore()
const notificationStore = useNotificationStore()

// 预设登录凭据
const email = ref('<EMAIL>')
const password = ref('123456')
const loading = ref(false)

const handleLogin = async () => {
  loading.value = true

  try {
    // 验证凭据
    if (email.value === '<EMAIL>' && password.value === '123456') {
      // 模拟用户数据
      const userData = {
        id: '1',
        email: email.value,
        username: 'admin',
        first_name: '管理员',
        last_name: '',
        is_active: true,
        is_staff: true,
        is_superuser: true,
        date_joined: new Date().toISOString(),
        last_login: new Date().toISOString(),
        avatar: null,
        phone: null,
        department: null,
        position: null,
        bio: null
      }

      // 设置用户状态
      userStore.setUser(userData)
      userStore.setToken('mock-jwt-token')

      notificationStore.success('登录成功', '欢迎回来！')

      // 跳转到主页
      router.push('/')
    } else {
      notificationStore.error('登录失败', '邮箱或密码错误')
    }
  } catch (error) {
    console.error('Login error:', error)
    notificationStore.error('登录失败', '登录过程中发生错误')
  } finally {
    loading.value = false
  }
}
</script>
