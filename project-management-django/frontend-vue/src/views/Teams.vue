<template>
  <v-container fluid>
    <!-- 页面标题和操作 -->
    <v-row class="mb-6">
      <v-col cols="12">
        <div class="d-flex justify-space-between align-center">
          <div>
            <h1 class="text-h4 font-weight-bold">团队管理</h1>
            <p class="text-body-1 text-medium-emphasis mt-1">
              管理团队成员和组织架构
            </p>
          </div>
          <v-btn
            color="primary"
            prepend-icon="mdi-account-plus"
            @click="showCreateMember = true"
            size="large"
          >
            添加成员
          </v-btn>
        </div>
      </v-col>
    </v-row>

    <!-- 团队统计卡片 -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" md="3">
        <v-card color="blue-lighten-5">
          <v-card-text class="text-center">
            <v-icon icon="mdi-account-group" size="large" color="blue"></v-icon>
            <div class="text-h4 font-weight-bold mt-2">{{ teamStore.activeMembers.length }}</div>
            <div class="text-body-2">活跃成员</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="green-lighten-5">
          <v-card-text class="text-center">
            <v-icon icon="mdi-briefcase" size="large" color="green"></v-icon>
            <div class="text-h4 font-weight-bold mt-2">{{ teamStore.teams.length }}</div>
            <div class="text-body-2">团队数量</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="orange-lighten-5">
          <v-card-text class="text-center">
            <v-icon icon="mdi-chart-line" size="large" color="orange"></v-icon>
            <div class="text-h4 font-weight-bold mt-2">{{ teamStore.averageWorkload }}%</div>
            <div class="text-body-2">平均工作负载</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="purple-lighten-5">
          <v-card-text class="text-center">
            <v-icon icon="mdi-account-off" size="large" color="purple"></v-icon>
            <div class="text-h4 font-weight-bold mt-2">{{ teamStore.onLeaveMembers.length }}</div>
            <div class="text-body-2">请假人员</div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 筛选和搜索 -->
    <v-row class="mb-4">
      <v-col cols="12" md="8">
        <v-card>
          <v-card-text>
            <v-row>
              <v-col cols="12" sm="6" md="3">
                <v-select
                  v-model="departmentFilter"
                  :items="departmentOptions"
                  label="部门筛选"
                  variant="outlined"
                  density="compact"
                  clearable
                ></v-select>
              </v-col>
              <v-col cols="12" sm="6" md="3">
                <v-select
                  v-model="roleFilter"
                  :items="roleOptions"
                  label="角色筛选"
                  variant="outlined"
                  density="compact"
                  clearable
                ></v-select>
              </v-col>
              <v-col cols="12" sm="6" md="3">
                <v-select
                  v-model="statusFilter"
                  :items="statusOptions"
                  label="状态筛选"
                  variant="outlined"
                  density="compact"
                  clearable
                ></v-select>
              </v-col>
              <v-col cols="12" sm="6" md="3">
                <v-text-field
                  v-model="searchQuery"
                  label="搜索成员"
                  placeholder="输入姓名或邮箱"
                  variant="outlined"
                  density="compact"
                  prepend-inner-icon="mdi-magnify"
                  clearable
                ></v-text-field>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="4">
        <v-card>
          <v-card-text>
            <div class="d-flex align-center">
              <v-icon icon="mdi-view-module" class="me-2"></v-icon>
              <span class="text-subtitle-1 me-4">视图模式</span>
              <v-btn-toggle v-model="viewMode" mandatory>
                <v-btn value="grid" icon="mdi-view-grid" size="small"></v-btn>
                <v-btn value="list" icon="mdi-view-list" size="small"></v-btn>
              </v-btn-toggle>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 团队成员列表 -->
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title class="d-flex justify-space-between align-center">
            <span class="text-h6">团队成员 ({{ filteredMembers.length }})</span>
            <v-btn
              color="primary"
              prepend-icon="mdi-account-plus"
              size="small"
              @click="showCreateMember = true"
            >
              添加成员
            </v-btn>
          </v-card-title>
          <v-divider></v-divider>

          <!-- 网格视图 -->
          <v-card-text v-if="viewMode === 'grid'" class="pa-4">
            <v-row>
              <v-col
                v-for="member in filteredMembers"
                :key="member.id"
                cols="12"
                sm="6"
                md="4"
                lg="3"
              >
                <v-card class="member-card" elevation="2" @click="viewMemberDetail(member)">
                  <v-card-text class="text-center pa-4">
                    <v-avatar size="64" class="mb-3" :color="getStatusColor(member.status)">
                      <v-icon icon="mdi-account" size="32" color="white"></v-icon>
                    </v-avatar>

                    <h3 class="text-h6 mb-1">{{ member.name }}</h3>
                    <p class="text-body-2 text-medium-emphasis mb-2">{{ member.role }}</p>
                    <p class="text-caption mb-3">{{ member.department }}</p>

                    <v-chip
                      :color="getStatusColor(member.status)"
                      variant="tonal"
                      size="small"
                      class="mb-3"
                    >
                      {{ getStatusText(member.status) }}
                    </v-chip>

                    <div class="mb-3">
                      <div class="text-caption mb-1">工作负载</div>
                      <v-progress-linear
                        :model-value="member.workload"
                        :color="getWorkloadColor(member.workload)"
                        height="6"
                      ></v-progress-linear>
                      <div class="text-caption mt-1">{{ member.workload }}%</div>
                    </div>

                    <div class="d-flex justify-space-between text-caption">
                      <span>当前任务: {{ member.current_tasks }}</span>
                      <span>已完成: {{ member.completed_tasks }}</span>
                    </div>
                  </v-card-text>

                  <v-card-actions class="justify-center">
                    <v-btn
                      icon="mdi-pencil"
                      variant="text"
                      size="small"
                      @click.stop="editMember(member)"
                    ></v-btn>
                    <v-btn
                      icon="mdi-email"
                      variant="text"
                      size="small"
                      @click.stop="sendEmail(member)"
                    ></v-btn>
                    <v-btn
                      icon="mdi-delete"
                      variant="text"
                      size="small"
                      color="red"
                      @click.stop="deleteMember(member)"
                    ></v-btn>
                  </v-card-actions>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>

          <!-- 列表视图 -->
          <v-card-text v-else class="pa-0">
            <v-data-table
              :headers="tableHeaders"
              :items="filteredMembers"
              :loading="teamStore.loading"
              item-key="id"
              class="elevation-0"
              @click:row="viewMemberDetail"
            >
              <template #item.name="{ item }">
                <div class="d-flex align-center">
                  <v-avatar size="32" :color="getStatusColor(item.status)" class="me-3">
                    <v-icon icon="mdi-account" color="white"></v-icon>
                  </v-avatar>
                  <div>
                    <div class="font-weight-medium">{{ item.name }}</div>
                    <div class="text-caption text-medium-emphasis">{{ item.email }}</div>
                  </div>
                </div>
              </template>

              <template #item.status="{ item }">
                <v-chip
                  :color="getStatusColor(item.status)"
                  variant="tonal"
                  size="small"
                >
                  {{ getStatusText(item.status) }}
                </v-chip>
              </template>

              <template #item.workload="{ item }">
                <div class="d-flex align-center">
                  <v-progress-linear
                    :model-value="item.workload"
                    :color="getWorkloadColor(item.workload)"
                    height="6"
                    class="me-2"
                    style="min-width: 80px"
                  ></v-progress-linear>
                  <span class="text-caption">{{ item.workload }}%</span>
                </div>
              </template>

              <template #item.performance_score="{ item }">
                <div class="d-flex align-center">
                  <v-rating
                    :model-value="item.performance_score / 20"
                    readonly
                    size="small"
                    density="compact"
                    class="me-2"
                  ></v-rating>
                  <span class="text-caption">{{ item.performance_score }}</span>
                </div>
              </template>

              <template #item.skills="{ item }">
                <div class="d-flex flex-wrap ga-1">
                  <v-chip
                    v-for="skill in item.skills.slice(0, 2)"
                    :key="skill"
                    size="x-small"
                    variant="outlined"
                  >
                    {{ skill }}
                  </v-chip>
                  <v-chip
                    v-if="item.skills.length > 2"
                    size="x-small"
                    variant="outlined"
                  >
                    +{{ item.skills.length - 2 }}
                  </v-chip>
                </div>
              </template>

              <template #item.actions="{ item }">
                <v-menu>
                  <template #activator="{ props }">
                    <v-btn
                      icon="mdi-dots-vertical"
                      variant="text"
                      size="small"
                      v-bind="props"
                    ></v-btn>
                  </template>
                  <v-list>
                    <v-list-item @click="editMember(item)">
                      <v-list-item-title>编辑</v-list-item-title>
                    </v-list-item>
                    <v-list-item @click="viewMemberDetail(item)">
                      <v-list-item-title>查看详情</v-list-item-title>
                    </v-list-item>
                    <v-list-item @click="sendEmail(item)">
                      <v-list-item-title>发送邮件</v-list-item-title>
                    </v-list-item>
                    <v-list-item @click="deleteMember(item)" class="text-red">
                      <v-list-item-title>删除</v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </template>
            </v-data-table>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 添加成员对话框 -->
    <v-dialog v-model="showCreateMember" max-width="600px">
      <v-card>
        <v-card-title class="text-h6">添加团队成员</v-card-title>
        <v-divider></v-divider>

        <v-card-text class="pa-6">
          <v-form ref="createForm" v-model="createFormValid">
            <v-row>
              <v-col cols="12" sm="6">
                <v-text-field
                  v-model="newMember.name"
                  label="姓名"
                  :rules="[rules.required]"
                  variant="outlined"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="6">
                <v-text-field
                  v-model="newMember.email"
                  label="邮箱"
                  :rules="[rules.required, rules.email]"
                  variant="outlined"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="6">
                <v-select
                  v-model="newMember.department"
                  :items="departmentOptions"
                  label="部门"
                  :rules="[rules.required]"
                  variant="outlined"
                  required
                ></v-select>
              </v-col>
              <v-col cols="12" sm="6">
                <v-text-field
                  v-model="newMember.role"
                  label="职位"
                  :rules="[rules.required]"
                  variant="outlined"
                  required
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="6">
                <v-text-field
                  v-model="newMember.position"
                  label="级别"
                  variant="outlined"
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="6">
                <v-text-field
                  v-model="newMember.phone"
                  label="电话"
                  variant="outlined"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-combobox
                  v-model="newMember.skills"
                  label="技能"
                  multiple
                  chips
                  variant="outlined"
                  hint="输入技能后按回车添加"
                ></v-combobox>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>

        <v-card-actions class="pa-6 pt-0">
          <v-spacer></v-spacer>
          <v-btn @click="showCreateMember = false">取消</v-btn>
          <v-btn
            color="primary"
            :disabled="!createFormValid"
            :loading="teamStore.loading"
            @click="createMember"
          >
            添加
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useTeamStore } from '@/stores/team'
import { useNotificationStore } from '@/stores/notification'
import type { TeamMember } from '@/types/team'

const teamStore = useTeamStore()
const notificationStore = useNotificationStore()

// 响应式数据
const viewMode = ref('grid')
const departmentFilter = ref('')
const roleFilter = ref('')
const statusFilter = ref('')
const searchQuery = ref('')
const showCreateMember = ref(false)
const createFormValid = ref(false)

const newMember = ref({
  name: '',
  email: '',
  department: '',
  role: '',
  position: '',
  phone: '',
  skills: [] as string[]
})

// 计算属性
const filteredMembers = computed(() => {
  let result = teamStore.members

  if (departmentFilter.value) {
    result = result.filter(member => member.department === departmentFilter.value)
  }

  if (roleFilter.value) {
    result = result.filter(member => member.role === roleFilter.value)
  }

  if (statusFilter.value) {
    result = result.filter(member => member.status === statusFilter.value)
  }

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(member =>
      member.name.toLowerCase().includes(query) ||
      member.email.toLowerCase().includes(query)
    )
  }

  return result
})

const departmentOptions = computed(() => {
  const departments = [...new Set(teamStore.members.map(m => m.department))]
  return departments.map(dept => ({ title: dept, value: dept }))
})

const roleOptions = computed(() => {
  const roles = [...new Set(teamStore.members.map(m => m.role))]
  return roles.map(role => ({ title: role, value: role }))
})

const statusOptions = [
  { title: '活跃', value: 'active' },
  { title: '非活跃', value: 'inactive' },
  { title: '请假', value: 'on_leave' }
]

// 表格配置
const tableHeaders = [
  { title: '成员', key: 'name', sortable: true },
  { title: '部门', key: 'department', sortable: true },
  { title: '职位', key: 'role', sortable: true },
  { title: '状态', key: 'status', sortable: true },
  { title: '工作负载', key: 'workload', sortable: true },
  { title: '绩效评分', key: 'performance_score', sortable: true },
  { title: '技能', key: 'skills', sortable: false },
  { title: '操作', key: 'actions', sortable: false }
]

// 表单验证规则
const rules = {
  required: (value: any) => !!value || '此字段为必填项',
  email: (value: string) => {
    const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return pattern.test(value) || '请输入有效的邮箱地址'
  }
}

// 方法
const createMember = async () => {
  try {
    await teamStore.createMember(newMember.value)
    showCreateMember.value = false
    newMember.value = {
      name: '',
      email: '',
      department: '',
      role: '',
      position: '',
      phone: '',
      skills: []
    }
    notificationStore.success('添加成功', '团队成员已添加')
  } catch (error) {
    notificationStore.error('添加失败', '添加团队成员失败')
  }
}

const editMember = (member: TeamMember) => {
  // TODO: 实现编辑功能
  notificationStore.info('功能开发中', '编辑功能正在开发中')
}

const viewMemberDetail = (member: TeamMember) => {
  // TODO: 实现查看详情功能
  notificationStore.info('功能开发中', '详情查看功能正在开发中')
}

const sendEmail = (member: TeamMember) => {
  window.open(`mailto:${member.email}`)
}

const deleteMember = async (member: TeamMember) => {
  if (confirm(`确定要删除成员"${member.name}"吗？`)) {
    try {
      await teamStore.deleteMember(member.id)
      notificationStore.success('删除成功', '团队成员已删除')
    } catch (error) {
      notificationStore.error('删除失败', '删除团队成员失败')
    }
  }
}

// 工具函数
const getStatusColor = (status: string) => {
  switch (status) {
    case 'active': return 'green'
    case 'inactive': return 'grey'
    case 'on_leave': return 'orange'
    default: return 'grey'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'active': return '活跃'
    case 'inactive': return '非活跃'
    case 'on_leave': return '请假'
    default: return '未知'
  }
}

const getWorkloadColor = (workload: number) => {
  if (workload >= 90) return 'red'
  if (workload >= 70) return 'orange'
  if (workload >= 50) return 'blue'
  return 'green'
}

onMounted(async () => {
  await Promise.all([
    teamStore.fetchTeams(),
    teamStore.fetchMembers()
  ])
})
</script>

<style scoped>
.member-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.member-card:hover {
  transform: translateY(-2px);
}
</style>
