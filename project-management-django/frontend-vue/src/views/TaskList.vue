<template>
  <v-container fluid>
    <!-- 页面标题和操作 -->
    <v-row class="mb-6">
      <v-col cols="12">
        <div class="d-flex justify-space-between align-center">
          <div>
            <h1 class="text-h4 font-weight-bold">任务管理</h1>
            <p class="text-body-1 text-medium-emphasis mt-1">
              管理和跟踪所有项目任务
            </p>
          </div>
          <v-btn
            color="primary"
            prepend-icon="mdi-plus"
            to="/tasks/create"
            size="large"
          >
            新建任务
          </v-btn>
        </div>
      </v-col>
    </v-row>

    <!-- 筛选和搜索 -->
    <v-row class="mb-4">
      <v-col cols="12" md="3">
        <v-text-field
          v-model="searchQuery"
          label="搜索任务"
          placeholder="输入任务标题或描述"
          variant="outlined"
          density="compact"
          prepend-inner-icon="mdi-magnify"
          clearable
          @input="handleSearch"
        ></v-text-field>
      </v-col>

      <v-col cols="12" md="2">
        <v-select
          v-model="statusFilter"
          :items="statusFilterOptions"
          label="状态筛选"
          variant="outlined"
          density="compact"
          clearable
          @update:model-value="handleFilter"
        ></v-select>
      </v-col>

      <v-col cols="12" md="2">
        <v-select
          v-model="priorityFilter"
          :items="priorityFilterOptions"
          label="优先级筛选"
          variant="outlined"
          density="compact"
          clearable
          @update:model-value="handleFilter"
        ></v-select>
      </v-col>

      <v-col cols="12" md="2">
        <v-select
          v-model="projectFilter"
          :items="projectFilterOptions"
          label="项目筛选"
          variant="outlined"
          density="compact"
          clearable
          @update:model-value="handleFilter"
        ></v-select>
      </v-col>

      <v-col cols="12" md="2">
        <v-select
          v-model="assigneeFilter"
          :items="assigneeFilterOptions"
          label="负责人筛选"
          variant="outlined"
          density="compact"
          clearable
          @update:model-value="handleFilter"
        ></v-select>
      </v-col>

      <v-col cols="12" md="1">
        <v-btn
          icon="mdi-filter-off"
          variant="outlined"
          @click="clearFilters"
          title="清除筛选"
        ></v-btn>
      </v-col>
    </v-row>

    <!-- 任务统计卡片 -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" md="3">
        <v-card color="blue-lighten-5">
          <v-card-text class="text-center">
            <v-icon icon="mdi-format-list-bulleted" size="large" color="blue"></v-icon>
            <div class="text-h4 font-weight-bold mt-2">{{ taskStore.todoTasks.length }}</div>
            <div class="text-body-2">待办任务</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="orange-lighten-5">
          <v-card-text class="text-center">
            <v-icon icon="mdi-progress-clock" size="large" color="orange"></v-icon>
            <div class="text-h4 font-weight-bold mt-2">{{ taskStore.inProgressTasks.length }}</div>
            <div class="text-body-2">进行中</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="green-lighten-5">
          <v-card-text class="text-center">
            <v-icon icon="mdi-check-circle" size="large" color="green"></v-icon>
            <div class="text-h4 font-weight-bold mt-2">{{ taskStore.completedTasks.length }}</div>
            <div class="text-body-2">已完成</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="red-lighten-5">
          <v-card-text class="text-center">
            <v-icon icon="mdi-alert-circle" size="large" color="red"></v-icon>
            <div class="text-h4 font-weight-bold mt-2">{{ taskStore.overdueTasks.length }}</div>
            <div class="text-body-2">逾期任务</div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 任务列表 -->
    <v-row>
      <v-col cols="12">
        <v-card elevation="2">
          <v-card-title class="d-flex justify-space-between align-center pa-6">
            <span class="text-h6">任务列表</span>
            <div class="d-flex gap-2">
              <v-btn-toggle v-model="viewMode" mandatory>
                <v-btn value="list" icon="mdi-view-list"></v-btn>
                <v-btn value="grid" icon="mdi-view-grid"></v-btn>
                <v-btn value="kanban" icon="mdi-view-column"></v-btn>
              </v-btn-toggle>
            </div>
          </v-card-title>

          <v-divider></v-divider>

          <v-card-text class="pa-0">
            <!-- 列表视图 -->
            <div v-if="viewMode === 'list'">
              <v-data-table
                :headers="tableHeaders"
                :items="filteredTasks"
                :loading="taskStore.loading"
                item-key="id"
                class="elevation-0"
                @click:row="openTask"
              >
                <template #item.title="{ item }">
                  <div class="d-flex align-center">
                    <v-icon
                      :icon="getTaskTypeIcon(item.type)"
                      :color="getPriorityColor(item.priority)"
                      class="me-2"
                    ></v-icon>
                    <div>
                      <div class="font-weight-medium">{{ item.title }}</div>
                      <div class="text-caption text-medium-emphasis">{{ item.description }}</div>
                    </div>
                  </div>
                </template>

                <template #item.status="{ item }">
                  <v-chip
                    :color="getStatusColor(item.status)"
                    variant="tonal"
                    size="small"
                  >
                    {{ getStatusText(item.status) }}
                  </v-chip>
                </template>

                <template #item.priority="{ item }">
                  <v-chip
                    :color="getPriorityColor(item.priority)"
                    variant="tonal"
                    size="small"
                  >
                    {{ getPriorityText(item.priority) }}
                  </v-chip>
                </template>

                <template #item.progress="{ item }">
                  <div class="d-flex align-center">
                    <v-progress-linear
                      :model-value="item.progress"
                      :color="getProgressColor(item.progress)"
                      height="6"
                      class="me-2"
                      style="min-width: 60px"
                    ></v-progress-linear>
                    <span class="text-caption">{{ item.progress }}%</span>
                  </div>
                </template>

                <template #item.due_date="{ item }">
                  <div v-if="item.due_date">
                    <div :class="{ 'text-red': item.is_overdue }">
                      {{ formatDate(item.due_date) }}
                    </div>
                    <div v-if="item.is_overdue" class="text-caption text-red">
                      已逾期
                    </div>
                  </div>
                  <span v-else class="text-medium-emphasis">未设置</span>
                </template>

                <template #item.assignee_id="{ item }">
                  <div v-if="item.assignee_id">
                    <v-avatar size="24" class="me-2">
                      <v-icon icon="mdi-account"></v-icon>
                    </v-avatar>
                    管理员
                  </div>
                  <span v-else class="text-medium-emphasis">未分配</span>
                </template>

                <template #item.actions="{ item }">
                  <v-menu>
                    <template #activator="{ props }">
                      <v-btn
                        icon="mdi-dots-vertical"
                        variant="text"
                        size="small"
                        v-bind="props"
                      ></v-btn>
                    </template>
                    <v-list>
                      <v-list-item @click="editTask(item)">
                        <v-list-item-title>编辑</v-list-item-title>
                      </v-list-item>
                      <v-list-item @click="toggleTaskStatus(item)">
                        <v-list-item-title>
                          {{ item.status === 'completed' ? '标记为未完成' : '标记为完成' }}
                        </v-list-item-title>
                      </v-list-item>
                      <v-list-item @click="deleteTask(item)" class="text-red">
                        <v-list-item-title>删除</v-list-item-title>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </template>
              </v-data-table>
            </div>

            <!-- 网格视图 -->
            <div v-else-if="viewMode === 'grid'" class="pa-4">
              <v-row>
                <v-col
                  v-for="task in filteredTasks"
                  :key="task.id"
                  cols="12"
                  sm="6"
                  md="4"
                  lg="3"
                >
                  <TaskCard :task="task" @click="openTask(task)" />
                </v-col>
              </v-row>
            </div>

            <!-- 看板视图 -->
            <div v-else-if="viewMode === 'kanban'" class="pa-4">
              <TaskKanban :tasks="filteredTasks" />
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useTaskStore } from '@/stores/task'
import { useNotificationStore } from '@/stores/notification'
import type { Task } from '@/types/task'

const router = useRouter()
const taskStore = useTaskStore()
const notificationStore = useNotificationStore()

// 响应式数据
const searchQuery = ref('')
const statusFilter = ref('')
const priorityFilter = ref('')
const projectFilter = ref('')
const assigneeFilter = ref('')
const viewMode = ref('list')

// 计算属性

const filteredTasks = computed(() => {
  let result = taskStore.tasks

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(task =>
      task.title.toLowerCase().includes(query) ||
      task.description?.toLowerCase().includes(query)
    )
  }

  if (statusFilter.value) {
    result = result.filter(task => task.status === statusFilter.value)
  }

  if (priorityFilter.value) {
    result = result.filter(task => task.priority === priorityFilter.value)
  }

  if (projectFilter.value) {
    result = result.filter(task => task.project_id === projectFilter.value)
  }

  if (assigneeFilter.value) {
    result = result.filter(task => task.assignee_id === assigneeFilter.value)
  }

  return result
})

// 表格配置
const tableHeaders = [
  { title: '任务', key: 'title', sortable: true },
  { title: '状态', key: 'status', sortable: true },
  { title: '优先级', key: 'priority', sortable: true },
  { title: '进度', key: 'progress', sortable: true },
  { title: '截止日期', key: 'due_date', sortable: true },
  { title: '负责人', key: 'assignee_id', sortable: false },
  { title: '操作', key: 'actions', sortable: false }
]

// 筛选选项
const statusFilterOptions = [
  { title: '待办', value: 'todo' },
  { title: '进行中', value: 'in_progress' },
  { title: '已完成', value: 'completed' },
  { title: '已取消', value: 'cancelled' },
  { title: '暂停', value: 'on_hold' }
]

const priorityFilterOptions = [
  { title: '低', value: 'low' },
  { title: '中', value: 'medium' },
  { title: '高', value: 'high' },
  { title: '紧急', value: 'urgent' }
]

const projectFilterOptions = ref([
  { title: '项目管理系统', value: 'project-1' },
  { title: '移动应用开发', value: 'project-2' }
])

const assigneeFilterOptions = ref([
  { title: '管理员', value: '1' },
  { title: '开发者A', value: '2' },
  { title: '开发者B', value: '3' }
])

// 方法
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleFilter = () => {
  // 筛选逻辑已在计算属性中处理
}

const clearFilters = () => {
  searchQuery.value = ''
  statusFilter.value = ''
  priorityFilter.value = ''
  projectFilter.value = ''
  assigneeFilter.value = ''
}

const openTask = (task: Task) => {
  router.push(`/tasks/${task.id}`)
}

const editTask = (task: Task) => {
  router.push(`/tasks/${task.id}/edit`)
}

const toggleTaskStatus = async (task: Task) => {
  try {
    const newStatus = task.status === 'completed' ? 'todo' : 'completed'
    await taskStore.updateTaskStatus(task.id, newStatus)
    notificationStore.success('状态更新', `任务状态已更新为${getStatusText(newStatus)}`)
  } catch (error) {
    notificationStore.error('更新失败', '更新任务状态失败')
  }
}

const deleteTask = async (task: Task) => {
  if (confirm(`确定要删除任务"${task.title}"吗？`)) {
    try {
      await taskStore.deleteTask(task.id)
      notificationStore.success('删除成功', '任务已删除')
    } catch (error) {
      notificationStore.error('删除失败', '删除任务失败')
    }
  }
}

// 工具函数
const getTaskTypeIcon = (type?: string) => {
  switch (type) {
    case 'milestone': return 'mdi-flag'
    case 'summary': return 'mdi-file-document'
    default: return 'mdi-checkbox-marked-circle'
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'todo': return 'blue'
    case 'in_progress': return 'orange'
    case 'completed': return 'green'
    case 'cancelled': return 'red'
    case 'on_hold': return 'grey'
    default: return 'grey'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'todo': return '待办'
    case 'in_progress': return '进行中'
    case 'completed': return '已完成'
    case 'cancelled': return '已取消'
    case 'on_hold': return '暂停'
    default: return '未知'
  }
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'low': return 'green'
    case 'medium': return 'orange'
    case 'high': return 'red'
    case 'urgent': return 'purple'
    default: return 'grey'
  }
}

const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'low': return '低'
    case 'medium': return '中'
    case 'high': return '高'
    case 'urgent': return '紧急'
    default: return '未知'
  }
}

const getProgressColor = (progress: number) => {
  if (progress >= 80) return 'green'
  if (progress >= 50) return 'orange'
  return 'blue'
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

onMounted(async () => {
  await taskStore.fetchTasks()
})
</script>
