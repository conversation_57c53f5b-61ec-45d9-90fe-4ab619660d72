<template>
  <v-container fluid>
    <!-- 页面标题和操作 -->
    <v-row class="mb-6">
      <v-col cols="12">
        <div class="d-flex justify-space-between align-center">
          <div>
            <h1 class="text-h4 font-weight-bold">报表分析</h1>
            <p class="text-body-1 text-medium-emphasis mt-1">
              项目数据分析和可视化报表
            </p>
          </div>
          <div>
            <v-btn
              color="primary"
              prepend-icon="mdi-download"
              variant="outlined"
              class="me-2"
              @click="showExportDialog = true"
            >
              导出报表
            </v-btn>
            <v-btn
              color="success"
              prepend-icon="mdi-refresh"
              @click="refreshData"
            >
              刷新数据
            </v-btn>
          </div>
        </div>
      </v-col>
    </v-row>

    <!-- 关键指标卡片 -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" md="3">
        <v-card color="blue-lighten-5">
          <v-card-text class="text-center">
            <v-icon icon="mdi-folder-multiple" size="large" color="blue"></v-icon>
            <div class="text-h4 font-weight-bold mt-2">{{ dashboardStats.total_projects }}</div>
            <div class="text-body-2">总项目数</div>
            <div class="text-caption mt-1">
              活跃: {{ dashboardStats.active_projects }} | 已完成: {{ dashboardStats.completed_projects }}
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="green-lighten-5">
          <v-card-text class="text-center">
            <v-icon icon="mdi-format-list-checks" size="large" color="green"></v-icon>
            <div class="text-h4 font-weight-bold mt-2">{{ dashboardStats.total_tasks }}</div>
            <div class="text-body-2">总任务数</div>
            <div class="text-caption mt-1">
              完成率: {{ Math.round(dashboardStats.completed_tasks / dashboardStats.total_tasks * 100) || 0 }}%
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="orange-lighten-5">
          <v-card-text class="text-center">
            <v-icon icon="mdi-account-group" size="large" color="orange"></v-icon>
            <div class="text-h4 font-weight-bold mt-2">{{ dashboardStats.total_members }}</div>
            <div class="text-body-2">团队成员</div>
            <div class="text-caption mt-1">
              平均负载: {{ dashboardStats.average_team_workload }}%
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="purple-lighten-5">
          <v-card-text class="text-center">
            <v-icon icon="mdi-currency-usd" size="large" color="purple"></v-icon>
            <div class="text-h4 font-weight-bold mt-2">¥{{ (dashboardStats.total_budget / 10000).toFixed(1) }}万</div>
            <div class="text-body-2">总预算</div>
            <div class="text-caption mt-1">
              已花费: {{ Math.round(dashboardStats.spent_budget / dashboardStats.total_budget * 100) || 0 }}%
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 效率指标 -->
    <v-row class="mb-6">
      <v-col cols="12">
        <v-card>
          <v-card-title class="d-flex align-center">
            <v-icon icon="mdi-speedometer" class="me-2"></v-icon>
            效率指标
          </v-card-title>
          <v-divider></v-divider>
          <v-card-text>
            <v-row>
              <v-col cols="12" sm="6" md="2">
                <div class="text-center">
                  <div class="text-h6 font-weight-bold">{{ efficiencyMetrics.project_completion_rate.toFixed(1) }}%</div>
                  <div class="text-caption">项目完成率</div>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="2">
                <div class="text-center">
                  <div class="text-h6 font-weight-bold">{{ efficiencyMetrics.task_completion_rate.toFixed(1) }}%</div>
                  <div class="text-caption">任务完成率</div>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="2">
                <div class="text-center">
                  <div class="text-h6 font-weight-bold">{{ efficiencyMetrics.on_time_delivery_rate.toFixed(1) }}%</div>
                  <div class="text-caption">按时交付率</div>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="2">
                <div class="text-center">
                  <div class="text-h6 font-weight-bold">{{ efficiencyMetrics.budget_utilization_rate.toFixed(1) }}%</div>
                  <div class="text-caption">预算利用率</div>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="2">
                <div class="text-center">
                  <div class="text-h6 font-weight-bold">{{ efficiencyMetrics.team_productivity_score.toFixed(1) }}</div>
                  <div class="text-caption">团队生产力</div>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="2">
                <div class="text-center">
                  <div class="text-h6 font-weight-bold">{{ efficiencyMetrics.average_task_cycle_time.toFixed(1) }}h</div>
                  <div class="text-caption">平均任务周期</div>
                </div>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 图表分析 -->
    <v-row class="mb-6">
      <!-- 项目状态分布 -->
      <v-col cols="12" md="6">
        <v-card>
          <v-card-title class="d-flex align-center">
            <v-icon icon="mdi-chart-pie" class="me-2"></v-icon>
            项目状态分布
          </v-card-title>
          <v-divider></v-divider>
          <v-card-text>
            <div class="chart-container">
              <canvas ref="projectStatusChart" width="400" height="300"></canvas>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- 任务优先级分布 -->
      <v-col cols="12" md="6">
        <v-card>
          <v-card-title class="d-flex align-center">
            <v-icon icon="mdi-chart-donut" class="me-2"></v-icon>
            任务优先级分布
          </v-card-title>
          <v-divider></v-divider>
          <v-card-text>
            <div class="chart-container">
              <canvas ref="taskPriorityChart" width="400" height="300"></canvas>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <v-row class="mb-6">
      <!-- 工作负载分布 -->
      <v-col cols="12" md="6">
        <v-card>
          <v-card-title class="d-flex align-center">
            <v-icon icon="mdi-chart-bar" class="me-2"></v-icon>
            团队工作负载分布
          </v-card-title>
          <v-divider></v-divider>
          <v-card-text>
            <div class="chart-container">
              <canvas ref="workloadChart" width="400" height="300"></canvas>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- 趋势分析 -->
      <v-col cols="12" md="6">
        <v-card>
          <v-card-title class="d-flex align-center">
            <v-icon icon="mdi-chart-line" class="me-2"></v-icon>
            项目进度趋势
          </v-card-title>
          <v-divider></v-divider>
          <v-card-text>
            <div class="chart-container">
              <canvas ref="trendChart" width="400" height="300"></canvas>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 详细报表 -->
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title class="d-flex justify-space-between align-center">
            <div class="d-flex align-center">
              <v-icon icon="mdi-table" class="me-2"></v-icon>
              详细报表
            </div>
            <v-btn-toggle v-model="reportType" mandatory>
              <v-btn value="projects" size="small">项目报表</v-btn>
              <v-btn value="tasks" size="small">任务报表</v-btn>
              <v-btn value="team" size="small">团队绩效</v-btn>
            </v-btn-toggle>
          </v-card-title>
          <v-divider></v-divider>

          <!-- 项目报表 -->
          <v-card-text v-if="reportType === 'projects'" class="pa-0">
            <v-data-table
              :headers="projectHeaders"
              :items="reportStore.projectReports"
              :loading="reportStore.loading"
              item-key="id"
              class="elevation-0"
            >
              <template #item.status="{ item }">
                <v-chip
                  :color="getStatusColor(item.status)"
                  variant="tonal"
                  size="small"
                >
                  {{ getStatusText(item.status) }}
                </v-chip>
              </template>

              <template #item.progress="{ item }">
                <div class="d-flex align-center">
                  <v-progress-linear
                    :model-value="item.progress"
                    :color="getProgressColor(item.progress)"
                    height="6"
                    class="me-2"
                    style="min-width: 80px"
                  ></v-progress-linear>
                  <span class="text-caption">{{ item.progress }}%</span>
                </div>
              </template>

              <template #item.budget="{ item }">
                <div>
                  <div class="font-weight-medium">¥{{ item.budget.toLocaleString() }}</div>
                  <div class="text-caption text-medium-emphasis">
                    已花费: ¥{{ item.spent.toLocaleString() }}
                  </div>
                </div>
              </template>

              <template #item.is_overdue="{ item }">
                <v-chip
                  :color="item.is_overdue ? 'red' : 'green'"
                  variant="tonal"
                  size="small"
                >
                  {{ item.is_overdue ? '逾期' : '正常' }}
                </v-chip>
              </template>

              <template #item.efficiency_score="{ item }">
                <div class="d-flex align-center">
                  <v-rating
                    :model-value="item.efficiency_score / 20"
                    readonly
                    size="small"
                    density="compact"
                    class="me-2"
                  ></v-rating>
                  <span class="text-caption">{{ item.efficiency_score }}</span>
                </div>
              </template>
            </v-data-table>
          </v-card-text>

          <!-- 任务报表 -->
          <v-card-text v-else-if="reportType === 'tasks'" class="pa-0">
            <v-data-table
              :headers="taskHeaders"
              :items="reportStore.taskReports"
              :loading="reportStore.loading"
              item-key="id"
              class="elevation-0"
            >
              <template #item.status="{ item }">
                <v-chip
                  :color="getTaskStatusColor(item.status)"
                  variant="tonal"
                  size="small"
                >
                  {{ getTaskStatusText(item.status) }}
                </v-chip>
              </template>

              <template #item.priority="{ item }">
                <v-chip
                  :color="getPriorityColor(item.priority)"
                  variant="tonal"
                  size="small"
                >
                  {{ getPriorityText(item.priority) }}
                </v-chip>
              </template>

              <template #item.progress="{ item }">
                <div class="d-flex align-center">
                  <v-progress-linear
                    :model-value="item.progress"
                    :color="getProgressColor(item.progress)"
                    height="6"
                    class="me-2"
                    style="min-width: 60px"
                  ></v-progress-linear>
                  <span class="text-caption">{{ item.progress }}%</span>
                </div>
              </template>

              <template #item.is_overdue="{ item }">
                <v-chip
                  :color="item.is_overdue ? 'red' : 'green'"
                  variant="tonal"
                  size="small"
                >
                  {{ item.is_overdue ? '逾期' : '正常' }}
                </v-chip>
              </template>

              <template #item.efficiency_ratio="{ item }">
                <div class="text-center">
                  <div class="font-weight-medium">{{ item.efficiency_ratio.toFixed(2) }}</div>
                  <div class="text-caption" :class="item.efficiency_ratio > 1 ? 'text-red' : 'text-green'">
                    {{ item.efficiency_ratio > 1 ? '超时' : '高效' }}
                  </div>
                </div>
              </template>
            </v-data-table>
          </v-card-text>

          <!-- 团队绩效报表 -->
          <v-card-text v-else class="pa-0">
            <v-data-table
              :headers="teamHeaders"
              :items="reportStore.teamPerformanceReports"
              :loading="reportStore.loading"
              item-key="member_id"
              class="elevation-0"
            >
              <template #item.member_name="{ item }">
                <div class="d-flex align-center">
                  <v-avatar size="32" color="primary" class="me-3">
                    <v-icon icon="mdi-account" color="white"></v-icon>
                  </v-avatar>
                  <div>
                    <div class="font-weight-medium">{{ item.member_name }}</div>
                    <div class="text-caption text-medium-emphasis">{{ item.role }}</div>
                  </div>
                </div>
              </template>

              <template #item.workload="{ item }">
                <div class="d-flex align-center">
                  <v-progress-linear
                    :model-value="item.workload"
                    :color="getWorkloadColor(item.workload)"
                    height="6"
                    class="me-2"
                    style="min-width: 60px"
                  ></v-progress-linear>
                  <span class="text-caption">{{ item.workload }}%</span>
                </div>
              </template>

              <template #item.performance_score="{ item }">
                <div class="d-flex align-center">
                  <v-rating
                    :model-value="item.performance_score / 20"
                    readonly
                    size="small"
                    density="compact"
                    class="me-2"
                  ></v-rating>
                  <span class="text-caption">{{ item.performance_score }}</span>
                </div>
              </template>

              <template #item.efficiency_trend="{ item }">
                <div class="d-flex align-center ga-1">
                  <v-icon
                    v-for="(value, index) in item.efficiency_trend.slice(-3)"
                    :key="index"
                    :icon="value > 80 ? 'mdi-trending-up' : value > 60 ? 'mdi-trending-neutral' : 'mdi-trending-down'"
                    :color="value > 80 ? 'green' : value > 60 ? 'orange' : 'red'"
                    size="small"
                  ></v-icon>
                </div>
              </template>
            </v-data-table>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 导出对话框 -->
    <v-dialog v-model="showExportDialog" max-width="500px">
      <v-card>
        <v-card-title class="text-h6">导出报表</v-card-title>
        <v-divider></v-divider>

        <v-card-text class="pa-6">
          <v-form>
            <v-select
              v-model="exportOptions.type"
              :items="exportTypeOptions"
              label="报表类型"
              variant="outlined"
              class="mb-4"
            ></v-select>

            <v-select
              v-model="exportOptions.format"
              :items="exportFormatOptions"
              label="导出格式"
              variant="outlined"
              class="mb-4"
            ></v-select>

            <v-checkbox
              v-model="exportOptions.includeCharts"
              label="包含图表"
            ></v-checkbox>

            <v-checkbox
              v-model="exportOptions.includeRawData"
              label="包含原始数据"
            ></v-checkbox>
          </v-form>
        </v-card-text>

        <v-card-actions class="pa-6 pt-0">
          <v-spacer></v-spacer>
          <v-btn @click="showExportDialog = false">取消</v-btn>
          <v-btn
            color="primary"
            :loading="reportStore.loading"
            @click="exportReport"
          >
            导出
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useReportStore } from '@/stores/report'
import { useProjectStore } from '@/stores/project'
import { useTaskStore } from '@/stores/task'
import { useTeamStore } from '@/stores/team'
import { useNotificationStore } from '@/stores/notification'

const reportStore = useReportStore()
const projectStore = useProjectStore()
const taskStore = useTaskStore()
const teamStore = useTeamStore()
const notificationStore = useNotificationStore()

// 响应式数据
const reportType = ref('projects')
const showExportDialog = ref(false)
const exportOptions = ref({
  type: 'projects',
  format: 'excel',
  includeCharts: true,
  includeRawData: true
})

// Chart.js 实例引用
const projectStatusChart = ref<HTMLCanvasElement>()
const taskPriorityChart = ref<HTMLCanvasElement>()
const workloadChart = ref<HTMLCanvasElement>()
const trendChart = ref<HTMLCanvasElement>()

// 计算属性
const dashboardStats = computed(() => reportStore.computedDashboardStats)
const efficiencyMetrics = computed(() => reportStore.efficiencyMetrics)

// 选项数据
const exportTypeOptions = [
  { title: '项目报表', value: 'projects' },
  { title: '任务报表', value: 'tasks' },
  { title: '团队绩效', value: 'team' },
  { title: '综合报表', value: 'dashboard' }
]

const exportFormatOptions = [
  { title: 'Excel', value: 'excel' },
  { title: 'PDF', value: 'pdf' },
  { title: 'CSV', value: 'csv' }
]

// 表格配置
const projectHeaders = [
  { title: '项目名称', key: 'name', sortable: true },
  { title: '状态', key: 'status', sortable: true },
  { title: '进度', key: 'progress', sortable: true },
  { title: '预算', key: 'budget', sortable: true },
  { title: '任务数', key: 'task_count', sortable: true },
  { title: '团队规模', key: 'team_member_count', sortable: true },
  { title: '是否逾期', key: 'is_overdue', sortable: true },
  { title: '效率评分', key: 'efficiency_score', sortable: true }
]

const taskHeaders = [
  { title: '任务名称', key: 'title', sortable: true },
  { title: '项目', key: 'project_name', sortable: true },
  { title: '状态', key: 'status', sortable: true },
  { title: '优先级', key: 'priority', sortable: true },
  { title: '负责人', key: 'assignee_name', sortable: true },
  { title: '进度', key: 'progress', sortable: true },
  { title: '预估/实际时间', key: 'estimated_hours', sortable: true },
  { title: '是否逾期', key: 'is_overdue', sortable: true },
  { title: '效率比', key: 'efficiency_ratio', sortable: true }
]

const teamHeaders = [
  { title: '成员', key: 'member_name', sortable: true },
  { title: '部门', key: 'department', sortable: true },
  { title: '总任务', key: 'total_tasks', sortable: true },
  { title: '已完成', key: 'completed_tasks', sortable: true },
  { title: '逾期任务', key: 'overdue_tasks', sortable: true },
  { title: '工作负载', key: 'workload', sortable: true },
  { title: '绩效评分', key: 'performance_score', sortable: true },
  { title: '效率趋势', key: 'efficiency_trend', sortable: false }
]

// 方法
const refreshData = async () => {
  try {
    await Promise.all([
      projectStore.fetchProjects(),
      taskStore.fetchTasks(),
      teamStore.fetchMembers()
    ])

    // 重新渲染图表
    await nextTick()
    renderCharts()

    notificationStore.success('刷新成功', '数据已更新')
  } catch (error) {
    notificationStore.error('刷新失败', '数据更新失败')
  }
}

const exportReport = async () => {
  try {
    await reportStore.exportReport(exportOptions.value.type, exportOptions.value.format)
    showExportDialog.value = false
    notificationStore.success('导出成功', '报表已导出')
  } catch (error) {
    notificationStore.error('导出失败', '报表导出失败')
  }
}

const renderCharts = async () => {
  // 这里应该使用 Chart.js 或其他图表库来渲染图表
  // 由于没有安装图表库，这里只是模拟
  console.log('渲染图表:', {
    projectStatus: reportStore.generateProjectStatusChart(),
    taskPriority: reportStore.generateTaskPriorityChart(),
    workload: reportStore.generateWorkloadChart(),
    trend: reportStore.generateTrendChart()
  })
}

// 工具函数
const getStatusColor = (status: string) => {
  switch (status) {
    case 'planning': return 'blue'
    case 'in_progress': return 'orange'
    case 'completed': return 'green'
    case 'paused': return 'grey'
    case 'cancelled': return 'red'
    default: return 'grey'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'planning': return '规划中'
    case 'in_progress': return '进行中'
    case 'completed': return '已完成'
    case 'paused': return '已暂停'
    case 'cancelled': return '已取消'
    default: return '未知'
  }
}

const getTaskStatusColor = (status: string) => {
  switch (status) {
    case 'todo': return 'grey'
    case 'in_progress': return 'orange'
    case 'completed': return 'green'
    case 'cancelled': return 'red'
    default: return 'grey'
  }
}

const getTaskStatusText = (status: string) => {
  switch (status) {
    case 'todo': return '待办'
    case 'in_progress': return '进行中'
    case 'completed': return '已完成'
    case 'cancelled': return '已取消'
    default: return '未知'
  }
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'low': return 'green'
    case 'medium': return 'orange'
    case 'high': return 'red'
    case 'urgent': return 'purple'
    default: return 'grey'
  }
}

const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'low': return '低'
    case 'medium': return '中'
    case 'high': return '高'
    case 'urgent': return '紧急'
    default: return '未知'
  }
}

const getProgressColor = (progress: number) => {
  if (progress >= 80) return 'green'
  if (progress >= 50) return 'orange'
  return 'blue'
}

const getWorkloadColor = (workload: number) => {
  if (workload >= 90) return 'red'
  if (workload >= 70) return 'orange'
  if (workload >= 50) return 'blue'
  return 'green'
}

onMounted(async () => {
  await refreshData()
})
</script>

<style scoped>
.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-container canvas {
  max-width: 100%;
  max-height: 100%;
}
</style>
