<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <div class="d-flex align-center mb-6">
          <v-btn
            icon="mdi-arrow-left"
            variant="text"
            @click="$router.go(-1)"
            class="me-4"
          ></v-btn>
          <div>
            <h1 class="text-h4 font-weight-bold">创建新项目</h1>
            <p class="text-body-1 text-medium-emphasis mt-1">
              填写项目基本信息，开始您的项目管理之旅
            </p>
          </div>
        </div>
      </v-col>
    </v-row>

    <v-row>
      <v-col cols="12" lg="8">
        <v-card elevation="2">
          <v-card-title class="text-h6 pa-6 pb-4">
            <v-icon icon="mdi-folder-plus" class="me-2"></v-icon>
            项目信息
          </v-card-title>
          
          <v-card-text class="pa-6 pt-0">
            <v-form ref="form" v-model="valid" @submit.prevent="handleSubmit">
              <!-- 基本信息 -->
              <v-row>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="formData.name"
                    :rules="nameRules"
                    label="项目名称"
                    placeholder="输入项目名称"
                    variant="outlined"
                    required
                    prepend-inner-icon="mdi-folder"
                  ></v-text-field>
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="formData.code"
                    :rules="codeRules"
                    label="项目编号"
                    placeholder="如：PRJ-2024-001"
                    variant="outlined"
                    required
                    prepend-inner-icon="mdi-identifier"
                  ></v-text-field>
                </v-col>
              </v-row>

              <v-row>
                <v-col cols="12">
                  <v-textarea
                    v-model="formData.description"
                    :rules="descriptionRules"
                    label="项目描述"
                    placeholder="详细描述项目的目标、范围和要求"
                    variant="outlined"
                    rows="4"
                    required
                  ></v-textarea>
                </v-col>
              </v-row>

              <!-- 项目设置 -->
              <v-row>
                <v-col cols="12" md="4">
                  <v-select
                    v-model="formData.status"
                    :items="statusOptions"
                    label="项目状态"
                    variant="outlined"
                    prepend-inner-icon="mdi-flag"
                  ></v-select>
                </v-col>
                
                <v-col cols="12" md="4">
                  <v-select
                    v-model="formData.priority"
                    :items="priorityOptions"
                    label="优先级"
                    variant="outlined"
                    prepend-inner-icon="mdi-priority-high"
                  ></v-select>
                </v-col>
                
                <v-col cols="12" md="4">
                  <v-text-field
                    v-model="formData.category"
                    label="项目类别"
                    placeholder="如：Web开发、移动应用"
                    variant="outlined"
                    prepend-inner-icon="mdi-tag"
                  ></v-text-field>
                </v-col>
              </v-row>

              <!-- 时间设置 -->
              <v-row>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="formData.start_date"
                    label="开始日期"
                    type="date"
                    variant="outlined"
                    required
                    prepend-inner-icon="mdi-calendar-start"
                  ></v-text-field>
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="formData.end_date"
                    label="结束日期"
                    type="date"
                    variant="outlined"
                    required
                    prepend-inner-icon="mdi-calendar-end"
                  ></v-text-field>
                </v-col>
              </v-row>

              <!-- 预算设置 -->
              <v-row>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model.number="formData.budget"
                    label="项目预算"
                    type="number"
                    min="0"
                    step="0.01"
                    variant="outlined"
                    prepend-inner-icon="mdi-currency-usd"
                    suffix="元"
                  ></v-text-field>
                </v-col>
              </v-row>

              <!-- 客户信息 -->
              <v-divider class="my-6"></v-divider>
              <h3 class="text-h6 mb-4">
                <v-icon icon="mdi-account-group" class="me-2"></v-icon>
                客户信息
              </h3>
              
              <v-row>
                <v-col cols="12" md="4">
                  <v-text-field
                    v-model="formData.client_name"
                    label="客户名称"
                    placeholder="客户公司名称"
                    variant="outlined"
                    prepend-inner-icon="mdi-domain"
                  ></v-text-field>
                </v-col>
                
                <v-col cols="12" md="4">
                  <v-text-field
                    v-model="formData.client_contact"
                    label="联系人"
                    placeholder="客户联系人姓名"
                    variant="outlined"
                    prepend-inner-icon="mdi-account"
                  ></v-text-field>
                </v-col>
                
                <v-col cols="12" md="4">
                  <v-text-field
                    v-model="formData.client_email"
                    label="联系邮箱"
                    type="email"
                    placeholder="<EMAIL>"
                    variant="outlined"
                    prepend-inner-icon="mdi-email"
                  ></v-text-field>
                </v-col>
              </v-row>

              <!-- 标签 -->
              <v-row>
                <v-col cols="12">
                  <v-text-field
                    v-model="formData.tags"
                    label="项目标签"
                    placeholder="用逗号分隔多个标签，如：紧急,重要,客户项目"
                    variant="outlined"
                    prepend-inner-icon="mdi-tag-multiple"
                  ></v-text-field>
                </v-col>
              </v-row>

              <!-- 操作按钮 -->
              <v-row class="mt-6">
                <v-col cols="12">
                  <div class="d-flex gap-4">
                    <v-btn
                      color="primary"
                      size="large"
                      type="submit"
                      :loading="loading"
                      :disabled="!valid"
                      prepend-icon="mdi-check"
                    >
                      创建项目
                    </v-btn>
                    
                    <v-btn
                      variant="outlined"
                      size="large"
                      @click="$router.go(-1)"
                      prepend-icon="mdi-cancel"
                    >
                      取消
                    </v-btn>
                  </div>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- 侧边栏提示 -->
      <v-col cols="12" lg="4">
        <v-card elevation="1" color="blue-grey-lighten-5">
          <v-card-title class="text-h6">
            <v-icon icon="mdi-lightbulb" class="me-2"></v-icon>
            创建提示
          </v-card-title>
          <v-card-text>
            <v-list density="compact">
              <v-list-item prepend-icon="mdi-check-circle">
                <v-list-item-title>项目名称要简洁明了</v-list-item-title>
              </v-list-item>
              <v-list-item prepend-icon="mdi-check-circle">
                <v-list-item-title>项目编号建议使用统一格式</v-list-item-title>
              </v-list-item>
              <v-list-item prepend-icon="mdi-check-circle">
                <v-list-item-title>详细的描述有助于团队理解</v-list-item-title>
              </v-list-item>
              <v-list-item prepend-icon="mdi-check-circle">
                <v-list-item-title>合理设置项目时间和预算</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useProjectStore } from '@/stores/project'
import { useNotificationStore } from '@/stores/notification'
import type { ProjectCreateRequest } from '@/types/project'

const router = useRouter()
const projectStore = useProjectStore()
const notificationStore = useNotificationStore()

const valid = ref(false)
const loading = ref(false)
const form = ref()

const formData = reactive<ProjectCreateRequest>({
  name: '',
  code: '',
  description: '',
  status: 'planning',
  priority: 'medium',
  start_date: new Date().toISOString().split('T')[0],
  end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  budget: 0,
  manager_id: '1',
  client_name: '',
  client_contact: '',
  client_email: '',
  tags: '',
  category: ''
})

// 验证规则
const nameRules = [
  (v: string) => !!v || '项目名称不能为空',
  (v: string) => v.length <= 200 || '项目名称不能超过200个字符'
]

const codeRules = [
  (v: string) => !!v || '项目编号不能为空',
  (v: string) => /^[A-Za-z0-9-_]+$/.test(v) || '项目编号只能包含字母、数字、连字符和下划线',
  (v: string) => v.length <= 50 || '项目编号不能超过50个字符'
]

const descriptionRules = [
  (v: string) => !!v || '项目描述不能为空',
  (v: string) => v.length <= 1000 || '项目描述不能超过1000个字符'
]

// 选项数据
const statusOptions = [
  { title: '规划中', value: 'planning' },
  { title: '进行中', value: 'in_progress' },
  { title: '已暂停', value: 'paused' },
  { title: '已完成', value: 'completed' },
  { title: '已取消', value: 'cancelled' }
]

const priorityOptions = [
  { title: '低', value: 'low' },
  { title: '中', value: 'medium' },
  { title: '高', value: 'high' },
  { title: '紧急', value: 'urgent' }
]

const handleSubmit = async () => {
  if (!form.value?.validate()) return
  
  loading.value = true
  
  try {
    // 验证日期
    if (new Date(formData.end_date) < new Date(formData.start_date)) {
      notificationStore.error('日期错误', '结束日期不能早于开始日期')
      return
    }
    
    const project = await projectStore.createProject(formData)
    
    if (project) {
      notificationStore.success('创建成功', `项目"${project.name}"已创建`)
      router.push('/projects')
    }
  } catch (error) {
    console.error('创建项目失败:', error)
    notificationStore.error('创建失败', '创建项目时发生错误，请重试')
  } finally {
    loading.value = false
  }
}
</script>
