<template>
  <v-container fluid>
    <!-- 加载状态 -->
    <div v-if="loading" class="d-flex justify-center align-center" style="min-height: 400px">
      <v-progress-circular indeterminate size="64" color="primary"></v-progress-circular>
    </div>

    <!-- 项目不存在 -->
    <div v-else-if="!project" class="text-center" style="min-height: 400px">
      <v-icon icon="mdi-folder-remove" size="64" color="grey" class="mb-4"></v-icon>
      <h2 class="text-h5 mb-2">项目不存在</h2>
      <p class="text-body-1 mb-4">您访问的项目可能已被删除或不存在</p>
      <v-btn color="primary" to="/projects">返回项目列表</v-btn>
    </div>

    <!-- 项目详情内容 -->
    <div v-else>
      <!-- 项目头部信息 -->
      <v-row class="mb-6">
        <v-col cols="12">
          <div class="d-flex justify-space-between align-center mb-4">
            <div>
              <v-btn
                icon="mdi-arrow-left"
                variant="text"
                @click="$router.go(-1)"
                class="me-2"
              ></v-btn>
              <span class="text-h4 font-weight-bold">{{ project.name }}</span>
              <v-chip
                :color="getStatusColor(project.status)"
                variant="tonal"
                size="small"
                class="ms-3"
              >
                {{ getStatusText(project.status) }}
              </v-chip>
            </div>
            <div>
              <v-btn
                color="primary"
                prepend-icon="mdi-pencil"
                variant="outlined"
                class="me-2"
                @click="editProject"
              >
                编辑项目
              </v-btn>
              <v-btn
                color="success"
                prepend-icon="mdi-plus"
                @click="createTask"
              >
                新建任务
              </v-btn>
            </div>
          </div>

          <div class="text-body-1 text-medium-emphasis mb-2">
            项目编号：{{ project.code }}
          </div>
          <div class="text-body-1" v-if="project.description">
            {{ project.description }}
          </div>
        </v-col>
      </v-row>

      <!-- 项目统计卡片 -->
      <v-row class="mb-6">
        <v-col cols="12" sm="6" md="3">
          <v-card color="blue-lighten-5">
            <v-card-text class="text-center">
              <v-icon icon="mdi-progress-clock" size="large" color="blue"></v-icon>
              <div class="text-h4 font-weight-bold mt-2">{{ project.progress }}%</div>
              <div class="text-body-2">项目进度</div>
            </v-card-text>
          </v-card>
        </v-col>

        <v-col cols="12" sm="6" md="3">
          <v-card color="green-lighten-5">
            <v-card-text class="text-center">
              <v-icon icon="mdi-format-list-checks" size="large" color="green"></v-icon>
              <div class="text-h4 font-weight-bold mt-2">{{ projectTasks.length }}</div>
              <div class="text-body-2">总任务数</div>
            </v-card-text>
          </v-card>
        </v-col>

        <v-col cols="12" sm="6" md="3">
          <v-card color="orange-lighten-5">
            <v-card-text class="text-center">
              <v-icon icon="mdi-account-group" size="large" color="orange"></v-icon>
              <div class="text-h4 font-weight-bold mt-2">{{ project.team_member_count }}</div>
              <div class="text-body-2">团队成员</div>
            </v-card-text>
          </v-card>
        </v-col>

        <v-col cols="12" sm="6" md="3">
          <v-card color="purple-lighten-5">
            <v-card-text class="text-center">
              <v-icon icon="mdi-currency-usd" size="large" color="purple"></v-icon>
              <div class="text-h4 font-weight-bold mt-2">¥{{ project.budget.toLocaleString() }}</div>
              <div class="text-body-2">项目预算</div>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <!-- 项目详细信息和任务列表 -->
      <v-row>
        <!-- 左侧：项目信息 -->
        <v-col cols="12" md="4">
          <v-card class="mb-4">
            <v-card-title class="d-flex align-center">
              <v-icon icon="mdi-information" class="me-2"></v-icon>
              项目信息
            </v-card-title>
            <v-divider></v-divider>
            <v-card-text>
              <v-list density="compact">
                <v-list-item>
                  <v-list-item-title>项目经理</v-list-item-title>
                  <v-list-item-subtitle>{{ project.manager_name }}</v-list-item-subtitle>
                </v-list-item>
                <v-list-item>
                  <v-list-item-title>优先级</v-list-item-title>
                  <v-list-item-subtitle>
                    <v-chip
                      :color="getPriorityColor(project.priority)"
                      variant="tonal"
                      size="small"
                    >
                      {{ getPriorityText(project.priority) }}
                    </v-chip>
                  </v-list-item-subtitle>
                </v-list-item>
                <v-list-item>
                  <v-list-item-title>开始日期</v-list-item-title>
                  <v-list-item-subtitle>{{ formatDate(project.start_date) }}</v-list-item-subtitle>
                </v-list-item>
                <v-list-item>
                  <v-list-item-title>结束日期</v-list-item-title>
                  <v-list-item-subtitle>{{ formatDate(project.end_date) }}</v-list-item-subtitle>
                </v-list-item>
                <v-list-item>
                  <v-list-item-title>预算</v-list-item-title>
                  <v-list-item-subtitle>¥{{ project.budget.toLocaleString() }}</v-list-item-subtitle>
                </v-list-item>
                <v-list-item>
                  <v-list-item-title>已花费</v-list-item-title>
                  <v-list-item-subtitle>¥{{ project.spent.toLocaleString() }}</v-list-item-subtitle>
                </v-list-item>
                <v-list-item v-if="project.client_name">
                  <v-list-item-title>客户</v-list-item-title>
                  <v-list-item-subtitle>{{ project.client_name }}</v-list-item-subtitle>
                </v-list-item>
                <v-list-item v-if="project.client_contact">
                  <v-list-item-title>客户联系人</v-list-item-title>
                  <v-list-item-subtitle>{{ project.client_contact }}</v-list-item-subtitle>
                </v-list-item>
              </v-list>
            </v-card-text>
          </v-card>

          <!-- 项目进度图表 -->
          <v-card>
            <v-card-title class="d-flex align-center">
              <v-icon icon="mdi-chart-donut" class="me-2"></v-icon>
              任务状态分布
            </v-card-title>
            <v-divider></v-divider>
            <v-card-text>
              <div class="text-center">
                <v-progress-circular
                  :model-value="project.progress"
                  :size="120"
                  :width="12"
                  color="primary"
                  class="mb-4"
                >
                  <span class="text-h6">{{ project.progress }}%</span>
                </v-progress-circular>
              </div>

              <v-list density="compact">
                <v-list-item>
                  <template #prepend>
                    <v-icon icon="mdi-circle" color="grey" size="small"></v-icon>
                  </template>
                  <v-list-item-title>待办任务</v-list-item-title>
                  <v-list-item-subtitle>{{ todoTasks.length }} 个</v-list-item-subtitle>
                </v-list-item>
                <v-list-item>
                  <template #prepend>
                    <v-icon icon="mdi-circle" color="orange" size="small"></v-icon>
                  </template>
                  <v-list-item-title>进行中</v-list-item-title>
                  <v-list-item-subtitle>{{ inProgressTasks.length }} 个</v-list-item-subtitle>
                </v-list-item>
                <v-list-item>
                  <template #prepend>
                    <v-icon icon="mdi-circle" color="green" size="small"></v-icon>
                  </template>
                  <v-list-item-title>已完成</v-list-item-title>
                  <v-list-item-subtitle>{{ completedTasks.length }} 个</v-list-item-subtitle>
                </v-list-item>
              </v-list>
            </v-card-text>
          </v-card>
        </v-col>

        <!-- 右侧：任务列表 -->
        <v-col cols="12" md="8">
          <v-card>
            <v-card-title class="d-flex justify-space-between align-center">
              <div class="d-flex align-center">
                <v-icon icon="mdi-format-list-bulleted" class="me-2"></v-icon>
                项目任务
              </div>
              <v-btn
                color="primary"
                prepend-icon="mdi-plus"
                size="small"
                @click="createTask"
              >
                新建任务
              </v-btn>
            </v-card-title>
            <v-divider></v-divider>

            <!-- 任务筛选 -->
            <v-card-text class="pb-0">
              <v-row>
                <v-col cols="12" sm="6" md="4">
                  <v-select
                    v-model="taskStatusFilter"
                    :items="statusOptions"
                    label="状态筛选"
                    variant="outlined"
                    density="compact"
                    clearable
                  ></v-select>
                </v-col>
                <v-col cols="12" sm="6" md="4">
                  <v-select
                    v-model="taskPriorityFilter"
                    :items="priorityOptions"
                    label="优先级筛选"
                    variant="outlined"
                    density="compact"
                    clearable
                  ></v-select>
                </v-col>
                <v-col cols="12" sm="6" md="4">
                  <v-text-field
                    v-model="taskSearchQuery"
                    label="搜索任务"
                    placeholder="输入任务名称"
                    variant="outlined"
                    density="compact"
                    prepend-inner-icon="mdi-magnify"
                    clearable
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-card-text>

            <!-- 任务列表 -->
            <v-card-text class="pa-0">
              <v-data-table
                :headers="taskHeaders"
                :items="filteredTasks"
                :loading="tasksLoading"
                item-key="id"
                class="elevation-0"
                @click:row="openTask"
              >
                <template #item.title="{ item }">
                  <div class="d-flex align-center">
                    <v-icon
                      icon="mdi-checkbox-marked-circle"
                      :color="getTaskStatusColor(item.status)"
                      class="me-2"
                      size="small"
                    ></v-icon>
                    <div>
                      <div class="font-weight-medium">{{ item.title }}</div>
                      <div class="text-caption text-medium-emphasis" v-if="item.description">
                        {{ item.description.substring(0, 50) }}{{ item.description.length > 50 ? '...' : '' }}
                      </div>
                    </div>
                  </div>
                </template>

                <template #item.status="{ item }">
                  <v-chip
                    :color="getTaskStatusColor(item.status)"
                    variant="tonal"
                    size="small"
                  >
                    {{ getTaskStatusText(item.status) }}
                  </v-chip>
                </template>

                <template #item.priority="{ item }">
                  <v-chip
                    :color="getPriorityColor(item.priority)"
                    variant="tonal"
                    size="small"
                  >
                    {{ getPriorityText(item.priority) }}
                  </v-chip>
                </template>

                <template #item.progress="{ item }">
                  <div class="d-flex align-center">
                    <v-progress-linear
                      :model-value="item.progress"
                      :color="getProgressColor(item.progress)"
                      height="6"
                      class="me-2"
                      style="min-width: 60px"
                    ></v-progress-linear>
                    <span class="text-caption">{{ item.progress }}%</span>
                  </div>
                </template>

                <template #item.due_date="{ item }">
                  <div>
                    {{ formatDate(item.due_date) }}
                  </div>
                </template>

                <template #item.actions="{ item }">
                  <v-menu>
                    <template #activator="{ props }">
                      <v-btn
                        icon="mdi-dots-vertical"
                        variant="text"
                        size="small"
                        v-bind="props"
                      ></v-btn>
                    </template>
                    <v-list>
                      <v-list-item @click="editTask(item)">
                        <v-list-item-title>编辑</v-list-item-title>
                      </v-list-item>
                      <v-list-item @click="updateTaskStatus(item)">
                        <v-list-item-title>更新状态</v-list-item-title>
                      </v-list-item>
                      <v-list-item @click="deleteTask(item)" class="text-red">
                        <v-list-item-title>删除</v-list-item-title>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </template>
              </v-data-table>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </div>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProjectStore } from '@/stores/project'
import { useTaskStore } from '@/stores/task'
import { useNotificationStore } from '@/stores/notification'
import type { Project } from '@/types/project'
import type { Task } from '@/types/task'

const route = useRoute()
const router = useRouter()
const projectStore = useProjectStore()
const taskStore = useTaskStore()
const notificationStore = useNotificationStore()

// 响应式数据
const loading = ref(true)
const tasksLoading = ref(false)
const project = ref<Project | null>(null)
const taskStatusFilter = ref('')
const taskPriorityFilter = ref('')
const taskSearchQuery = ref('')

// 计算属性
const projectTasks = computed(() =>
  taskStore.tasks.filter(task => task.project_id === route.params.id)
)

const todoTasks = computed(() =>
  projectTasks.value.filter(task => task.status === 'todo')
)

const inProgressTasks = computed(() =>
  projectTasks.value.filter(task => task.status === 'in_progress')
)

const completedTasks = computed(() =>
  projectTasks.value.filter(task => task.status === 'completed')
)

const filteredTasks = computed(() => {
  let result = projectTasks.value

  if (taskStatusFilter.value) {
    result = result.filter(task => task.status === taskStatusFilter.value)
  }

  if (taskPriorityFilter.value) {
    result = result.filter(task => task.priority === taskPriorityFilter.value)
  }

  if (taskSearchQuery.value) {
    const query = taskSearchQuery.value.toLowerCase()
    result = result.filter(task =>
      task.title.toLowerCase().includes(query) ||
      task.description?.toLowerCase().includes(query)
    )
  }

  return result
})

// 选项数据
const statusOptions = [
  { title: '待办', value: 'todo' },
  { title: '进行中', value: 'in_progress' },
  { title: '已完成', value: 'completed' },
  { title: '已取消', value: 'cancelled' }
]

const priorityOptions = [
  { title: '低', value: 'low' },
  { title: '中', value: 'medium' },
  { title: '高', value: 'high' },
  { title: '紧急', value: 'urgent' }
]

// 表格配置
const taskHeaders = [
  { title: '任务名称', key: 'title', sortable: true },
  { title: '状态', key: 'status', sortable: true },
  { title: '优先级', key: 'priority', sortable: true },
  { title: '进度', key: 'progress', sortable: true },
  { title: '截止日期', key: 'due_date', sortable: true },
  { title: '操作', key: 'actions', sortable: false }
]

// 方法
const loadProjectData = async () => {
  loading.value = true
  try {
    const projectId = route.params.id as string

    // 加载项目信息
    const foundProject = projectStore.projects.find(p => p.id === projectId)
    if (foundProject) {
      project.value = foundProject
    } else {
      // 如果项目列表为空，先加载项目列表
      await projectStore.fetchProjects()
      project.value = projectStore.projects.find(p => p.id === projectId) || null
    }

    // 加载任务列表
    tasksLoading.value = true
    await taskStore.fetchTasks()
    tasksLoading.value = false

  } catch (error) {
    notificationStore.error('加载失败', '加载项目数据失败')
  } finally {
    loading.value = false
  }
}

const editProject = () => {
  router.push(`/projects/${project.value?.id}/edit`)
}

const createTask = () => {
  router.push(`/tasks/create?project=${project.value?.id}`)
}

const openTask = (event: any, { item }: { item: Task }) => {
  router.push(`/projects/${project.value?.id}/tasks/${item.id}`)
}

const editTask = (task: Task) => {
  router.push(`/tasks/${task.id}/edit`)
}

const updateTaskStatus = async (task: Task) => {
  const newStatus = task.status === 'completed' ? 'in_progress' : 'completed'
  try {
    await taskStore.updateTask(task.id, { status: newStatus })
    notificationStore.success('更新成功', '任务状态已更新')
  } catch (error) {
    notificationStore.error('更新失败', '更新任务状态失败')
  }
}

const deleteTask = async (task: Task) => {
  if (confirm(`确定要删除任务"${task.title}"吗？`)) {
    try {
      await taskStore.deleteTask(task.id)
      notificationStore.success('删除成功', '任务已删除')
    } catch (error) {
      notificationStore.error('删除失败', '删除任务失败')
    }
  }
}

// 工具函数
const getStatusColor = (status: string) => {
  switch (status) {
    case 'planning': return 'blue'
    case 'in_progress': return 'orange'
    case 'completed': return 'green'
    case 'paused': return 'grey'
    case 'cancelled': return 'red'
    default: return 'grey'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'planning': return '规划中'
    case 'in_progress': return '进行中'
    case 'completed': return '已完成'
    case 'paused': return '已暂停'
    case 'cancelled': return '已取消'
    default: return '未知'
  }
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'low': return 'green'
    case 'medium': return 'orange'
    case 'high': return 'red'
    case 'urgent': return 'purple'
    default: return 'grey'
  }
}

const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'low': return '低'
    case 'medium': return '中'
    case 'high': return '高'
    case 'urgent': return '紧急'
    default: return '未知'
  }
}

const getTaskStatusColor = (status: string) => {
  switch (status) {
    case 'todo': return 'grey'
    case 'in_progress': return 'orange'
    case 'completed': return 'green'
    case 'cancelled': return 'red'
    default: return 'grey'
  }
}

const getTaskStatusText = (status: string) => {
  switch (status) {
    case 'todo': return '待办'
    case 'in_progress': return '进行中'
    case 'completed': return '已完成'
    case 'cancelled': return '已取消'
    default: return '未知'
  }
}

const getProgressColor = (progress: number) => {
  if (progress >= 80) return 'green'
  if (progress >= 50) return 'orange'
  return 'blue'
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

onMounted(async () => {
  await loadProjectData()
})
</script>

<style scoped>
.project-detail {
  padding: 24px;
}
</style>
