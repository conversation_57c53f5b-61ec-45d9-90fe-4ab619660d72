<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <div class="d-flex align-center mb-6">
          <v-btn
            icon="mdi-arrow-left"
            variant="text"
            @click="$router.go(-1)"
            class="me-4"
          ></v-btn>
          <div>
            <h1 class="text-h4 font-weight-bold">创建新任务</h1>
            <p class="text-body-1 text-medium-emphasis mt-1">
              为项目添加新的任务，明确责任和时间安排
            </p>
          </div>
        </div>
      </v-col>
    </v-row>

    <v-row>
      <v-col cols="12" lg="8">
        <v-card elevation="2">
          <v-card-title class="text-h6 pa-6 pb-4">
            <v-icon icon="mdi-plus-circle" class="me-2"></v-icon>
            任务信息
          </v-card-title>
          
          <v-card-text class="pa-6 pt-0">
            <v-form ref="form" v-model="valid" @submit.prevent="handleSubmit">
              <!-- 基本信息 -->
              <v-row>
                <v-col cols="12">
                  <v-text-field
                    v-model="formData.title"
                    :rules="titleRules"
                    label="任务标题"
                    placeholder="输入任务标题"
                    variant="outlined"
                    required
                    prepend-inner-icon="mdi-format-title"
                  ></v-text-field>
                </v-col>
              </v-row>

              <v-row>
                <v-col cols="12">
                  <v-textarea
                    v-model="formData.description"
                    label="任务描述"
                    placeholder="详细描述任务的内容、要求和验收标准"
                    variant="outlined"
                    rows="4"
                  ></v-textarea>
                </v-col>
              </v-row>

              <!-- 项目和分配 -->
              <v-row>
                <v-col cols="12" md="6">
                  <v-select
                    v-model="formData.project_id"
                    :items="projectOptions"
                    :rules="projectRules"
                    label="所属项目"
                    variant="outlined"
                    required
                    prepend-inner-icon="mdi-folder"
                  ></v-select>
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-select
                    v-model="formData.assignee_id"
                    :items="userOptions"
                    label="负责人"
                    variant="outlined"
                    clearable
                    prepend-inner-icon="mdi-account"
                  ></v-select>
                </v-col>
              </v-row>

              <!-- 状态和优先级 -->
              <v-row>
                <v-col cols="12" md="4">
                  <v-select
                    v-model="formData.status"
                    :items="statusOptions"
                    label="任务状态"
                    variant="outlined"
                    prepend-inner-icon="mdi-flag"
                  ></v-select>
                </v-col>
                
                <v-col cols="12" md="4">
                  <v-select
                    v-model="formData.priority"
                    :items="priorityOptions"
                    label="优先级"
                    variant="outlined"
                    prepend-inner-icon="mdi-priority-high"
                  ></v-select>
                </v-col>
                
                <v-col cols="12" md="4">
                  <v-select
                    v-model="formData.type"
                    :items="typeOptions"
                    label="任务类型"
                    variant="outlined"
                    prepend-inner-icon="mdi-shape"
                  ></v-select>
                </v-col>
              </v-row>

              <!-- 时间设置 -->
              <v-row>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="formData.start_date"
                    label="开始日期"
                    type="date"
                    variant="outlined"
                    prepend-inner-icon="mdi-calendar-start"
                  ></v-text-field>
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="formData.due_date"
                    label="截止日期"
                    type="date"
                    variant="outlined"
                    prepend-inner-icon="mdi-calendar-end"
                  ></v-text-field>
                </v-col>
              </v-row>

              <!-- 工时设置 -->
              <v-row>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model.number="formData.estimated_hours"
                    label="预估工时"
                    type="number"
                    min="0"
                    step="0.5"
                    variant="outlined"
                    prepend-inner-icon="mdi-clock-outline"
                    suffix="小时"
                  ></v-text-field>
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model.number="formData.duration_days"
                    label="持续天数"
                    type="number"
                    min="1"
                    variant="outlined"
                    prepend-inner-icon="mdi-calendar-range"
                    suffix="天"
                  ></v-text-field>
                </v-col>
              </v-row>

              <!-- 标签 -->
              <v-row>
                <v-col cols="12">
                  <v-combobox
                    v-model="formData.tags"
                    label="任务标签"
                    placeholder="输入标签并按回车添加"
                    variant="outlined"
                    multiple
                    chips
                    prepend-inner-icon="mdi-tag-multiple"
                  ></v-combobox>
                </v-col>
              </v-row>

              <!-- 操作按钮 -->
              <v-row class="mt-6">
                <v-col cols="12">
                  <div class="d-flex gap-4">
                    <v-btn
                      color="primary"
                      size="large"
                      type="submit"
                      :loading="loading"
                      :disabled="!valid"
                      prepend-icon="mdi-check"
                    >
                      创建任务
                    </v-btn>
                    
                    <v-btn
                      variant="outlined"
                      size="large"
                      @click="$router.go(-1)"
                      prepend-icon="mdi-cancel"
                    >
                      取消
                    </v-btn>
                  </div>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- 侧边栏提示 -->
      <v-col cols="12" lg="4">
        <v-card elevation="1" color="green-lighten-5">
          <v-card-title class="text-h6">
            <v-icon icon="mdi-lightbulb" class="me-2"></v-icon>
            任务创建提示
          </v-card-title>
          <v-card-text>
            <v-list density="compact">
              <v-list-item prepend-icon="mdi-check-circle">
                <v-list-item-title>任务标题要简洁明了</v-list-item-title>
              </v-list-item>
              <v-list-item prepend-icon="mdi-check-circle">
                <v-list-item-title>详细描述有助于执行</v-list-item-title>
              </v-list-item>
              <v-list-item prepend-icon="mdi-check-circle">
                <v-list-item-title>合理设置时间和工时</v-list-item-title>
              </v-list-item>
              <v-list-item prepend-icon="mdi-check-circle">
                <v-list-item-title>明确负责人和优先级</v-list-item-title>
              </v-list-item>
              <v-list-item prepend-icon="mdi-check-circle">
                <v-list-item-title>使用标签便于分类</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>

        <!-- 快速模板 -->
        <v-card elevation="1" class="mt-4">
          <v-card-title class="text-h6">
            <v-icon icon="mdi-lightning-bolt" class="me-2"></v-icon>
            快速模板
          </v-card-title>
          <v-card-text>
            <v-btn
              v-for="template in quickTemplates"
              :key="template.name"
              variant="outlined"
              size="small"
              class="ma-1"
              @click="applyTemplate(template)"
            >
              {{ template.name }}
            </v-btn>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useTaskStore } from '@/stores/task'
import { useProjectStore } from '@/stores/project'
import { useNotificationStore } from '@/stores/notification'
import type { TaskCreateRequest } from '@/types/task'

const router = useRouter()
const taskStore = useTaskStore()
const projectStore = useProjectStore()
const notificationStore = useNotificationStore()

const valid = ref(false)
const loading = ref(false)
const form = ref()

const formData = reactive<TaskCreateRequest>({
  title: '',
  description: '',
  status: 'todo',
  priority: 'medium',
  type: 'task',
  project_id: '',
  assignee_id: '',
  start_date: '',
  due_date: '',
  estimated_hours: undefined,
  duration_days: undefined,
  tags: []
})

// 验证规则
const titleRules = [
  (v: string) => !!v || '任务标题不能为空',
  (v: string) => v.length <= 200 || '任务标题不能超过200个字符'
]

const projectRules = [
  (v: string) => !!v || '请选择所属项目'
]

// 选项数据
const statusOptions = [
  { title: '待办', value: 'todo' },
  { title: '进行中', value: 'in_progress' },
  { title: '已完成', value: 'completed' },
  { title: '已取消', value: 'cancelled' },
  { title: '暂停', value: 'on_hold' }
]

const priorityOptions = [
  { title: '低', value: 'low' },
  { title: '中', value: 'medium' },
  { title: '高', value: 'high' },
  { title: '紧急', value: 'urgent' }
]

const typeOptions = [
  { title: '任务', value: 'task' },
  { title: '里程碑', value: 'milestone' },
  { title: '总结', value: 'summary' }
]

const projectOptions = ref([
  { title: '项目管理系统', value: 'project-1' },
  { title: '移动应用开发', value: 'project-2' }
])

const userOptions = ref([
  { title: '管理员', value: '1' },
  { title: '开发者A', value: '2' },
  { title: '开发者B', value: '3' }
])

// 快速模板
const quickTemplates = [
  {
    name: '开发任务',
    data: {
      type: 'task',
      priority: 'medium',
      estimated_hours: 8,
      tags: ['开发']
    }
  },
  {
    name: '测试任务',
    data: {
      type: 'task',
      priority: 'medium',
      estimated_hours: 4,
      tags: ['测试']
    }
  },
  {
    name: '设计任务',
    data: {
      type: 'task',
      priority: 'high',
      estimated_hours: 6,
      tags: ['设计']
    }
  },
  {
    name: '里程碑',
    data: {
      type: 'milestone',
      priority: 'high',
      tags: ['里程碑']
    }
  }
]

const applyTemplate = (template: any) => {
  Object.assign(formData, template.data)
}

const handleSubmit = async () => {
  if (!form.value?.validate()) return
  
  loading.value = true
  
  try {
    // 验证日期
    if (formData.start_date && formData.due_date && 
        new Date(formData.due_date) < new Date(formData.start_date)) {
      notificationStore.error('日期错误', '截止日期不能早于开始日期')
      return
    }
    
    const task = await taskStore.createTask(formData)
    
    if (task) {
      notificationStore.success('创建成功', `任务"${task.title}"已创建`)
      router.push('/tasks')
    }
  } catch (error) {
    console.error('创建任务失败:', error)
    notificationStore.error('创建失败', '创建任务时发生错误，请重试')
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  // 获取项目列表
  await projectStore.fetchProjects()
})
</script>
